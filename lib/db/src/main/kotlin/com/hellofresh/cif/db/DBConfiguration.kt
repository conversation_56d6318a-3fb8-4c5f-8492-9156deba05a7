package com.hellofresh.cif.db

import com.hellofresh.cif.checks.HealthChecks
import com.hellofresh.cif.checks.StartUpChecks
import com.hellofresh.cif.config.ConfigurationLoader.getStringOrFail
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import io.micrometer.core.instrument.MeterRegistry
import java.util.concurrent.Executors
import java.util.concurrent.ThreadFactory
import java.util.concurrent.atomic.AtomicLong
import org.jooq.SQLDialect.POSTGRES
import org.jooq.conf.Settings
import org.jooq.impl.DefaultConfiguration
import org.postgresql.Driver

private const val CLIENT_DB_TIMEOUT_IN_MILLISECONDS = 25000

object DBConfiguration {

    fun getMasterDatabaseConfig() =
        DatabaseConfig(
            configName = "master",
            hostName = getStringOrFail("HF_INVENTORY_DB_MASTER_HOST"),
            userName = getStringOrFail("HF_INVENTORY_DB_USERNAME"),
            password = getStringOrFail("HF_INVENTORY_DB_PASSWORD"),
        )

    fun getReadonlyDatabaseConfig() =
        DatabaseConfig(
            configName = "readonly",
            hostName = getStringOrFail("HF_INVENTORY_DB_HOST"),
            userName = getStringOrFail("HF_INVENTORY_READONLY_DB_USERNAME"),
            password = getStringOrFail("HF_INVENTORY_READONLY_DB_PASSWORD"),
        )

    fun jooqReadOnlyDslContext(
        parallelism: Int,
        meterRegistry: MeterRegistry,
    ) = jooqDslContext(getReadonlyDatabaseConfig(), parallelism, meterRegistry)

    fun jooqMasterDslContext(
        parallelism: Int,
        meterRegistry: MeterRegistry,
    ) = jooqDslContext(getMasterDatabaseConfig(), parallelism, meterRegistry)

    fun jooqDslContext(
        databaseConfig: DatabaseConfig,
        parallelism: Int,
        meterRegistry: MeterRegistry
    ): MetricsDSLContext {
        val dataSource = shutdownNeeded {
            HikariDataSource(
                HikariConfig().apply {
                    driverClassName = Driver::class.qualifiedName
                    metricRegistry = meterRegistry
                    jdbcUrl = "jdbc:postgresql://${databaseConfig.hostName}/inventory"
                    username = databaseConfig.userName
                    password = databaseConfig.password
                    isReadOnly = false
                    maximumPoolSize = parallelism
                    validate()
                },
            )
        }

        return MetricsDSLContext(
            databaseConfig.configName,
            DefaultConfiguration().apply
                {
                    setSQLDialect(POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(createThreadPool(parallelism, databaseConfig))
                    setSettings(Settings().withQueryTimeout(CLIENT_DB_TIMEOUT_IN_MILLISECONDS))
                },
            meterRegistry,
        ).also {
            StartUpChecks.add(it)
            HealthChecks.add(it)
        }
    }

    private fun createThreadPool(
        parallelism: Int,
        databaseConfig: DatabaseConfig,
    ) =
        shutdownNeeded {
            Executors.newFixedThreadPool(
                parallelism,
                object : ThreadFactory {
                    private val count: AtomicLong = AtomicLong(0)
                    override fun newThread(r: Runnable): Thread =
                        Executors.defaultThreadFactory().newThread(r)
                            .also {
                                it.name = "database-query-${databaseConfig.configName}-thread-${count.getAndIncrement()}"
                            }
                },
            )
        }
}

data class DatabaseConfig(
    val configName: String,
    val hostName: String,
    val userName: String,
    val password: String
)
