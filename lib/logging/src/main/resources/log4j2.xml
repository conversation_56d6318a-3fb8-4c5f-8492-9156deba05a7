<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="error" strict="true">
    <Appenders>
        <Console name="HelloFresh">
            <JsonTemplateLayout
                eventDelimiter="&#xA;"
                eventTemplateUri="classpath:log4j2.hellofresh.template.json"
                locationInfoEnabled="${sys:logging.location-info:-false}"
                stackTraceEnabled="${sys:logging.stack-trace.enabled:-true}"
            />
        </Console>
        <Console name="Human">
            <PatternLayout
                charset="UTF-8"
                pattern="[%date{ISO8601}{UTC}Z] %highlight{[%level]} [%thread] [%logger] %notEmpty{~%marker~ }%message %mdc %location%n"
                alwaysWriteExceptions="${sys:logging.stack-trace.enabled:-true}"
            />
        </Console>
        <Slack
            name="Slack"
            channel="${env:SLACK_ALERT_CHANNEL}"
            webhookUrl="${env:SLACK_WEBHOOK}"
            username="ErrorLogNotifier"
        >
            <!-- We use a low rate as it is per second, everything is logged in Loki so nothing is lost. -->
            <BurstFilter level="ALL" rate="5"/>
            <PatternLayout
                charset="UTF-8"
                pattern="tier: ${env:HF_TIER}%ntime: %date{ISO8601}{UTC}Z%nseverity: %level%nthread: %thread%nlogger: %logger%nmarker: %marker%nmessage: message%nmdc: %mdc%nlocation: %location%nexception: %exception"
            />
        </Slack>
    </Appenders>
    <Loggers>
        <Logger name="org.jooq.tools.LoggerListener" level="info">
            <AppenderRef ref="Human"/>
        </Logger>
        <Root level="${sys:logging.level.root:-info}" includeLocation="${sys:logging.location-info:-false}">
            <AppenderRef ref="${sys:logging.layout:-Human}"/>
            <AppenderRef ref="Slack" level="${sys:logging.slack.level:-OFF}"/>
            <!-- https://issues.apache.org/jira/browse/KAFKA-7509 -->
            <RegexFilter regex="^The configuration '[^']+' was supplied but isn't a known config\.$" onMatch="DENY" onMismatch="NEUTRAL"/>
        </Root>
    </Loggers>
</Configuration>
