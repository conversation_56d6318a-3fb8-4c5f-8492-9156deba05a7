package com.hellofresh.cif.featureflags

import com.statsig.sdk.RulesUpdatedCallback
import com.statsig.sdk.Statsig
import com.statsig.sdk.StatsigOptions
import com.statsig.sdk.StatsigUser
import kotlinx.coroutines.runBlocking
import org.apache.logging.log4j.kotlin.Logging

object StatsigFactory {
    fun build(
        shutDownHook: (AutoCloseable) -> Unit,
        sdkKey: String,
        userId: String,
        isOffline: Boolean = false,
        hfTier: String = "local"
    ): StatsigFeatureFlagClient {
        shutDownHook.also { Statsig.shutdown() }
        return StatsigFeatureFlagClientImpl(StatsigInitData(sdkKey, isOffline, StatsigTier.from(hfTier), userId))
    }
}

interface StatsigFeatureFlagClient {
    fun isEnabledFor(featureFlag: FeatureFlag, default: Boolean? = null): Boolean
}

class StatsigFeatureFlagClientImpl internal constructor(initData: StatsigInitData) : StatsigFeatureFlagClient {

    private val statsigUser: StatsigUser

    init {
        // Every 10s statsig polls and refresh the feature flags locally
        val options = StatsigOptions(customLogger = StatsigLogger).apply {
            localMode = initData.isOffline
            setTier(initData.tier.name.lowercase())
            rulesUpdatedCallback = RulesUpdatedCallback {
                logger.info("Features flags rules updates to $it .")
            }
        }
        statsigUser = StatsigUser(userID = initData.userId)
        runBlocking { Statsig.initialize(initData.sdkKey, options) }
    }

    override fun isEnabledFor(featureFlag: FeatureFlag, default: Boolean?): Boolean =
        if (Statsig.isInitialized()) {
            runBlocking {
                Statsig.getFeatureGate(
                    gateName = featureFlag.name,
                    user = featureFlag.createParam(<EMAIL>),
                ).value
            }
        } else {
            default ?: error("Statsig is not yet initialized")
        }

    companion object : Logging
}

data class StatsigInitData(val sdkKey: String, val isOffline: Boolean = false, val tier: StatsigTier, val userId: String)

enum class StatsigTier {
    DEVELOPMENT, STAGING, PRODUCTION;

    companion object {
        fun from(hfTier: String) = when (hfTier.lowercase()) {
            "local" -> DEVELOPMENT
            "staging" -> STAGING
            "live" -> PRODUCTION
            else -> error("Unknown HF_TIER: $hfTier")
        }
    }
}
