package com.hellofresh.cif.featureflags

import com.hellofresh.cif.featureflags.Context.COUNTRY
import com.statsig.sdk.StatsigUser

/**
 * Calculates Unusable inventory without using acl rules
 */
const val NO_ACL_UNUSABLE_INVENTORY = "no-acl-unusable-inventory"

/**
 * Enable by dc code live rules for automated distribution center and hide staging/storage quantities in stock overview page
 */
const val AUTOMATED_DC_LIVE_RULES_FLAG_KEY = "automated-dc-live-rules"

/**
 * Enable Live 2.0 rules
 */
const val LIVE_2_RULES_FLAG_KEY = "live-2-rules"

/**
 * Stop publishing demands for clients.
 */
const val STOP_PUBLISHING_FORECAST_TO_CLIENTS = "stop-publishing-forecast-to-clients"

/**
 * Enable Weekly SQR On Stock Overview Page
 */
const val ENABLE_WEEKLY_SQR_ON_STOCK_OVERVIEW = "enable-weekly-sqr-on-stock-overview"

/**
 * Apply the inventory movement activity and process the unusable stock.
 */
const val APPLY_UNUSABLE_MOVEMENTS_FLAG_KEY = "apply-unusable-movements"

/**
 *  Blacklist purchase order suppliers
 */
const val BLOCKED_SUPPLIER_FLAG_KEY = "blocked-suppliers"

/**
 * Flags if actual consumption can be used in domain to calculate actual demand
 */
const val USABLE_ACTUAL_CONSUMPTION_DEMAND_DOMAIN = "usable-actual-consumption-demand-domain"

/**
 * Flags if triggered cleardown time could be considered for inventory all snapshot data
 */
const val TRIGGERED_CLEARDOWN_TIME_FOR_INVENTORY_SNAPSHOT = "triggered-cleardown-time-for-inventory-snapshot"

/**
 * Flags to include net needs and safety stock in FE, CSV
 */
const val ENABLE_NET_NEEDS = "enable-net-needs"

/**
 * Flags to disable expiry unusable rules
 */
const val DISABLE_EXPIRY_UNUSABLE = "disable-expiry-unusable"

/**
 * Flag to Enable the Safety Stock Formula based on Risk Multiplier data
 */
const val SAFETY_STOCK_MULTIPLIER_FORMULA = "safety-stock-multiplier-formula"

/**
 * Flag to Enable to process the transfer orders for specific markets
 */
const val PROCESS_TRANSFER_ORDERS = "process-transfer-orders"

enum class Context(val key: String) {
    MARKET("market"),
    DC("dc_code"),
    COUNTRY("country"),
    CATEGORY("category"),
    PACKAGING("packaging"),
    SUPPLIER_ID("supplier_id"), ;

    fun data(value: String) = ContextData(this, value)
}

sealed interface FeatureFlag {
    val name: String
    val contextData: Set<ContextData>

    fun createParam(statsigUser: StatsigUser): StatsigUser {
        var user = statsigUser
        val countryData: ContextData? = contextData.firstOrNull { it.context == COUNTRY }
        if (countryData != null) {
            user = statsigUser.copy().apply { country = countryData.value }
        }
        return user.copy().apply {
            custom = contextData
                .filter { it.context != COUNTRY }
                .associate { it.context.key to it.value }
        }
    }

    data class DisableExpiryUnusable(override val contextData: Set<ContextData>) : FeatureFlag {
        override val name: String = DISABLE_EXPIRY_UNUSABLE
    }

    data class StockUpdate(
        override val contextData: Set<ContextData>,
    ) : FeatureFlag {
        override val name: String = "squad_inventory_stock_update_feature"
    }

    data class TriggeredCleardownTimeForInventorySnapshot(override val contextData: Set<ContextData>) : FeatureFlag {
        override val name: String = TRIGGERED_CLEARDOWN_TIME_FOR_INVENTORY_SNAPSHOT
    }

    data class UsableActualConsumptionDemandDomain(override val contextData: Set<ContextData>) : FeatureFlag {
        override val name: String = USABLE_ACTUAL_CONSUMPTION_DEMAND_DOMAIN
    }

    data class BlockedSupplier(override val contextData: Set<ContextData>) : FeatureFlag {
        override val name: String = BLOCKED_SUPPLIER_FLAG_KEY
    }

    data class ApplyUnusableMovementsStock(override val contextData: Set<ContextData>) : FeatureFlag {
        override val name: String = APPLY_UNUSABLE_MOVEMENTS_FLAG_KEY
    }

    data class Live2Rule(override val contextData: Set<ContextData>) : FeatureFlag {
        override val name: String = LIVE_2_RULES_FLAG_KEY
    }

    data class AutomatedDcLiveRules(override val contextData: Set<ContextData>) : FeatureFlag {
        override val name: String = AUTOMATED_DC_LIVE_RULES_FLAG_KEY
    }

    data class NoAclUnusableInventory(override val contextData: Set<ContextData>) : FeatureFlag {
        override val name: String = NO_ACL_UNUSABLE_INVENTORY
    }

    data class NetNeeds(override val contextData: Set<ContextData>) : FeatureFlag {
        override val name: String = ENABLE_NET_NEEDS
    }

    data class StopPublishingDemandsForClients(override val contextData: Set<ContextData>) : FeatureFlag {
        override val name: String = STOP_PUBLISHING_FORECAST_TO_CLIENTS
    }

    data class EnableWeeklySQROnStockOverview(override val contextData: Set<ContextData>) : FeatureFlag {
        override val name: String = ENABLE_WEEKLY_SQR_ON_STOCK_OVERVIEW
    }

    data class SafetyStockMultiplierFormula(override val contextData: Set<ContextData>) : FeatureFlag {
        override val name: String = SAFETY_STOCK_MULTIPLIER_FORMULA
    }

    data class ProcessTransferOrders(override val contextData: Set<ContextData>) : FeatureFlag {
        override val name: String = PROCESS_TRANSFER_ORDERS
    }
}

data class ContextData(val context: Context, val value: String)
