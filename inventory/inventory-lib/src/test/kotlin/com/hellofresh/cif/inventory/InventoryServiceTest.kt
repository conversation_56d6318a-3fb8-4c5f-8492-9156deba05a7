package com.hellofresh.cif.inventory

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.featureflags.Context.DC
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.TriggeredCleardownTimeForInventorySnapshot
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.inventory.InventoryRepositoryImpl.CleardownDetail
import com.hellofresh.cif.inventory.InventoryService.Companion.calculationInputDcDateRange
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.DateTimeRange
import com.hellofresh.inventory.models.CleardownData
import com.hellofresh.inventory.models.CleardownMode
import com.hellofresh.inventory.models.CleardownMode.SCHEDULED
import com.hellofresh.inventory.models.CleardownMode.TRIGGERED
import com.hellofresh.inventory.models.InventoryAdjustment
import com.hellofresh.inventory.models.InventoryMovement
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.SkuInventory
import com.hellofresh.inventory.models.default
import com.hellofresh.inventory.models.inbounds.InventorySnapshotInbounds.Companion.calculateInboundTimeRange
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneOffset.UTC
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

internal class InventoryServiceTest {
    private val inventoryRepository = mockk<InventoryRepository>()
    private val liveInventoryRepository = mockk<LiveInventoryRepository>(relaxed = true)
    private val inventoryActivityRepository = mockk<InventoryActivityRepository>(relaxed = true)
    private val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(emptySet())
    private val inventoryService = InventoryService(
        inventoryRepository,
        liveInventoryRepository,
        inventoryActivityRepository,
        statsigFeatureFlagClient,
    )

    @AfterEach
    fun afterEach() {
        unmockkStatic(LocalDate::class)
    }

    @Test
    fun `should return scheduled and triggered cleardown inventory snapshots`() {
        statsigFeatureFlagClient.fixtures = setOf(TriggeredCleardownTimeForInventorySnapshot(setOf(ContextData(DC, "BV"))))

        val triggeredCleardownTime = ZonedDateTime.now().truncatedTo(ChronoUnit.MINUTES)
        val dcConfig1 = DistributionCenterConfiguration.default(
            "D1",
        ).copy(cleardown = LocalDateTime.now().minusDays(1).dayOfWeek)
        val dcConfig2 = DistributionCenterConfiguration.default(
            "BV",
        ).copy(cleardown = triggeredCleardownTime.minusDays(2).dayOfWeek)
        val dcConfig3 = DistributionCenterConfiguration.default(
            "XX",
        ).copy(hasCleardown = false)

        val inventory1 = InventorySnapshot.Companion.default().copy(dcCode = dcConfig1.dcCode)
        val scheduledCleardownTime = CleardownDetail(
            dcConfig1.dcCode,
            dcConfig1.cleardown,
            dcConfig1.zoneId,
            null,
            LocalTime.MIDNIGHT,
        ).scheduledDateTime

        val cleardownInventory1 =
            CleardownData(
                dcConfig1.dcCode,
                scheduledCleardownTime.minusMinutes(10).toLocalDateTime(),
                SCHEDULED,
                inventory1,
            )
        val inventory2 = InventorySnapshot.Companion.default().copy(dcCode = dcConfig2.dcCode)
        val cleardownInventory2 =
            CleardownData(
                dcConfig2.dcCode,
                triggeredCleardownTime.minusMinutes(15).toLocalDateTime(),
                TRIGGERED,
                inventory2,
            )
        val inventory3 = InventorySnapshot.Companion.default().copy(dcCode = dcConfig3.dcCode)

        val scheduledCleardown = CleardownDetail(
            dcConfig1.dcCode,
            dcConfig1.cleardown,
            dcConfig1.zoneId,
            null,
            scheduledCleardownTime.toLocalTime(),
        )
        val triggeredCleardown = CleardownDetail(
            dcConfig2.dcCode,
            dcConfig2.cleardown,
            dcConfig2.zoneId,
            triggeredCleardownTime,
            scheduledCleardownTime.toLocalTime(),
            cleardownInventory2.snapshot?.snapshotId,
        )

        coEvery {
            inventoryRepository.getCleardownDetails(setOf(dcConfig1.dcCode, dcConfig2.dcCode, dcConfig3.dcCode))
        } returns listOf(scheduledCleardown, triggeredCleardown)
        coEvery {
            inventoryRepository.fetchBy(
                setOf(dcConfig1.dcCode, dcConfig2.dcCode, dcConfig3.dcCode),
                calculationInputDcDateRange(dcConfig1, null),
            )
        } returns listOf(inventory1, inventory2, inventory3)

        coEvery {
            inventoryRepository.fetchInventory(
                listOf(
                    ScheduledCleardown(dcConfig1.dcCode, scheduledCleardownTime),
                    TriggeredCleardown(
                        dcConfig2.dcCode,
                        triggeredCleardown.triggeredCleardownTime!!,
                        cleardownInventory2.snapshot?.snapshotId,
                    ),
                ),
            )
        } returns listOf(
            InventorySnapshot(
                cleardownInventory1.dcCode,
                cleardownInventory1.snapshot?.snapshotId!!,
                cleardownInventory1.snapshot?.snapshotTime!!,
                listOf(
                    SkuInventory(
                        cleardownInventory1.snapshot?.skus?.first()?.skuId!!,
                        cleardownInventory1.snapshot?.skus?.flatMap { it.inventory }!!,
                    ),
                ),
            ),
            InventorySnapshot(
                cleardownInventory2.dcCode,
                cleardownInventory2.snapshot?.snapshotId!!,
                cleardownInventory2.snapshot?.snapshotTime!!,
                listOf(
                    SkuInventory(
                        cleardownInventory2.snapshot?.skus?.first()?.skuId!!,
                        cleardownInventory2.snapshot?.skus?.flatMap { it.inventory }!!,
                    ),
                ),
            ),
        )

        runBlocking {
            val inventorySnapshots = inventoryService.fetchInventorySnapshots(
                setOf(dcConfig1, dcConfig2, dcConfig3),
            )
            assertEquals(listOf(inventory1, inventory2, inventory3), inventorySnapshots.inventoryList)
            assertEquals(
                scheduledCleardownTime.toLocalDateTime(),
                inventorySnapshots.cleardownData.first { it.dcCode == dcConfig1.dcCode }.cleardownTime,
            )
            assertEquals(
                scheduledCleardownTime.toLocalTime(),
                inventorySnapshots.cleardownData.first { it.dcCode == dcConfig1.dcCode }.cleardownTime.toLocalTime(),
            )
            assertEquals(
                cleardownInventory1.snapshot?.snapshotId,
                inventorySnapshots.cleardownData.first {
                    it.dcCode == dcConfig1.dcCode
                }.snapshot?.snapshotId,
            )
            assertEquals(
                SCHEDULED,
                inventorySnapshots.cleardownData.first {
                    it.dcCode == dcConfig1.dcCode
                }.cleardownMode,
            )
            assertEquals(
                cleardownInventory1.snapshot?.snapshotTime,
                inventorySnapshots.cleardownData.first { it.dcCode == dcConfig1.dcCode }.snapshot?.snapshotTime,
            )
            assertEquals(
                inventory1.skus.flatMap { it.inventory }.toSet(),
                inventorySnapshots.cleardownData.first {
                    it.dcCode == dcConfig1.dcCode
                }.snapshot?.skus?.flatMap { it.inventory }?.toSet(),
            )

            assertEquals(
                triggeredCleardown.triggeredCleardownTime?.toLocalDateTime()?.truncatedTo(ChronoUnit.MINUTES),
                inventorySnapshots.cleardownData.first { it.dcCode == dcConfig2.dcCode }.cleardownTime,
            )
            assertEquals(
                cleardownInventory2.snapshot?.snapshotId,
                inventorySnapshots.cleardownData.first {
                    it.dcCode == dcConfig2.dcCode
                }.snapshot?.snapshotId,
            )
            assertEquals(
                TRIGGERED,
                inventorySnapshots.cleardownData.first {
                    it.dcCode == dcConfig2.dcCode
                }.cleardownMode,
            )
            assertEquals(
                cleardownInventory2.snapshot?.snapshotTime,
                inventorySnapshots.cleardownData.first { it.dcCode == dcConfig2.dcCode }.snapshot?.snapshotTime,
            )
            assertEquals(
                inventory2.skus.flatMap { it.inventory }.toSet(),
                inventorySnapshots.cleardownData.first {
                    it.dcCode == dcConfig2.dcCode
                }.snapshot?.skus?.first()?.inventory?.toSet(),
            )
        }
    }

    @Test
    fun `scheduled cleardown is used when no manual cleardown triggered`() {
        statsigFeatureFlagClient.fixtures = setOf(TriggeredCleardownTimeForInventorySnapshot(setOf(ContextData(DC, "BV"))))

        val dcCode = UUID.randomUUID().toString()

        val now = ZonedDateTime.now(UTC)
        val cleardownTime = now.minusDays(2)

        val scheduledCleardown = CleardownDetail(
            dcCode,
            cleardownTime.dayOfWeek,
            UTC,
            null,
            cleardownTime.toLocalTime(),
        )

        coEvery {
            inventoryRepository.getCleardownDetails(setOf(dcCode))
        } returns listOf(scheduledCleardown)

        val resolvedCleardown = runBlocking { inventoryService.getResolvedLatestCleardownTime(setOf(dcCode))[dcCode]!! }

        assertEquals(dcCode, resolvedCleardown.dcCode)
        assertEquals(SCHEDULED, resolvedCleardown.cleardownMode)
        assertEquals(
            cleardownTime.with(cleardownTime.toLocalTime()).truncatedTo(ChronoUnit.MINUTES),
            resolvedCleardown.cleardownTime,
        )
    }

    @Test
    fun `manual cleardown is used if triggered after last cleardown day`() {
        val dcCode = UUID.randomUUID().toString()
        statsigFeatureFlagClient.fixtures = setOf(TriggeredCleardownTimeForInventorySnapshot(setOf(ContextData(DC, dcCode))))

        val now = ZonedDateTime.now(UTC)

        val triggeredCleardownTime = now.minusDays(1)

        val scheduledCleardown = CleardownDetail(
            dcCode,
            now.minusDays(3).dayOfWeek,
            UTC,
            triggeredCleardownTime,
            LocalTime.now(),
        )

        coEvery {
            inventoryRepository.getCleardownDetails(setOf(dcCode))
        } returns listOf(scheduledCleardown)

        val resolvedCleardown = runBlocking { inventoryService.getResolvedLatestCleardownTime(setOf(dcCode))[dcCode]!! }

        assertEquals(dcCode, resolvedCleardown.dcCode)
        assertEquals(TRIGGERED, resolvedCleardown.cleardownMode)
        assertEquals(triggeredCleardownTime.truncatedTo(ChronoUnit.MINUTES), resolvedCleardown.cleardownTime)
    }

    @ParameterizedTest
    @CsvSource(
        "1,TRIGGERED",
        "$SCHEDULED_CLEARDOWN_DAYS_AFTER_MANUAL,TRIGGERED",
        "4,SCHEDULED",
    )
    fun `manual cleardown is used if triggered in past X days`(
        triggeredDaysInPast: Long,
        cleardownMode: CleardownMode
    ) {
        val dcCode = UUID.randomUUID().toString()
        statsigFeatureFlagClient.fixtures = setOf(TriggeredCleardownTimeForInventorySnapshot(setOf(ContextData(DC, dcCode))))

        val cleardownTime = ZonedDateTime.now(UTC)
        val triggeredCleardownTime = cleardownTime.minusDays(triggeredDaysInPast)

        val scheduledCleardown = CleardownDetail(
            dcCode,
            cleardownTime.dayOfWeek,
            UTC,
            triggeredCleardownTime,
            triggeredCleardownTime.toLocalTime(),
        )

        coEvery {
            inventoryRepository.getCleardownDetails(setOf(dcCode))
        } returns listOf(scheduledCleardown)

        val resolvedCleardown = runBlocking { inventoryService.getResolvedLatestCleardownTime(setOf(dcCode))[dcCode]!! }

        assertEquals(dcCode, resolvedCleardown.dcCode)
        assertEquals(cleardownMode, resolvedCleardown.cleardownMode)
        assertEquals(
            when (cleardownMode) {
                TRIGGERED -> triggeredCleardownTime.with(cleardownTime.toLocalTime()).truncatedTo(ChronoUnit.MINUTES)
                SCHEDULED -> cleardownTime.with(cleardownTime.toLocalTime())
            }.truncatedTo(ChronoUnit.MINUTES),
            resolvedCleardown.cleardownTime,
        )
    }

    @Test
    fun `should return inventory activities range before cleardown time`() {
        val dcConfig1 = DistributionCenterConfiguration.default(
            "D1",
        ).copy(cleardown = LocalDateTime.now().minusDays(1).dayOfWeek)
        val dcConfig2 = DistributionCenterConfiguration.default(
            "BV",
        ).copy(cleardown = LocalDateTime.now().dayOfWeek)

        val cleardown1 = CleardownDetail(
            dcConfig1.dcCode,
            dcConfig1.cleardown,
            dcConfig1.zoneId,
            null,
            LocalTime.MIDNIGHT,
        )
        val cleardown2 = CleardownDetail(
            dcConfig2.dcCode,
            dcConfig2.cleardown,
            dcConfig2.zoneId,
            null,
            LocalTime.NOON,
        )

        coEvery {
            inventoryRepository.getCleardownDetails(setOf(dcConfig1.dcCode, dcConfig2.dcCode))
        } returns listOf(cleardown1, cleardown2)
        coEvery { inventoryRepository.fetchBy(any(), any()) } returns emptyList()

        coEvery {
            inventoryRepository.fetchInventory(
                listOf(
                    ScheduledCleardown(dcConfig1.dcCode, cleardown1.scheduledDateTime),
                    ScheduledCleardown(dcConfig2.dcCode, cleardown2.scheduledDateTime),
                ),
            )
        } returns listOf(
            InventorySnapshot(dcConfig1.dcCode, UUID.randomUUID(), cleardown1.scheduledDateTime.minusMinutes(10).toLocalDateTime(), emptyList()),
            InventorySnapshot(dcConfig2.dcCode, UUID.randomUUID(), cleardown2.scheduledDateTime.minusMinutes(10).toLocalDateTime(), emptyList()),
        )

        val expectedMovement = InventoryMovement.Companion.default()

        coEvery {
            inventoryActivityRepository.fetchInventoryActivity(
                setOf(dcConfig1.dcCode),
                calculateInboundTimeRange(
                    cleardown1.scheduledDateTime.toLocalDateTime(),
                    cleardown1.scheduledDateTime.minusMinutes(10).toLocalDateTime(),
                ).let {
                    DateTimeRange(it.start.atZone(dcConfig1.zoneId).toOffsetDateTime(), it.endInclusive.atZone(dcConfig1.zoneId).toOffsetDateTime())
                },
            )
        } returns listOf(expectedMovement)

        val expectedAdjustment = InventoryAdjustment.default()
        coEvery {
            inventoryActivityRepository.fetchInventoryActivity(
                setOf(dcConfig2.dcCode),
                calculateInboundTimeRange(
                    cleardown2.scheduledDateTime.toLocalDateTime(),
                    cleardown2.scheduledDateTime.minusMinutes(10).toLocalDateTime(),
                ).let {
                    DateTimeRange(it.start.atZone(dcConfig2.zoneId).toOffsetDateTime(), it.endInclusive.atZone(dcConfig2.zoneId).toOffsetDateTime())
                },
            )
        } returns listOf(expectedAdjustment)

        val inventorySnapshots = runBlocking {
            inventoryService.fetchInventorySnapshots(
                setOf(dcConfig1, dcConfig2),
            )
        }

        assertEquals(
            expectedMovement,
            inventorySnapshots.inventoryActivities.first {
                it.activityId == expectedMovement.activityId
            },
        )
        assertEquals(
            expectedAdjustment,
            inventorySnapshots.inventoryActivities.first {
                it.activityId == expectedAdjustment.activityId
            },
        )
    }

    @ParameterizedTest
    @CsvSource(
        value =
        [
            "2024-10-25, 2024-10-10T00:00:00, null,                2024-10-09", // day before cleardown time (inbounds logic)
            "2024-10-25, 2024-10-17T00:00:00, null,                2024-10-16", // day before cleardown time (inbounds logic)
            "2024-10-25, 2024-10-16T23:45:00, null,                2024-10-15", // day before cleardown time (inbounds logic)
            "2024-10-25, 2024-10-17T00:00:00, 2024-10-16T23:45:00, 2024-10-15", // day before cleardown snapshot time (inbounds logic)
            "2024-10-25, 2024-10-24T00:00:00, null,                2024-10-18", // min 7 days from today
        ],
        nullValues = ["null"],
    )
    fun `should return correct input data range from last cleardown`(
        today: LocalDate,
        lastCleardownDateTime: LocalDateTime,
        cleardownSnapshotTime: LocalDateTime?,
        expectedDateFrom: LocalDate
    ) {
        mockkStatic(LocalDate::class)
        every { LocalDate.now(UTC) } returns today

        val dcConfig = DistributionCenterConfiguration.default(
            "D1",
        ).copy(zoneId = UTC, cleardown = lastCleardownDateTime.dayOfWeek)

        val inputDcDateRange = calculationInputDcDateRange(
            dcConfig,
            CleardownData(
                dcCode = dcConfig.dcCode,
                cleardownTime = lastCleardownDateTime,
                cleardownMode = TRIGGERED,
                snapshot = cleardownSnapshotTime?.let {
                    InventorySnapshot(
                        dcCode = dcConfig.dcCode,
                        snapshotId = UUID.randomUUID(),
                        snapshotTime = cleardownSnapshotTime,
                        skus = emptyList(),
                    )
                },
            ),
        )

        assertEquals(
            DateRange(
                expectedDateFrom,
                today.plusDays(NUMBER_OF_DAYS_FOR_CALCULATION),
            ),
            inputDcDateRange,
        )
    }
}
