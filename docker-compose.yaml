---
version: '3.8'

x-calculations-db-sink-common: &calculations-db-sink-common
  command: [
    "-r",
    "/config/resources*.yaml",
    "-c",
    "/config/benthos.yaml"
  ]
  image: "jeffail/benthos:3.65"
  healthcheck:
    test: [ "CMD-SHELL", "wget --no-verbose --spider http://localhost:8080/ready || exit 1" ]
    interval: 30s
    timeout: 30s
    retries: 3
  volumes:
    - ./forecast-api-db/src/main/resources/:/config
    - ./docker/dev:/usr/src
  depends_on:
    - broker
    - inventory-postgres

x-calculations-db-sink-common-env: &calculations-db-sink-common-env
  BENTHOS_PORT: "8080"
  HF_KAFKA_BOOTSTRAP_SERVERS: "broker:19092"
  APP_NAME: "cif_benthos_forecast_db_sink"
  APP_VERSION: '1'
  DB_SCHEMA: "inventory?sslmode=disable"
  DB_HOST: "inventory-postgres"
  HF_INVENTORY_DB_USERNAME: "cif"
  HF_INVENTORY_DB_PASSWORD: "123456"
  TLS_ENABLED: "false"
  LOG_LEVEL: 'DEBUG'
# This is a simple single broker setup which is sufficient for local testing.
services:
  zookeeper:
    image: confluentinc/cp-zookeeper:7.3.1
    hostname: zookeeper
    container_name: zookeeper
    ports:
      - '2181:2181'
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_SERVER_ID: 1

  broker:
    image: confluentinc/cp-kafka:7.3.1
    hostname: broker
    container_name: broker
    depends_on:
      - zookeeper
    ports:
      - '29092:29092'
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: LISTENER_DOCKER_INTERNAL:PLAINTEXT,PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: LISTENER_DOCKER_INTERNAL://broker:19092,PLAINTEXT://broker:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_INTER_BROKER_LISTENER_NAME: LISTENER_DOCKER_INTERNAL
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      CONFLUENT_SUPPORT_CUSTOMER_ID: 'anonymous'
      ALL_TOPICS: ${ALL_TOPICS}
    volumes:
      - ./docker/dev:/usr/src
      - ./mvp/kafka-mirror-plugin/build/libs:/usr/src/kafka-mirror-plugin
    healthcheck:
      test: nc -z localhost 9092 || exit -1
      start_period: 15s
      interval: 5s
      timeout: 10s
      retries: 10


  prometheus:
    image: prom/prometheus:v2.38.0
    container_name: prometheus
    command: "--config.file=/etc/prometheus/prometheus.yml"
    ports:
      - '9090:9090'
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
      - ./docker/dev/prometheus/config.yaml:/etc/prometheus/prometheus.yml

  inventory-postgres:
    image: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/supply-automation-postgres:15.5
    container_name: inventory-postgres
    ports:
      - '5432:5432'
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U cif -d inventory" ]
      interval: 1s
      timeout: 2s
      retries: 5
    environment:
      LC_ALL: C.UTF-8
      POSTGRES_USER: cif
      POSTGRES_PASSWORD: 123456
      POSTGRES_DB: inventory

  # Admin panel for Kafka
  kafdrop:
    image: obsidiandynamics/kafdrop
    container_name: kafdrop
    restart: on-failure
    ports:
      - "19000:9000"
    environment:
      KAFKA_BROKERCONNECT: "broker:19092"
      JVM_OPTS: "-Xms160M -Xmx480M -XX:-TieredCompilation -XX:+UseStringDeduplication -noverify"
    depends_on:
      - broker

  benthos-distribution-center-to-sink:
    command: [
      "-r",
      "/benthos-source-to-sink/config/benthos-resources/*resources.yaml",
      "-c",
      "/benthos-source-to-sink/config/benthos.yaml"
    ]
    image: "jeffail/benthos:4.10"
    container_name: benthos-distribution-center-to-sink
    ports:
      - '4198:8080'
    healthcheck:
      test: [ "CMD-SHELL", "wget --no-verbose --spider http://localhost:8080/ready || exit 1" ]
      interval: 30s
      timeout: 30s
      retries: 3
    environment:
      BENTHOS_PORT: "8080"
      HF_KAFKA_BOOTSTRAP_SERVERS: "broker:19092"
      APP_NAME: "cif_benthos_distribution_center_to_sink"
      APP_VERSION: '1'
      DB_SCHEMA: "inventory?sslmode=disable"
      DB_HOST: "inventory-postgres"
      HF_INVENTORY_DB_USERNAME: "cif"
      HF_INVENTORY_DB_PASSWORD: "123456"
      HF_KAFKA_SASL_MECHANISM: "none"
      TLS_ENABLED: "false"
      SOURCE_TOPIC: 'csku-inventory-forecast.intermediate.distribution-center'
      SOURCE_TOPIC_VERSION: '${DISTRIBUTION_CENTER_TOPIC_VERSION}'
      RESOURCE_NAME: 'distribution_center'
      PROCESSOR_NAME: 'common'
    volumes:
      - ./benthos-source-to-sink/src/main/resources/config/:/benthos-source-to-sink/config
      - ./docker/dev:/usr/src
    depends_on:
      - broker
      - inventory-postgres

  kafka-db-to-sink:
    command: [
      "-c",
      "/etc/benthos/config.yaml",
      "-r",
      "/etc/benthos/resources-*.yaml",
      "streams",
      "/etc/benthos/streams"
    ]
    image: "jeffail/benthos:4.10"
    container_name:   kafka-db-to-sink
    ports:
      - '4199:8080'
    healthcheck:
      test: [ "CMD-SHELL", "wget --no-verbose --spider http://localhost:8080/ready || exit 1" ]
      interval: 30s
      timeout: 30s
      retries: 3
    environment:
      BENTHOS_PORT: "8080"
      HF_KAFKA_BOOTSTRAP_SERVERS: "broker:19092"
      APP_NAME: "cif_kafka_db_to_sink"
      APP_VERSION: '1'
      INVENTORY_DB_URL: "inventory-postgres:5432/inventory?sslmode=disable"
      HF_INVENTORY_DB_USERNAME: "cif"
      HF_INVENTORY_DB_PASSWORD: "123456"
      HF_KAFKA_SASL_MECHANISM: "none"
      TLS_ENABLED: "false"
      SKU_SPECIFICATION_TOPIC: 'csku-inventory-forecast.intermediate.sku-specification'
      SKU_SPECIFICATION_TOPIC_VERSION: '1'
      SKU_SPECIFICATION_PROCESSOR_GROUP_ID: "cif-kafka-db-sink-sku-specification.v4"
      KAFKA_CLIENT_ID: "kafka-db-sink-client-id"
      RESOURCE_NAME: 'kafka_db_sink'
      PROCESSOR_NAME: 'common'
    volumes:
      - ./kafka-db-sink/src/main/resources/:/etc/benthos
      - ./docker/dev:/usr/src
    depends_on:
      - broker
      - inventory-postgres

  calculations-db-sink:
    <<: *calculations-db-sink-common
    ports:
      - '4200:8080'
    environment:
      <<: *calculations-db-sink-common-env
      CALCULATOR_MODE: 'calculation'
      INPUT_TOPIC_NAME: 'csku-inventory-forecast.intermediate.calculations.v1'

  pre-prod-calculations-db-sink:
    <<: *calculations-db-sink-common
    ports:
      - '4201:8080'
    environment:
      <<: *calculations-db-sink-common-env
      CALCULATOR_MODE: 'pre_production_calculation'
      INPUT_TOPIC_NAME: 'csku-inventory-forecast.intermediate.pre-production.calculations.v1'

  live-calculations-db-sink:
    <<: *calculations-db-sink-common
    ports:
      - '4202:8080'
    environment:
      <<: *calculations-db-sink-common-env
      CALCULATOR_MODE: 'live_inventory_calculation'
      INPUT_TOPIC_NAME: 'csku-inventory-forecast.intermediate.forecast.live-inventory-calculations.v1'

  live-preprod-calculations-db-sink:
    <<: *calculations-db-sink-common
    ports:
      - '4203:8080'
    environment:
      <<: *calculations-db-sink-common-env
      CALCULATOR_MODE: 'pre_production_live_inventory_calculation'
      INPUT_TOPIC_NAME: 'csku-inventory-forecast.intermediate.forecast.live-inventory-pre-production-calculations.v1'

  demand:
    image: csku-inventory-forecast/demand:local
    container_name: demand
    depends_on:
      - broker
      - sqs-service
      - s3-service
    environment:
      HF_TIER: local
      HF_PROFILES: e2e
      HF_SCHEMA_REGISTRY_URL: 'https://kafka-live-hellofresh-live.aivencloud.com:23411'
      HF_AIVEN_PASSWORD: ${HF_AIVEN_PASSWORD}
      bootstrap.servers: broker:19092
      HF_INVENTORY_DB_MASTER_HOST: inventory-postgres
      HF_INVENTORY_DB_USERNAME: cif
      HF_INVENTORY_DB_PASSWORD: 123456
      HF_INVENTORY_DB_HOST: inventory-postgres
      HF_INVENTORY_READONLY_DB_USERNAME: cif
      HF_INVENTORY_READONLY_DB_PASSWORD: 123456
      aws.sqs.host: http://sqs-service:4566
      aws.s3.host: http://s3-service:4567
    restart: on-failure

  purchase-order-service:
    image: csku-inventory-forecast/purchase-order-service:local
    container_name: purchase-order-service
    depends_on:
      - broker
      - inventory-postgres
    environment:
      HF_TIER: local
      bootstrap.servers: broker:19092
      HF_INVENTORY_DB_HOST: inventory-postgres
      HF_INVENTORY_READONLY_DB_USERNAME: cif
      HF_INVENTORY_READONLY_DB_PASSWORD: 123456
      HF_INVENTORY_DB_MASTER_HOST: inventory-postgres
      HF_INVENTORY_DB_USERNAME: cif
      HF_INVENTORY_DB_PASSWORD: 123456
    restart: on-failure

  goods-received-note-service:
    image: csku-inventory-forecast/goods-received-note-service:local
    container_name: goods-received-note-service
    depends_on:
      - broker
    environment:
      HF_TIER: local
      HF_PROFILES: e2e
      HF_SCHEMA_REGISTRY_URL: 'https://kafka-live-hellofresh-live.aivencloud.com:23411'
      HF_AIVEN_PASSWORD: ${HF_AIVEN_PASSWORD}
      bootstrap.servers: broker:19092
      HF_INVENTORY_DB_MASTER_HOST: inventory-postgres
      HF_INVENTORY_DB_USERNAME: cif
      HF_INVENTORY_DB_PASSWORD: 123456
      HF_INVENTORY_DB_HOST: inventory-postgres
      HF_INVENTORY_READONLY_DB_USERNAME: cif
      HF_INVENTORY_READONLY_DB_PASSWORD: 123456
    restart: on-failure

  calculator-job:
    image: csku-inventory-forecast/calculator-job:local
    deploy:
      resources:
        reservations:
          cpus: '1'
          memory: 1G
    container_name: calculator-job
    depends_on:
      - broker
    environment:
      bootstrap.servers: broker:19092
      HF_TIER: local
      HF_SCHEMA_REGISTRY_URL: 'https://kafka-live-hellofresh-live.aivencloud.com:23411'
      HF_AIVEN_PASSWORD: ${HF_AIVEN_PASSWORD}
      HF_INVENTORY_DB_HOST: inventory-postgres
      HF_INVENTORY_READONLY_DB_USERNAME: cif
      HF_INVENTORY_READONLY_DB_PASSWORD: 123456
    restart: on-failure

  forecast-api:
    image: csku-inventory-forecast/forecast-api:local
    container_name: forecast-api
    ports:
      - '4204:8080'
    depends_on:
      - inventory-postgres
    environment:
      HF_TIER: local
      HF_INVENTORY_DB_HOST: inventory-postgres
      HF_INVENTORY_DB_USERNAME: cif
      HF_INVENTORY_DB_PASSWORD: 123456
      HF_INVENTORY_DB_MASTER_HOST: inventory-postgres
      HF_INVENTORY_READONLY_DB_USERNAME: cif
      HF_INVENTORY_READONLY_DB_PASSWORD: 123456
      LOG_LEVEL: 'DEBUG'
      HF_AUTH_SERVICE_JWT_SECRET_KEY: ""
    restart: on-failure

  output-demand-forecast:
    image: csku-inventory-forecast/output-demand-forecast:local
    container_name: output-demand-forecast
    environment:
      bootstrap.servers: broker:19092
      HF_TIER: local
      HF_INVENTORY_DB_HOST: inventory-postgres
      HF_INVENTORY_READONLY_DB_USERNAME: cif
      HF_INVENTORY_READONLY_DB_PASSWORD: 123456

  supply-quantity-recommendation-job:
    image: csku-inventory-forecast/supply-quantity-recommendation-job:local
    container_name: supply-quantity-recommendation-job
    depends_on:
      - sqs-service
      - s3-service
    environment:
      HF_TIER: local
      bootstrap.servers: broker:19092
      HF_INVENTORY_DB_HOST: inventory-postgres
      HF_INVENTORY_DB_USERNAME: cif
      HF_INVENTORY_DB_PASSWORD: 123456
      HF_INVENTORY_DB_MASTER_HOST: inventory-postgres
      HF_INVENTORY_READONLY_DB_USERNAME: cif
      HF_INVENTORY_READONLY_DB_PASSWORD: 123456
      aws.sqs.host: http://sqs-service:4566
      aws.s3.host: http://s3-service:4567

  transfer-order-service:
      image: csku-inventory-forecast/transfer-order-service:local
      container_name: transfer-order-service
      depends_on:
          - broker
          - inventory-postgres
      environment:
          HF_TIER: local
          bootstrap.servers: broker:19092
          HF_INVENTORY_DB_HOST: inventory-postgres
          HF_INVENTORY_DB_USERNAME: cif
          HF_INVENTORY_DB_PASSWORD: 123456
          HF_INVENTORY_DB_MASTER_HOST: inventory-postgres
          HF_INVENTORY_READONLY_DB_USERNAME: cif
          HF_INVENTORY_READONLY_DB_PASSWORD: 123456

  db-migration:
    image: flyway/flyway:9.22
    depends_on:
      - inventory-postgres
    command: -locations=filesystem:/flyway/sql -connectRetries=20 -placeholders.cifReadonlyPassword="'123456'" migrate
    volumes:
      - ./inventory-db/src/main/resources/migrations:/flyway/sql/migration
    environment:
      FLYWAY_URL: '***********************************************************************'
      FLYWAY_USER: 'cif'
      FLYWAY_PASSWORD: '123456'
      FLYWAY_DRIVER: org.postgresql.Driver

  sqs-service:
      image: localstack/localstack:3.2
      ports:
          - "4566:4566"
      environment:
          QUEUE_NAME: '${SQS_QUEUE_NAME:-safety-multiplier-sqs-local-queue}'
          QUEUE_NAME_3PW_STOCK_UPLOADS: '${SQS_QUEUE_NAME:-supply_automation_3pw_inventory_notification_local}'
          QUEUE_NAME_STOCK_UPDATE: '${SQS_QUEUE_NAME:-supply_automation_stock_update_queue_local}'
          QUEUE_NAME_US_SAFETY_STOCK: '${SQS_QUEUE_NAME:-hf_ip_us_safety_stock_notification_queue_local}'
          QUEUE_NAME_UK_PACKAGING: '${SQS_QUEUE_NAME:-uk_packaging-sqs-local-queue}'
          QUEUE_NAME_BNL_CALCULATION: '${SQS_QUEUE_NAME:-bnl-calculation-local-queue}'
          AWS_KEY: '${AWS_KEY:-foo}'
          AWS_SECRET: '${AWS_SECRET:-bar}'
          AWS_REGION: '${AWS_REGION:-eu-west-1}'
          LOCALSTACK_DUMMY_ID: '${LOCALSTACK_DUMMY_ID:-000000000000}'
          SERVICES: sqs
          DEBUG: 1
          DOCKER_HOST: unix:///var/run/docker.sock
          HOSTNAME_EXTERNAL: localstack
          SQS_DISABLE_CLOUDWATCH_METRICS: 1
          DEFAULT_REGION: '${AWS_REGION:-eu-west-1}'
          USE_SINGLE_REGION: 1
      healthcheck:
          test: [ "CMD-SHELL", "curl --silent --fail localhost:4566/_localstack/health | grep '\"sqs\": \"running\"'" ]
          interval: 3s
          timeout: 10s
          start_period: 10s
      volumes:
          - ./docker/dev/sqs/init.sh:/etc/localstack/init/ready.d/init-aws.sh
          - ./docker/dev/sqs/tmp:/var/lib/localstack
          - /var/run/docker.sock:/var/run/docker.sock

  s3-service:
    image: localstack/localstack
    ports:
        - "127.0.0.1:4567:4566"            # LocalStack Gateway
        - "127.0.0.1:4510-4559:4510-4559"  # external services port range
    environment:
        - SERVICES=s3
        - DEFAULT_REGION=eu-west-1
        - AWS_DEFAULT_REGION=eu-west-1
        - HOSTNAME_EXTERNAL=localhost
        - DATA_DIR=/tmp/localstack/data
        - USE_SSL=false
        - DEBUG=1
        - S3_BUCKET_NAME="hf-bi-dwh-uploader"
    volumes:
        - "${LOCALSTACK_VOLUME_DIR:-./volume}:/var/lib/localstack"
        - "./docker/s3-buckets/init.sh:/etc/localstack/init/ready.d/init-aws.sh"
        - "./docker/s3-buckets:/docker/s3-buckets"
        - /var/run/docker.sock:/var/run/docker.sock
    healthcheck:
        test: [ "CMD", "curl", "-f", "http://localhost:4567/health", "|", "grep", "running" ]
        interval: 5s
        timeout: 10s
        retries: 5
        start_period: 20s

  pushgateway:
    image: prom/pushgateway
    container_name: pushgateway
    restart: on-failure
    ports:
      - "9091:9091"

networks:
  default:
    name: cif-network
