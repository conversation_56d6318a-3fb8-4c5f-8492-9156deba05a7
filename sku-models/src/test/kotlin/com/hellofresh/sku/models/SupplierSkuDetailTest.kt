package com.hellofresh.sku.models

import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals

class SupplierSkuDetailTest {
    private val leadTimes = listOf(
        LeadTime(5, LocalDate.of(2023, 9, 20), LocalDate.of(2023, 9, 25)),
        LeadTime(7, LocalDate.of(2023, 9, 21), LocalDate.of(2023, 9, 26)),
        LeadTime(3, LocalDate.of(2023, 9, 22), LocalDate.of(2023, 9, 27)),
    )

    @Test
    fun `should get max lead time when the given input date is within range`() {
        val supplierSkuDetail = SupplierSkuDetail(UUID.randomUUID(), "Supplier 1", 1, leadTimes)

        val result = supplierSkuDetail.getMaxLeadTimeByDate(LocalDate.of(2023, 9, 24))

        assertEquals(7, result)
    }

    @Test
    fun `should return null when date is outside range`() {
        val supplierSkuDetail = SupplierSkuDetail(UUID.randomUUID(), "Supplier 1", 1, leadTimes)

        val result = supplierSkuDetail.getMaxLeadTimeByDate(LocalDate.of(2023, 9, 28))

        assertEquals(null, result)
    }

    @Test
    fun `should return null when leadTimes list is empty`() {
        val supplierSkuDetail = SupplierSkuDetail(UUID.randomUUID(), "Supplier 1", 1, emptyList())

        val result = supplierSkuDetail.getMaxLeadTimeByDate(LocalDate.of(2023, 9, 24))

        assertEquals(null, result)
    }
}
