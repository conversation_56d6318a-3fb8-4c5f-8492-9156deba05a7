package com.hellofresh.sku.models

import com.hellofresh.sku.models.SkuId.SkuIdSerde
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals

class SkuIdSerdeTest {

    @Test fun `deserialize a string UUID into SkuID instance`() {
        val uuid = UUID.randomUUID()
        val skuId = SkuIdSerde.deserializer().deserialize("", uuid.toString().toByteArray())
        assertEquals(uuid, skuId.value)
    }

    @Test fun `serialize SkuId directly to a string`() {
        val skuId = SkuId(UUID.randomUUID())
        val serialized = String(SkuIdSerde.serializer().serialize(null, skuId))
        assertEquals(skuId.value.toString(), serialized)
    }
}
