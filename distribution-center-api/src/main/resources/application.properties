application.name=distribution-center-api

parallelism=3

group.id=csku-distribution-center-api.dcRegistry.v4
auto.offset.reset=earliest

# 5 min
max.poll.interval.ms=600000

# poll will either wait 5 seconds or for the 20KB or 500 records
fetch.min.bytes=20000
fetch.max.wait.ms=5000
max.poll.records=500

poll.interval_ms=20
poll.timeout=PT1S
process.timeout=PT15S
acks=all

service.email.address=<EMAIL>

consumer.poison-pill.strategy=LOG_ERROR_IGNORE

HF_AZURE_ISSUER=issuer
HF_AZURE_CLIENT_ID=test
HF_AZURE_JWKS_URI=https://azure-jwks-uri
