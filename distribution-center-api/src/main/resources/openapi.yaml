openapi: 3.0.3
servers: [ ]
info:
  description: Distribution Center API
  version: 1.0.0
  title: DC API
  contact:
    email: <EMAIL>
paths:
  /dc:
    get:
      operationId: getDcConfigs
      summary: 'Fetches all the Distribution Center configuration'
      parameters:
        - $ref: '#/components/parameters/countryQueryParam'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DcConfigurationResponse'
        '204':
          description: No Distribution Centers Found
        '500':
          $ref: '#/components/responses/apiErrorResponse'
  /dc/{dcCode}:
    get:
      operationId: getDcConfig
      summary: 'Fetches the Distribution Center configuration'
      parameters:
        - $ref: '#/components/parameters/dcCodePathParam'
        - $ref: '#/components/parameters/fromDateTime'
        - $ref: '#/components/parameters/toDateTime'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DcConfigurationRequest'
    put:
      operationId: updateDcConfig
      summary: 'Updates a DC configuration'
      security:
        - BearerAuth: [admin]
      parameters:
        - $ref: '#/components/parameters/dcCodePathParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateDcConfigurationRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DcConfigurationResponse'

  /dc/{dcCode}/v1:
    put:
      operationId: updateDcConfigMultipleParams
      summary: 'Updates DC configuration params - cleardown, production start, enabled, hasCleardown'
      security:
        - BearerAuth: [ admin ]
      parameters:
        - $ref: '#/components/parameters/dcCodePathParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateDcConfigurationMultiParamRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DcConfigurationResponse'

components:
  parameters:
    dcCodePathParam:
      in: path
      name: 'dcCode'
      required: true
      schema:
        $ref: '#/components/schemas/DcCode'
    fromDateTime:
      in : query
      name: 'from'
      required: false
      schema:
        $ref: '#/components/schemas/snapshotDateTime'
    toDateTime:
      in : query
      name: 'to'
      required: false
      schema:
        $ref: '#/components/schemas/snapshotDateTime'

    countryQueryParam:
      in: query
      name: 'country'
      required: true
      allowEmptyValue: false
      description: country code (example DE)
      schema:
        $ref: '#/components/schemas/Country'

  schemas:
    DcConfigurationRequest:
      type: object
      required:
        - dcCode
        - market
        - productionStartDay
        - zoneId
        - enabled
        - hasCleardown
        - brands
      properties:
        dcCode:
          $ref: '#/components/schemas/DcCode'
        market:
          $ref: '#/components/schemas/Market'
        productionStartDay:
          $ref: '#/components/schemas/DayOfWeek'
        cleardownDay:
          $ref: '#/components/schemas/DayOfWeek'
        zoneId:
          type: string
        enabled:
          type: boolean
        hasCleardown:
          type: boolean
        scheduledCleardownTime:
          type: string
          format: time
          description: "Scheduled cleardown time in ISO_LOCAL_TIME format according to ISO-8601."
          example: "12:15:52"
        snapshots:
          type: array
          items:
            $ref: '#/components/schemas/SnapshotTime'
        brands:
            type: array
            items:
                type: string
                description: "List of brands associated with the DC configuration."
                example: "BRAND_A"
        poCutOffTime:
          type: string
          format: time
          description: "PO cutoff time in ISO_LOCAL_TIME format according to ISO-8601."
          example: "12:15:52"
    DcConfigurationResponse:
      allOf:
        - $ref: '#/components/schemas/DcConfigurationRequest'

    UpdateDcConfigurationRequest:
      type: object
      required:
        - cleardownDay
      properties:
        cleardownDay:
          $ref: '#/components/schemas/DayOfWeek'

    UpdateDcConfigurationMultiParamRequest:
      type: object
      required:
        - cleardownDay
        - productionStart
        - enabled
        - hasCleardown
      properties:
        cleardownDay:
          $ref: '#/components/schemas/DayOfWeek'
        productionStart:
          $ref: '#/components/schemas/DayOfWeek'
        enabled:
          type: boolean
        hasCleardown:
          type: boolean
        scheduledCleardownTime:
          type: string
          format: time
          description: "Scheduled cleardown time in ISO_LOCAL_TIME format according to ISO-8601."
          example: "12:15:52"
        wmsType:
          type: string
          description: "WMS type"
          example: "WMS"
        poCutOffTime:
          type: string
          format: time
          description: "PO cutoff time in ISO_LOCAL_TIME format according to ISO-8601."
          example: "12:15:52"

    DayOfWeek:
      type: string
      enum:
        - MONDAY
        - TUESDAY
        - WEDNESDAY
        - THURSDAY
        - FRIDAY
        - SATURDAY
        - SUNDAY
      example: SUNDAY

    Market:
      type: string
      description: Hellofresh Market code.
      pattern: '^[A-Z]*$'
      example: 'DACH'

    SnapshotTime:
      type: object
      properties:
        snapshotId:
          type: string
          format: uuid
        snapshotTime:
          type: string
          format: date-time
    Country:
      type: string
      description: Hellofresh Country code.
      pattern: '^[A-Z]*$'
      example: 'DE'

    DcCode:
      type: string
      description: dcCode in upper caps
      pattern: '^[A-Z]+$'
      example: 'VE'
    snapshotDateTime:
      type: string
      format: date-time
      description: "end of time range for snapshots requiredin ISO_LOCAL_TIME format according to ISO-8601."
      example: "12:15:52"

    ErrorResponse:
      type: object
      description: 'Describes the error from the server'
      properties:
        reason:
          type: string
          example: "fieldName is missing"

  responses:
    apiErrorResponse:
      description: Error response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
