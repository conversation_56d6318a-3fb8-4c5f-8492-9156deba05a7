package com.hellofresh.cif.distributionCenter.repo

import com.hellofresh.cif.distributionCenter.model.DcParamRequest
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import java.time.DayOfWeek
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneId
import java.util.UUID

interface DcRepository {
    suspend fun updateCleardown(dcCode: String, cleardown: DayOfWeek, authorEmail: String): DcConfiguration?
    suspend fun updateDc(dcParamRequest: DcParamRequest): DcConfiguration?
    suspend fun getAllDcs(market: String): List<DcConfiguration>
    suspend fun find(dcCode: String, fromDateTime: LocalDateTime?, toDateTime: LocalDateTime?): DcConfiguration?

    suspend fun getUnpublishedDcConfigs(): List<DcConfiguration>

    suspend fun updatePublishedFlag(dcCode: String, published: Boolean)
}

data class DcConfiguration(
    val dcCode: String,
    val productionStart: DayOfWeek,
    val cleardown: DayOfWeek?,
    val market: String,
    val zoneId: ZoneId,
    val globalDc: String?,
    val enabled: Boolean,
    val hasCleardown: Boolean,
    val lastUpdatedByEmail: String,
    val scheduledClearDownTime: LocalTime,
    val wmsType: WmsSystem,
    val snapshotTimes: List<SnapshotTime> = emptyList(),
    val poCutoffTime: LocalTime?,
    val brands: List<String> = emptyList(),
)

data class SnapshotTime(
    val snapshotId: UUID,
    val snapshotTime: OffsetDateTime
)
