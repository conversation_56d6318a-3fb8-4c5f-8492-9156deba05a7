package com.hellofresh.cif.distributionCenter.service

import com.hellofresh.cif.distributionCenter.model.DcParamRequest
import com.hellofresh.cif.distributionCenter.repo.DcRepository
import java.time.DayOfWeek
import java.time.LocalDateTime

class DistributionCenterService(private val dcRepository: DcRepository) {

    suspend fun getAllDcs(market: String) = dcRepository.getAllDcs(market)
    suspend fun getByCode(dcCode: String, fromDateTime: LocalDateTime?, toDateTime: LocalDateTime?) =
        dcRepository.find(dcCode, fromDateTime, toDateTime)
    suspend fun updateCleardownDay(dcCode: String, cleardown: DayOfWeek, lastUpdatedByEmail: String) =
        dcRepository.updateCleardown(dcCode, cleardown, lastUpdatedByEmail)
    suspend fun updateDc(dcParamRequest: DcParamRequest) = dcRepository.updateDc(dcParamRequest)
}

class DcAlreadyExistsException(dcCode: String) : Exception("This DC already exists $dcCode")
