package com.hellofresh.cif.distributionCenter.model

import com.hellofresh.cif.distributionCenter.models.WmsSystem
import java.time.DayOfWeek
import java.time.LocalTime

data class DcParamRequest(
    val dcCode: String,
    val cleardown: DayOfWeek,
    val productionStart: DayOfWeek,
    val enabled: <PERSON><PERSON><PERSON>,
    val hasCleardown: <PERSON><PERSON>an,
    val lastUpdatedByEmail: String,
    val scheduledClearDownTime: LocalTime?,
    val wmsType: WmsSystem?,
    val poCutoffTime: LocalTime?,
)
