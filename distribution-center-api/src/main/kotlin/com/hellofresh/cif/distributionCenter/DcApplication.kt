package com.hellofresh.cif.distributionCenter

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.google.common.util.concurrent.ThreadFactoryBuilder
import com.hellofresh.cif.checks.CheckResult
import com.hellofresh.cif.checks.HealthChecks
import com.hellofresh.cif.checks.StartUpChecks
import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.DBConfiguration
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenter.DcApplication.createDcConfigPublisher
import com.hellofresh.cif.distributionCenter.DcApplication.dcConfigTopicProducer
import com.hellofresh.cif.distributionCenter.DcApplication.startHttpServer
import com.hellofresh.cif.distributionCenter.api.RoutingModule
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfigurationTopicValue
import com.hellofresh.cif.distributionCenter.repo.DcRepositoryImpl
import com.hellofresh.cif.distributionCenter.repo.consumer.PerformDcRegistryUpsert
import com.hellofresh.cif.distributionCenter.service.DcConfigPublishScheduler
import com.hellofresh.cif.distributionCenter.service.DcConfigurationKafkaPublisher
import com.hellofresh.cif.distributionCenter.service.DistributionCenterService
import com.hellofresh.cif.lib.StatusServer
import com.hellofresh.cif.lib.kafka.ConsumerProcessorConfig
import com.hellofresh.cif.lib.kafka.CoroutinesProcessor
import com.hellofresh.cif.lib.kafka.DeserializationExceptionStrategy
import com.hellofresh.cif.lib.kafka.IgnoreAndContinueProcessing
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.lib.kafka.serde.JacksonSerializer
import com.hellofresh.cif.lib.kafka.serde.ProtoDeserializer
import com.hellofresh.cif.lib.metrics.HelloFreshMeterRegistry
import com.hellofresh.cif.lib.metrics.createMeterRegistry
import com.hellofresh.cif.shutdown.shutdownHook
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.hellofresh.proto.stream.scm.registry.dc.v1beta1.DistributionCenter as DistributionCenterProto
import io.ktor.server.application.Application
import io.ktor.server.application.ServerReady
import io.ktor.server.application.install
import io.ktor.server.engine.embeddedServer
import io.ktor.server.metrics.micrometer.MicrometerMetrics
import io.ktor.server.netty.Netty
import io.ktor.server.netty.NettyApplicationEngine
import io.ktor.server.plugins.callid.CallId
import io.ktor.server.plugins.callid.callIdMdc
import io.ktor.server.plugins.callid.generate
import io.ktor.server.plugins.calllogging.CallLogging
import io.ktor.server.plugins.compression.Compression
import io.ktor.server.plugins.compression.gzip
import io.micrometer.core.instrument.MeterRegistry
import java.util.concurrent.ArrayBlockingQueue
import java.util.concurrent.Executors
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy
import java.util.concurrent.TimeUnit.HOURS
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.time.Duration
import kotlinx.coroutines.ExecutorCoroutineDispatcher
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.common.serialization.StringDeserializer
import org.apache.kafka.common.serialization.StringSerializer
import org.apache.logging.log4j.kotlin.Logging

private const val HTTP_PORT = 8080
private const val STATUS_SERVER_HTTP_PORT = 8081
private const val CALL_ID_LENGTH = 12
private const val DEFAULT_PARALLELISM = 1
private const val DC_REGISTRY_TOPIC_NAME = "public.scm.registry.dc.v1beta1"

// a custom dictionary that skips the +/=- characters for better grepping
private const val CALL_ID_DICTIONARY = "abcdefghijklmnopqrstuvwxyz0123456789"
private const val SERVICE_EMAIL_ADDRESS = "<EMAIL>"
private const val DEFAULT_POLL_INTERVAL_MS = 20
suspend fun main() {
    val parallelism = ConfigurationLoader.getIntegerOrDefault("parallelism", DEFAULT_PARALLELISM)
    val coroutineDispatcher = Executors.newFixedThreadPool(
        parallelism,
        ThreadFactoryBuilder().setNameFormat("producer-thread-%d").build(),
    ).asCoroutineDispatcher()

    val meterRegistry = createMeterRegistry()
    val metricsDSLContext = DBConfiguration.jooqMasterDslContext(parallelism, meterRegistry)

    val repo = DcRepositoryImpl(metricsDSLContext)
    val jobTimePeriod = Duration.parse(ConfigurationLoader.getStringOrDefault("job.time_period", "PT1M"))
    val kafkaProducer = dcConfigTopicProducer(ConfigurationLoader.loadKafkaProducerConfigurations())
    val dcPublisher = DcConfigurationKafkaPublisher(kafkaProducer, repo)
    val dcConfigPublishScheduler = createDcConfigPublisher(jobTimePeriod, dcPublisher, meterRegistry)
    dcConfigPublishScheduler.scheduleJob()
    val dcService = DistributionCenterService(repo)
    startDcRegistryApp(metricsDSLContext, coroutineDispatcher, meterRegistry, dcService)
}

private suspend fun startDcRegistryApp(
    metricsDSLContext: MetricsDSLContext,
    coroutineDispatcher: ExecutorCoroutineDispatcher,
    meterRegistry: HelloFreshMeterRegistry,
    dcService: DistributionCenterService
) {
    val pollTimeout = Duration.parse(ConfigurationLoader.getStringOrFail("poll.timeout"))
    val pollInterval = ConfigurationLoader.getIntegerOrDefault("poll.interval_ms", DEFAULT_POLL_INTERVAL_MS).toLong()
    val processTimeout = Duration.parse(ConfigurationLoader.getStringOrFail("process.timeout"))
    val emailAddress = ConfigurationLoader.getStringOrDefault("service.email.address", SERVICE_EMAIL_ADDRESS)
    val update = PerformDcRegistryUpsert(metricsDSLContext, emailAddress)
    val pollConfig = PollConfig(
        pollTimeout,
        pollInterval,
        processTimeout,
    )
    val consumerConfig: Map<String, String> = ConfigurationLoader.loadKafkaConsumerConfigurations() +
        mapOf(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG to "false")
    val consumerProcessorConfig = ConsumerProcessorConfig(
        consumerConfig,
        StringDeserializer(),
        ProtoDeserializer<DistributionCenterProto>(),
        listOf(DC_REGISTRY_TOPIC_NAME),
    )

    startHttpServer(dcService, meterRegistry)

    withContext(coroutineDispatcher) {
        launch {
            CoroutinesProcessor(
                pollConfig = pollConfig,
                consumerProcessorConfig = consumerProcessorConfig,
                meterRegistry = meterRegistry,
                process = update,
                handleDeserializationException = DeserializationExceptionStrategy.create(
                    ConfigurationLoader.getStringOrFail("consumer.poison-pill.strategy"),
                    meterRegistry,
                ),
                recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(
                    meterRegistry,
                    "distribution_center_processor_write_failure",
                ),
            )
                .also {
                    shutdownHook(it)
                    HealthChecks.add(it)
                    StartUpChecks.add(it)
                }
                .run()
        }
    }
}

object DcApplication : Logging {
    private val objectMapper = jacksonObjectMapper().findAndRegisterModules()
    private val isUp = AtomicBoolean(false)

    private val jwtConfiguration = JwtConfiguration(
        ConfigurationLoader.getStringOrFail("HF_AUTH_SERVICE_JWT_SECRET_KEY"),
        ConfigurationLoader.getStringOrFail("application.name"),
        ConfigurationLoader.getStringOrFail("HF_AZURE_ISSUER"),
        ConfigurationLoader.getStringOrFail("HF_AZURE_CLIENT_ID"),
        ConfigurationLoader.getStringOrFail("HF_AZURE_JWKS_URI"),
    )
    private val timeoutDuration = Duration.parse(ConfigurationLoader.getStringOrDefault("db.timeout", "PT15S"))

    @Suppress("MagicNumber")
    fun startHttpServer(dcService: DistributionCenterService, meterRegistry: MeterRegistry) {
        StatusServer.run(
            meterRegistry,
            STATUS_SERVER_HTTP_PORT,
        )
        embeddedServer(
            factory = Netty.apply {
                configuration {
                    maxHeaderSize = 16 * 1024
                }
            },
            port = HTTP_PORT,
            module = {
                installFeatures(
                    meterRegistry,
                    listOf(
                        RoutingModule().dcConfigRoutingModule(dcService, jwtConfiguration, timeoutDuration),
                    ),
                )
            },
        )
            .also {
                it.monitor.subscribe(ServerReady) {
                    isUp.set(true)
                    logger.info("Main Server ready")
                }
                shutdownNeeded { shutdownHttpServer(it.engine) }
                StartUpChecks.add(::check)
            }.start(false)
    }

    private fun check() = CheckResult("HttpServer", isUp.get())

    fun dcConfigTopicProducer(producerConfig: Map<String, String>): KafkaProducer<String, DistributionCenterConfigurationTopicValue> =
        KafkaProducer(
            producerConfig,
            StringSerializer(),
            JacksonSerializer<DistributionCenterConfigurationTopicValue>(objectMapper),
        )

    fun createDcConfigPublisher(
        duration: Duration,
        dcPublisher: DcConfigurationKafkaPublisher,
        meterRegistry: MeterRegistry
    ): DcConfigPublishScheduler {
        val poolSize = 1
        val singleThreadExecutor = ThreadPoolExecutor(
            poolSize,
            poolSize,
            Long.MAX_VALUE,
            HOURS,
            ArrayBlockingQueue(poolSize, true),
            CallerRunsPolicy(),
        ).apply {
            shutdownNeeded { AutoCloseable { shutdownNow() } }
        }
        return DcConfigPublishScheduler(dcPublisher, duration, meterRegistry, singleThreadExecutor)
    }

    private fun Application.installFeatures(
        meterRegistry: MeterRegistry,
        routes: List<Application.() -> Unit>
    ) {
        install(Compression) { gzip() }
        install(CallId) {
            generate(length = CALL_ID_LENGTH, dictionary = CALL_ID_DICTIONARY)
        }
        install(CallLogging) {
            callIdMdc("request-id")
        }
        install(MicrometerMetrics) {
            registry = meterRegistry
        }
        routes.forEach { it() }
    }

    private fun shutdownHttpServer(engine: NettyApplicationEngine) = AutoCloseable {
        engine.stop(
            gracePeriodMillis = 5_000,
            timeoutMillis = 15_000,
        )
        isUp.set(false)
    }
}

data class JwtConfiguration(
    val secret: String,
    val realm: String,
    val issuer: String,
    val clientId: String,
    val jwksURI: String,
)
