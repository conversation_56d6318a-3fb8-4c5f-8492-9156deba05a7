package com.hellofresh.cif.distributionCenter.service

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfigurationTopicValue
import com.hellofresh.cif.distributionCenter.models.privateDistributionCenterTopic
import com.hellofresh.cif.distributionCenter.repo.DcConfiguration
import com.hellofresh.cif.distributionCenter.repo.DcRepository
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.logging.log4j.kotlin.Logging

val DC_CONFIG_TOPIC = privateDistributionCenterTopic.name

class DcConfigurationKafkaPublisher(
    private val kafkaProducer: KafkaProducer<
        String,
        DistributionCenterConfigurationTopicValue,
        >,
    private val dcRepo: DcRepository
) {

    suspend fun publishDcConfigs() {
        val unpublishedDcConfigs = dcRepo.getUnpublishedDcConfigs()
        if (unpublishedDcConfigs.isNotEmpty()) {
            logger.info("Publishing DC updates for ${unpublishedDcConfigs.map { it.dcCode }}")
            unpublishedDcConfigs.forEach {
                kafkaProducer
                    .send(ProducerRecord(DC_CONFIG_TOPIC, it.dcCode, mapToDistributionCenterVal(it))).get()
                dcRepo.updatePublishedFlag(it.dcCode, true)
            }
        }
    }

    private fun mapToDistributionCenterVal(dcConfig: DcConfiguration) =
        DistributionCenterConfigurationTopicValue(
            dcConfig.productionStart,
            dcConfig.cleardown,
            dcConfig.market,
            dcConfig.zoneId,
            dcConfig.globalDc,
            dcConfig.enabled,
            dcConfig.hasCleardown,
            dcConfig.scheduledClearDownTime.toString(),
            dcConfig.wmsType,
            dcConfig.poCutoffTime.toString(),
            dcConfig.brands,
        )

    companion object : Logging
}
