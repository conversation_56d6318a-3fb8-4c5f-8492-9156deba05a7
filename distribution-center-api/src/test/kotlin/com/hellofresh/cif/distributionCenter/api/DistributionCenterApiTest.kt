package com.hellofresh.cif.distributionCenter.api

import com.auth0.jwt.JWT
import com.auth0.jwt.JWTCreator
import com.auth0.jwt.algorithms.Algorithm
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.distributionCenter.JwtConfiguration
import com.hellofresh.cif.distributionCenter.api.generated.model.DayOfWeek.FRIDAY
import com.hellofresh.cif.distributionCenter.api.generated.model.DayOfWeek.MONDAY
import com.hellofresh.cif.distributionCenter.api.generated.model.DayOfWeek.valueOf
import com.hellofresh.cif.distributionCenter.api.generated.model.DcConfigurationRequest
import com.hellofresh.cif.distributionCenter.api.generated.model.DcConfigurationResponse
import com.hellofresh.cif.distributionCenter.api.generated.model.UpdateDcConfigurationMultiParamRequest
import com.hellofresh.cif.distributionCenter.api.generated.model.UpdateDcConfigurationRequest
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.distributionCenter.repo.DcConfiguration
import com.hellofresh.cif.distributionCenter.service.DistributionCenterService
import io.ktor.client.request.header
import io.ktor.client.request.request
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType.Application
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.auth.AuthScheme
import io.ktor.http.auth.HttpAuthHeader
import io.ktor.server.testing.testApplication
import io.mockk.coEvery
import io.mockk.mockk
import java.time.DayOfWeek
import java.time.Duration
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlin.time.toKotlinDuration
import kotlinx.coroutines.runBlocking

private val jwtSecret = UUID.randomUUID().toString()

class DistributionCenterApiTest {
    private val dcServiceMock: DistributionCenterService = mockk(relaxed = true)
    private val authorEmail = "<EMAIL>"

    @Test
    fun `returns 401 if no auth provided`() {
        // given
        val newDcConfigRequest = DcConfigurationRequest(
            "VE",
            "DACH",
            FRIDAY,
            "UTC",
            enabled = true,
            hasCleardown = true,
            cleardownDay = MONDAY,
            brands = listOf("Hellofresh", "EveryPlate"),
        )
        val jsonBody = objectMapper.writeValueAsString(newDcConfigRequest)

        // when
        val response = callDcConfigResource("/dc/VE", HttpMethod.Put, jsonBody, null)

        // then
        assertEquals(HttpStatusCode.Unauthorized, response.status)
    }

    @Test
    fun `returns all the dcs`() {
        // given
        val newDcConfig = DcConfiguration(
            "VE", DayOfWeek.FRIDAY, DayOfWeek.MONDAY, "DACH", ZoneId.of("UTC"),
            globalDc = null, enabled = true, hasCleardown = true, "<EMAIL>",
            scheduledClearDownTime = LocalTime.now(), wmsType = WmsSystem.UNRECOGNIZED,
            poCutoffTime = LocalTime.now(), brands = listOf("Hellofresh", "EveryPlate"),
        )

        coEvery {
            dcServiceMock.getAllDcs("DACH")
        } returns listOf(newDcConfig)

        // when
        val response = callDcConfigResource("/dc?country=DE", HttpMethod.Get, null, authorEmail)

        // then
        val dcConfigResponse = runBlocking {
            objectMapper.readValue(
                response.bodyAsText(),
                object : TypeReference<List<DcConfigurationResponse>>() {},
            )
        }

        // then
        assertEquals(HttpStatusCode.OK, response.status)
        assertEquals(1, dcConfigResponse.size)
        dcConfigResponse[0].apply {
            assertEquals(newDcConfig.dcCode, dcCode)
            assertEquals(newDcConfig.cleardown?.name, cleardownDay?.name)
            assertEquals(newDcConfig.productionStart.name, productionStartDay.name)
            assertEquals(newDcConfig.enabled, enabled)
            assertEquals(newDcConfig.hasCleardown, hasCleardown)
            assertEquals(newDcConfig.market, market)
            assertEquals(newDcConfig.zoneId.id, zoneId)
            assertEquals(newDcConfig.brands, brands)
        }
    }

    @Test
    fun `returns empty list when there is no dc exist`() {
        coEvery {
            dcServiceMock.getAllDcs("DACH")
        } returns emptyList()

        // when
        val response = callDcConfigResource("/dc?country=DE", HttpMethod.Get, null, authorEmail)

        // then
        val dcConfigResponse = runBlocking {
            val responseBody = response.bodyAsText()
            if (responseBody.isNotEmpty()) {
                objectMapper.readValue(
                    responseBody,
                    object : TypeReference<List<DcConfigurationResponse>>() {},
                )
            } else {
                emptyList()
            }
        }

        // then
        assertEquals(HttpStatusCode.NoContent, response.status)
        assertTrue(dcConfigResponse.isEmpty())
    }

    @Test
    fun `throws country not found exception when the input country is invalid`() {
        // when
        val response = callDcConfigResource("/dc?country=INVALID_COUNTRY", HttpMethod.Get, null, authorEmail)
        // then
        runBlocking {
            assertEquals(HttpStatusCode.NotFound, response.status)
            assertEquals("""{"reason":"Country INVALID_COUNTRY not found"}""", response.bodyAsText())
        }
    }

    @Test
    fun `returns 400 Bad Request if input is malformed`() {
        val response = callDcConfigResource("/dc/VE", HttpMethod.Put, "garble", "a@b.c")
        // then
        assertEquals(HttpStatusCode.BadRequest, response.status)
    }

    @Test
    fun `updates the DC cleardown day`() {
        // given
        val dcCode = "BV"
        val authorEmail = "hello@fresh"
        val expectedDcUpdate = DcConfiguration(
            dcCode,
            DayOfWeek.MONDAY,
            DayOfWeek.valueOf(FRIDAY.name),
            "DACH",
            ZoneOffset.UTC,
            globalDc = null,
            enabled = true,
            hasCleardown = true,
            authorEmail,
            LocalTime.now(),
            wmsType = WmsSystem.UNRECOGNIZED,
            poCutoffTime = LocalTime.now(),
        )
        coEvery {
            dcServiceMock.updateCleardownDay(any(), any(), any())
        } returns expectedDcUpdate
        val updateDcConfigurationRequest = UpdateDcConfigurationRequest(
            valueOf(expectedDcUpdate.cleardown!!.name),
        )
        val jsonBody = objectMapper.writeValueAsString(updateDcConfigurationRequest)

        // when
        val response = callDcConfigResource("/dc/$dcCode", HttpMethod.Put, jsonBody, authorEmail)
        val dcConfigResponse = runBlocking {
            objectMapper.readValue(response.bodyAsText(), DcConfiguration::class.java)
        }

        // then
        assertEquals(HttpStatusCode.OK, response.status)
        assertEquals(expectedDcUpdate, dcConfigResponse)
    }

    @Test
    fun `updates the cleardown day, production start, enabled, hasCleardown dc fields`() {
        // given
        val dcCode = "BV"
        val authorEmail = "hello@fresh"
        val expectedDcUpdate = DcConfiguration(
            dcCode,
            DayOfWeek.MONDAY,
            DayOfWeek.FRIDAY,
            "DACH",
            ZoneOffset.UTC,
            globalDc = null,
            enabled = true,
            hasCleardown = true,
            authorEmail,
            LocalTime.now(),
            wmsType = WmsSystem.UNRECOGNIZED,
            poCutoffTime = LocalTime.now(),
        )
        coEvery {
            dcServiceMock.updateDc(any())
        } returns expectedDcUpdate
        val updateDcConfigurationMultiParamsRequest = UpdateDcConfigurationMultiParamRequest(
            valueOf(expectedDcUpdate.cleardown!!.name),
            valueOf(expectedDcUpdate.productionStart.name),
            expectedDcUpdate.enabled,
            expectedDcUpdate.hasCleardown,
            LocalTime.now().toString()
        )
        val jsonBody = objectMapper.writeValueAsString(updateDcConfigurationMultiParamsRequest)

        // when
        val response = callDcConfigResource("/dc/$dcCode/v1", HttpMethod.Put, jsonBody, authorEmail)
        val dcConfigResponse = runBlocking {
            objectMapper.readValue(response.bodyAsText(), DcConfiguration::class.java)
        }

        // then
        assertEquals(HttpStatusCode.OK, response.status)
        assertEquals(expectedDcUpdate, dcConfigResponse)
    }

    private fun callDcConfigResource(
        path: String,
        method: HttpMethod,
        jsonBody: String?,
        userEmail: String?
    ): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            application(
                RoutingModule().dcConfigRoutingModule(
                    dcServiceMock,
                    JwtConfiguration(
                        secret = jwtSecret,
                        realm = "test",
                        issuer = "testIssuer",
                        clientId = "testClientId",
                        jwksURI = "https://test.com"
                    ),
                    Duration.ofSeconds(3).toKotlinDuration(),
                ),
            )
            response = client.request(path) {
                this.method = method
                jsonBody?.let { setBody(it) }
                userEmail?.let {
                    header(
                        HttpHeaders.Authorization,
                        HttpAuthHeader.Single(
                            AuthScheme.Bearer,
                            buildJwtToken {
                                this.withClaim("email", it)
                            },
                        ),
                    )
                }
                header(HttpHeaders.ContentType, Application.Json.toString())
            }
        }
        return response
    }

    private fun buildJwtToken(jwtBuilderConf: JWTCreator.Builder.() -> Unit) =
        JWT.create().withClaim("sub", UUID.randomUUID().toString())
            .also(jwtBuilderConf)
            .sign(Algorithm.HMAC256(jwtSecret))

    companion object {
        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()
    }
}
