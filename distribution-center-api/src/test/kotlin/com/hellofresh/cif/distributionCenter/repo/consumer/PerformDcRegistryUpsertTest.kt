package com.hellofresh.cif.distributionCenter.repo.consumer

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.distribution_center_api.schema.config.Tables.DISTRIBUTION_CENTER
import com.hellofresh.proto.stream.scm.registry.dc.v1beta1.DistributionCenter
import com.hellofresh.proto.stream.scm.registry.dc.v1beta1.DistributionCenter.Market
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.Instant
import java.util.Optional
import java.util.concurrent.Executors
import kotlin.test.BeforeTest
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.common.TopicPartition
import org.apache.kafka.common.header.internals.RecordHeaders
import org.apache.kafka.common.record.TimestampType.CREATE_TIME
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

internal class PerformDcRegistryUpsertTest {
    private val performDcRegistryUpsert = PerformDcRegistryUpsert(dsl, "testemailaddress")
    private val expectedProductionStart = "MONDAY"
    private val expectedCleardown = "FRIDAY"

    @BeforeTest
    fun cleanup() {
        dsl.deleteFrom(DISTRIBUTION_CENTER).execute()
    }

    @Test
    fun `insert new distribution center records`() {
        val dcRecords = (0..2).map { i -> generateMockDcData(i) }
        val consumerRecords = ConsumerRecords(mapOf(TopicPartition("test", 0) to dcRecords))
        runBlocking { performDcRegistryUpsert(consumerRecords) }
        val records = dsl.fetch(DISTRIBUTION_CENTER)

        assertEquals(1, records.size)
        records.forEachIndexed { i, dc ->
            val expectedDc = dcRecords[i].value()!!
            assertEquals(expectedDc.code, dc.dcCode)
            assertEquals(
                expectedDc.marketList.first().code.toString().uppercase(),
                dc.market,
            )
            assertEquals(expectedProductionStart, dc.productionStart)
            assertEquals(expectedCleardown, dc.cleardown)
            assertEquals(expectedDc.timeZone, dc.zoneId)
            assertFalse(dc.published)
        }
    }

    @ParameterizedTest
    @CsvSource("true", "false")
    fun `shouldn't update enabled state of the distribution center record`(initialState: String) {
        // a record exists
        val enabled = if (initialState == "true") true else false
        val consumerRecords = ConsumerRecords(mapOf(TopicPartition("test", 0) to listOf(generateMockDcData(1))))
        runBlocking { performDcRegistryUpsert(consumerRecords) }
        var records = dsl.fetch(DISTRIBUTION_CENTER)
        // new DC is always disabled
        assertFalse(records.first().enabled)

        // set the state as per the test param
        dsl.update(DISTRIBUTION_CENTER).set(DISTRIBUTION_CENTER.ENABLED, enabled).execute()
        records = dsl.fetch(DISTRIBUTION_CENTER)
        assertEquals(enabled, records.first().enabled)

        // update the record from db
        val updatedDcRecord = generateMockDcData(0, "Europe/Munich", DistributionCenter.WmsSystem.WMS_SYSTEM_HIGH_JUMP)
        runBlocking {
            performDcRegistryUpsert(
                ConsumerRecords(mapOf(TopicPartition("test", 0) to listOf(updatedDcRecord))),
            )
        }

        // enabled state remain same
        records = dsl.fetch(DISTRIBUTION_CENTER)
        assertEquals(enabled, records.first().enabled)
    }

    @Test
    fun `should update the existing distribution center records (market, timezone)`() {
        val dcRecord = generateMockDcData(0)
        val consumerRecords = ConsumerRecords(mapOf(TopicPartition("test", 0) to listOf(dcRecord)))
        runBlocking { performDcRegistryUpsert(consumerRecords) }
        val updatedDcRecord = generateMockDcData(0, "Europe/Munich", DistributionCenter.WmsSystem.WMS_SYSTEM_HIGH_JUMP)
        val updatedConsumerRecords = ConsumerRecords(mapOf(TopicPartition("test", 0) to listOf(updatedDcRecord)))
        runBlocking { performDcRegistryUpsert(updatedConsumerRecords) }
        val records = dsl.fetch(DISTRIBUTION_CENTER)

        assertEquals(1, records.size)
        records.forEach { dc ->
            val expectedDc = updatedDcRecord.value()!!
            assertEquals(expectedDc.code, dc.dcCode)
            assertEquals(
                expectedDc.marketList.first().code.toString().uppercase(),
                dc.market,
            )
            assertEquals(expectedProductionStart, dc.productionStart)
            assertEquals(expectedCleardown, dc.cleardown)
            assertEquals(expectedDc.timeZone, dc.zoneId)
            Assertions.assertFalse(dc.published)
            assertEquals(WmsSystem.WMS_SYSTEM_HIGH_JUMP.value, dc.wmsType)
        }
    }

    private fun generateMockDcData(
        i: Int,
        timeZoneParam: String = "Europe/Berlin",
        wmsSystemParam: DistributionCenter.WmsSystem = DistributionCenter.WmsSystem.WMS_SYSTEM_FCMS,
        recordTimestampIncrement: Long = i.toLong()
    ): ConsumerRecord<String, DistributionCenter> {
        val value = DistributionCenter.newBuilder().apply {
            code = "VE"
            timeZone = timeZoneParam
            wmsSystem = wmsSystemParam
        }.addMarket(
            Market.newBuilder().apply {
                code = "dach"
            }.build(),
        ).build()

        return ConsumerRecord(
            "test", 0, i.toLong(),
            Instant.now().toEpochMilli() + recordTimestampIncrement,
            CREATE_TIME, 1, 1, "test-topic-message-key",
            value,
            RecordHeaders(), Optional.empty(),
        )
    }

    companion object {
        private val dataSource = getMigratedDataSource()
        private val dbConfiguration = DefaultConfiguration()
            .apply {
                setSQLDialect(SQLDialect.POSTGRES)
                setDataSource(dataSource)
                setExecutor(Executors.newSingleThreadExecutor())
            }
        private val dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
    }
}
