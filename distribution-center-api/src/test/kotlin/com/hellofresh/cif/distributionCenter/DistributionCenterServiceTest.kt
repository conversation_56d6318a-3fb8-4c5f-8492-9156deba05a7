package com.hellofresh.cif.distributionCenter

import com.hellofresh.cif.distributionCenter.model.DcParamRequest
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.distributionCenter.repo.DcConfiguration
import com.hellofresh.cif.distributionCenter.repo.DcRepository
import com.hellofresh.cif.distributionCenter.service.DistributionCenterService
import io.mockk.coEvery
import io.mockk.mockk
import java.time.DayOfWeek.FRIDAY
import java.time.DayOfWeek.MONDAY
import java.time.LocalTime
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking

class DistributionCenterServiceTest {
    private val dcRepoMock: DcRepository = mockk()
    private val dcService = DistributionCenterService(dcRepoMock)

    @Test
    fun `updates a DC with a new cleardown day`() {
        // given
        val dcCode = "VE"
        val cleardown = FRIDAY
        val author = "email@"
        val dcConfig = DcConfiguration(
            dcCode,
            MONDAY,
            cleardown,
            "DACH",
            ZoneOffset.UTC,
            globalDc = null,
            enabled = true,
            hasCleardown = true,
            author,
            LocalTime.now(),
            wmsType = WmsSystem.UNRECOGNIZED,
            poCutoffTime = LocalTime.now(),
        )

        coEvery {
            dcRepoMock.updateCleardown(any(), any(), any())
        } returns dcConfig

        // when
        val result = runBlocking { dcService.updateCleardownDay(dcCode, cleardown, author) }

        // then
        assertEquals(dcCode, result?.dcCode)
        assertEquals(author, result?.lastUpdatedByEmail)
        assertEquals(cleardown, result?.cleardown)
        assertEquals(WmsSystem.UNRECOGNIZED, result?.wmsType)
    }

    @Test
    fun `updates a DC with a new cleardown day, production start, enabled, hasCleardown`() {
        // given
        val dcCode = "VE"
        val cleardown = FRIDAY
        val productionStart = MONDAY
        val enabled = true
        val hasCleardown = true
        val author = "email@"
        val dcConfig = DcConfiguration(
            dcCode,
            MONDAY,
            cleardown,
            "DACH",
            ZoneOffset.UTC,
            globalDc = null,
            enabled = enabled,
            hasCleardown = hasCleardown,
            author,
            LocalTime.now(),
            wmsType = WmsSystem.UNRECOGNIZED,
            poCutoffTime = LocalTime.now(),
        )

        coEvery {
            dcRepoMock.updateDc(any())
        } returns dcConfig

        // when
        val result = runBlocking {
            dcService.updateDc(
                DcParamRequest(
                    dcCode = dcCode,
                    cleardown = cleardown,
                    productionStart = productionStart,
                    enabled = enabled,
                    hasCleardown = hasCleardown,
                    lastUpdatedByEmail = author,
                    LocalTime.now().truncatedTo(ChronoUnit.SECONDS),
                    WmsSystem.UNRECOGNIZED,
                    poCutoffTime = LocalTime.now(),
                ),
            )
        }

        // then
        assertEquals(dcCode, result?.dcCode)
        assertEquals(author, result?.lastUpdatedByEmail)
        assertEquals(cleardown, result?.cleardown)
        assertEquals(productionStart, result?.productionStart)
        assertTrue(result!!.enabled)
        assertTrue(result.hasCleardown)
    }

    @Test
    fun `returns null when attempting to update a non-existent DC`() {
        coEvery {
            dcRepoMock.updateCleardown(any(), any(), any())
        } returns null

        // when
        val result = runBlocking { dcService.updateCleardownDay("VE", FRIDAY, "a@b") }

        // then
        assertNull(result)
    }

    @Test
    fun `returns all the dcs`() {
        val newDcConfig =
            DcConfiguration(
                "VE",
                MONDAY,
                FRIDAY,
                "mkt",
                ZoneOffset.UTC,
                null,
                true,
                true,
                "hello@fresh",
                LocalTime.now(),
                wmsType = WmsSystem.UNRECOGNIZED,
                poCutoffTime = LocalTime.now(),
            )

        coEvery {
            dcRepoMock.getAllDcs("mkt")
        } returns listOf(newDcConfig)

        // when
        val result = runBlocking { dcService.getAllDcs("mkt") }

        // then
        assertEquals(listOf(newDcConfig), result)
    }
}
