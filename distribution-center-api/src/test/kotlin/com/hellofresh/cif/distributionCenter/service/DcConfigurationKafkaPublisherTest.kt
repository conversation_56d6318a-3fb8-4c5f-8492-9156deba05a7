package com.hellofresh.cif.distributionCenter.service

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfigurationTopicValue
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.distributionCenter.repo.DcConfiguration
import com.hellofresh.cif.distributionCenter.repo.DcRepository
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.coVerifyOrder
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.DayOfWeek.FRIDAY
import java.time.DayOfWeek.MONDAY
import java.time.LocalTime
import java.time.ZoneOffset
import java.util.concurrent.Future
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.clients.producer.RecordMetadata

class DcConfigurationKafkaPublisherTest {
    private val kafkaProducerMock: KafkaProducer<String, DistributionCenterConfigurationTopicValue> = mockk(
        relaxed = true
    )
    private val dcRepoMock: DcRepository = mockk()
    private val dcConfigurationKafkaPublisher = DcConfigurationKafkaPublisher(kafkaProducerMock, dcRepoMock)

    @Test
    fun `should publish successfully a DC config`() = runBlocking {
        val localTime = LocalTime.now()
        val dcCode = "VE"
        val unpublishedDcConfig =
            DcConfiguration(
                dcCode,
                MONDAY,
                FRIDAY,
                "mkt",
                ZoneOffset.UTC,
                "GL",
                enabled = true,
                hasCleardown = true,
                "hello@fresh",
                localTime,
                wmsType = WmsSystem.UNRECOGNIZED,
                poCutoffTime = LocalTime.now()
            )
        coEvery { dcRepoMock.getUnpublishedDcConfigs() } returns listOf(unpublishedDcConfig)
        coEvery { dcRepoMock.updatePublishedFlag(dcCode, true) } returns Unit
        val slot1 = slot<ProducerRecord<String, DistributionCenterConfigurationTopicValue>>()
        every { kafkaProducerMock.send(capture(slot1)) } returns mockk<Future<RecordMetadata>>(relaxed = true)

        // when
        dcConfigurationKafkaPublisher.publishDcConfigs()

        // then
        coVerifyOrder {
            kafkaProducerMock.send(capture(slot1))
        }

        val dcRecordPublished1 = slot1.captured.value()

        assertEquals(dcCode, slot1.captured.key())

        // v3
        assertEquals(unpublishedDcConfig.cleardown, dcRecordPublished1.cleardown)
        assertEquals(unpublishedDcConfig.zoneId, dcRecordPublished1.zoneId)
        assertEquals(unpublishedDcConfig.globalDc, dcRecordPublished1.globalDc)
        assertEquals(unpublishedDcConfig.market, dcRecordPublished1.market)
        assertEquals(unpublishedDcConfig.hasCleardown, dcRecordPublished1.hasCleardown)
        assertEquals(unpublishedDcConfig.enabled, dcRecordPublished1.enabled)
        assertEquals(unpublishedDcConfig.enabled, dcRecordPublished1.enabled)
        assertEquals(unpublishedDcConfig.wmsType, dcRecordPublished1.wmsType)
        assertEquals(localTime.toString(), dcRecordPublished1.scheduledClearDownTime)
    }

    @Test
    fun `should not publish DC configs if there is no unpublished one`() {
        // given
        coEvery { dcRepoMock.getUnpublishedDcConfigs() } returns emptyList()

        // when
        runBlocking { dcConfigurationKafkaPublisher.publishDcConfigs() }

        // then
        verify(exactly = 0) { kafkaProducerMock.send(any()) }
        coVerify(exactly = 0) { dcRepoMock.updatePublishedFlag(any(), any()) }
    }
}
