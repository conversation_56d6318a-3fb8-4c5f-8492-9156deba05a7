package com.hellofresh.cif.distributionCenter.repo

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenter.model.DcParamRequest
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.distributionCenter.models.WmsSystem.WMS_SYSTEM_UNSPECIFIED
import com.hellofresh.cif.distributionCenter.models.WmsSystem.WMS_SYSTEM_WMS_LITE
import com.hellofresh.cif.distribution_center_api.schema.config.Tables.DISTRIBUTION_CENTER
import com.hellofresh.cif.distribution_center_api.schema.config.tables.records.DistributionCenterRecord
import com.hellofresh.cif.distribution_center_api.schema.public_.Tables.INVENTORY_PROCESSED_SNAPSHOTS
import com.hellofresh.cif.distribution_center_api.schema.public_.tables.records.InventoryProcessedSnapshotsRecord
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.DayOfWeek.FRIDAY
import java.time.DayOfWeek.MONDAY
import java.time.DayOfWeek.THURSDAY
import java.time.DayOfWeek.TUESDAY
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset.UTC
import java.time.temporal.ChronoUnit
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue
import kotlinx.coroutines.future.await
import kotlinx.coroutines.runBlocking
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.BeforeEach

class DcRepositoryImplTest {
    private val dcConfigRepo = DcRepositoryImpl(dsl)

    @BeforeEach
    fun cleanupDb() {
        dsl.deleteFrom(DISTRIBUTION_CENTER).execute()
        dsl.deleteFrom(INVENTORY_PROCESSED_SNAPSHOTS).execute()
    }

    @Test
    fun `should fetch a DC by code`() {
        // given
        val newDcConfigRecord = DistributionCenterRecord().apply {
            dcCode = "VE"
            cleardown = MONDAY.name
            productionStart = FRIDAY.name
            market = "mkt"
            zoneId = UTC.id
            hasCleardown = true
            enabled = true
            lastUpdatedByEmail = "hello@fresh"
            published = false
            createdAt = LocalDateTime.now(UTC)
            updatedAt = LocalDateTime.now(UTC)
            brands = arrayOf("brand1", "brand2")
        }
        dsl.batchInsert(newDcConfigRecord).execute()

        // when
        val dcConfig = runBlocking { dcConfigRepo.find(newDcConfigRecord.dcCode!!, null, null) }

        // then
        assertNotNull(dcConfig)
        assertEquals(newDcConfigRecord.dcCode, dcConfig.dcCode)
        assertEquals(newDcConfigRecord.market, dcConfig.market)
        assertEquals(newDcConfigRecord.zoneId, dcConfig.zoneId.id)
        assertEquals(newDcConfigRecord.cleardown, dcConfig.cleardown?.name)
        assertEquals(newDcConfigRecord.productionStart, dcConfig.productionStart.name)
        assertEquals(newDcConfigRecord.hasCleardown, dcConfig.hasCleardown)
        assertEquals(newDcConfigRecord.lastUpdatedByEmail, dcConfig.lastUpdatedByEmail)
    }

    @Test
    fun `should fetch a DC by code with inventory snapshot data in the last 7 days`() {
        // given
        val newDcConfigRecord = DistributionCenterRecord().apply {
            dcCode = "VE"
            cleardown = MONDAY.name
            productionStart = FRIDAY.name
            market = "mkt"
            zoneId = UTC.id
            hasCleardown = true
            enabled = true
            lastUpdatedByEmail = "hello@fresh"
            published = false
            createdAt = LocalDateTime.now(UTC)
            updatedAt = LocalDateTime.now(UTC)
            poCutoffTime = LocalTime.now()
            brands = arrayOf("brand1", "brand2")
        }

        val inventoryProcessedSnapshotRecord = InventoryProcessedSnapshotsRecord().apply {
            dcCode = newDcConfigRecord.dcCode
            snapshotId = UUID.randomUUID()
            snapshotTime = OffsetDateTime.now(UTC).minusDays(6)
        }

        val inventoryProcessedSnapshotRecord2 = InventoryProcessedSnapshotsRecord().apply {
            dcCode = newDcConfigRecord.dcCode
            snapshotId = UUID.randomUUID()
            snapshotTime = OffsetDateTime.now(UTC).minusDays(8)
        }

        dsl.batchInsert(
            newDcConfigRecord,
            inventoryProcessedSnapshotRecord,
            inventoryProcessedSnapshotRecord2,
        ).execute()

        // when
        val dcConfig = runBlocking { dcConfigRepo.find(newDcConfigRecord.dcCode!!, null, null) }

        // then
        assertNotNull(dcConfig)
        assertEquals(newDcConfigRecord.dcCode, dcConfig.dcCode)
        assertEquals(1, dcConfig.snapshotTimes.size)
        assertEquals(inventoryProcessedSnapshotRecord.snapshotId, dcConfig.snapshotTimes.first().snapshotId)
        assertEquals(
            inventoryProcessedSnapshotRecord.snapshotTime.truncatedTo(ChronoUnit.SECONDS),
            dcConfig.snapshotTimes.first().snapshotTime.truncatedTo(ChronoUnit.SECONDS),
        )
    }

    @Test
    fun `should fetch a DC by code with inventory snapshot data between provided dc local range`() {
        // given
        val dcZoneId = ZoneId.of("Australia/Sydney")
        val newDcConfigRecord = DistributionCenterRecord().apply {
            dcCode = "VE"
            cleardown = MONDAY.name
            productionStart = FRIDAY.name
            market = "mkt"
            zoneId = dcZoneId.id
            hasCleardown = true
            enabled = true
            lastUpdatedByEmail = "hello@fresh"
            published = false
            createdAt = LocalDateTime.now(UTC)
            updatedAt = LocalDateTime.now(UTC)
            brands = arrayOf("brand1", "brand2")
        }
        val inventoryProcessedSnapshotRecord = InventoryProcessedSnapshotsRecord().apply {
            dcCode = newDcConfigRecord.dcCode
            snapshotId = UUID.randomUUID()
            snapshotTime = OffsetDateTime.now(dcZoneId).minusDays(1)
        }
        val inventoryProcessedSnapshotRecord2 = InventoryProcessedSnapshotsRecord().apply {
            dcCode = newDcConfigRecord.dcCode
            snapshotId = UUID.randomUUID()
            snapshotTime = OffsetDateTime.now(dcZoneId).minusDays(2)
        }
        val inventoryProcessedSnapshotRecord3 = InventoryProcessedSnapshotsRecord().apply {
            dcCode = newDcConfigRecord.dcCode
            snapshotId = UUID.randomUUID()
            snapshotTime = OffsetDateTime.now(UTC).plusHours(1)
        }
        dsl.batchInsert(
            newDcConfigRecord,
            inventoryProcessedSnapshotRecord,
            inventoryProcessedSnapshotRecord2,
            inventoryProcessedSnapshotRecord3,
        ).execute()

        // when
        val dcConfig = runBlocking {
            dcConfigRepo.find(
                newDcConfigRecord.dcCode!!,
                LocalDateTime.now(UTC).minusDays(3),
                LocalDateTime.now(UTC),
            )
        }

        // then
        assertNotNull(dcConfig)
        assertEquals(newDcConfigRecord.dcCode, dcConfig.dcCode)
        assertEquals(2, dcConfig.snapshotTimes.size)
        assertEquals(
            setOf(
                inventoryProcessedSnapshotRecord.snapshotId,
                inventoryProcessedSnapshotRecord2.snapshotId,
            ),
            dcConfig.snapshotTimes.map { it.snapshotId }.toSet(),
        )
    }

    @Test
    fun `should fetch dc info even when there is no snapshots in range`() {
        // given
        val newDcConfigRecord = DistributionCenterRecord().apply {
            dcCode = "VE"
            cleardown = MONDAY.name
            productionStart = FRIDAY.name
            market = "mkt"
            zoneId = UTC.id
            hasCleardown = true
            enabled = true
            lastUpdatedByEmail = "hello@fresh"
            published = false
            createdAt = LocalDateTime.now(UTC)
            updatedAt = LocalDateTime.now(UTC)
            brands = arrayOf("brand1", "brand2")
        }
        val inventoryProcessedSnapshot = InventoryProcessedSnapshotsRecord().apply {
            dcCode = newDcConfigRecord.dcCode
            snapshotId = UUID.randomUUID()
            snapshotTime = OffsetDateTime.now(UTC).minusDays(1)
        }
        val inventoryProcessedSnapshot2 = InventoryProcessedSnapshotsRecord().apply {
            dcCode = newDcConfigRecord.dcCode
            snapshotId = UUID.randomUUID()
            snapshotTime = OffsetDateTime.now(UTC).minusDays(2)
        }

        dsl.batchInsert(
            newDcConfigRecord,
            inventoryProcessedSnapshot,
            inventoryProcessedSnapshot2,
        ).execute()

        // when
        val dcConfig = runBlocking {
            dcConfigRepo.find(
                newDcConfigRecord.dcCode!!,
                LocalDateTime.now(UTC).minusHours(1),
                LocalDateTime.now(UTC),
            )
        }

        // then
        assertNotNull(dcConfig)
        assertEquals(newDcConfigRecord.dcCode, dcConfig.dcCode)
        assertEquals(0, dcConfig.snapshotTimes.size)
    }

    @Test
    fun `should update the cleardown day for an existing DC and set the published flag to false`() {
        // given
        val dcCode = "BV"
        val newDcConfig =
            DcConfiguration(
                dcCode,
                MONDAY,
                FRIDAY,
                "mkt",
                UTC,
                null,
                true,
                true,
                "hello@fresh",
                LocalTime.now(),
                wmsType = WMS_SYSTEM_UNSPECIFIED,
                poCutoffTime = LocalTime.now(),
                brands = arrayListOf("brand1", "brand2"),
            )
        val updatedCleardownDay = THURSDAY
        val updatedByEmail = "hello2@fresh"
        runBlocking { create(newDcConfig) }
        val unrelatedDcConfig = newDcConfig.copy(dcCode = "XX", cleardown = TUESDAY)
        runBlocking { create(unrelatedDcConfig) }

        // when
        val dbDc = runBlocking { dcConfigRepo.find(dcCode, null, null) }
        runBlocking { dcConfigRepo.updateCleardown(dcCode, updatedCleardownDay, updatedByEmail) }
        val dbDcUpdate = dsl.fetchOne(DISTRIBUTION_CENTER, DISTRIBUTION_CENTER.DC_CODE.eq(dcCode))!!

        // then
        assertEquals(newDcConfig.cleardown, dbDc?.cleardown)
        assertEquals(updatedCleardownDay.name, dbDcUpdate.cleardown)
        assertEquals(updatedByEmail, dbDcUpdate.lastUpdatedByEmail)
        assertTrue(dbDcUpdate.updatedAt.isAfter(dbDcUpdate.createdAt))
        assertFalse(dbDcUpdate.published)

        // check unrelated DC cleardown is left unchanged
        val unrelatedDcConfigStored = dsl
            .fetchOne(DISTRIBUTION_CENTER, DISTRIBUTION_CENTER.DC_CODE.eq(unrelatedDcConfig.dcCode))!!
        assertEquals(unrelatedDcConfig.cleardown?.name, unrelatedDcConfigStored.cleardown)
    }

    @Test
    fun `should update the cleardown, production start, enabled, hasCleardown for an existing DC and set the published flag to false`() {
        // given
        val dcCode = "BV"
        val newDcConfig =
            DcConfiguration(
                dcCode,
                MONDAY,
                FRIDAY,
                "mkt",
                UTC,
                null,
                true,
                true,
                "hello@fresh",
                LocalTime.now(),
                wmsType = WMS_SYSTEM_WMS_LITE,
                poCutoffTime = LocalTime.now(),
                brands = arrayListOf("brand1", "brand2"),
            )
        val updatedCleardownDay = THURSDAY
        val updatedProductionStart = FRIDAY
        val updateEnabledFlag = false
        val updateHasCleardownFlag = false
        val updatedByEmail = "hello2@fresh"
        runBlocking { create(newDcConfig) }
        val unrelatedDcConfig = newDcConfig.copy(dcCode = "XX", cleardown = TUESDAY)
        runBlocking { create(unrelatedDcConfig) }

        // when
        val dbDc = runBlocking { dcConfigRepo.find(dcCode, null, null) }
        runBlocking {
            dcConfigRepo.updateDc(
                DcParamRequest(
                    dcCode,
                    updatedCleardownDay,
                    updatedProductionStart,
                    updateEnabledFlag,
                    updateHasCleardownFlag,
                    updatedByEmail,
                    LocalTime.now().truncatedTo(ChronoUnit.SECONDS),
                    null,
                    LocalTime.now(),
                ),
            )
        }
        val dbDcUpdate = dsl.fetchOne(DISTRIBUTION_CENTER, DISTRIBUTION_CENTER.DC_CODE.eq(dcCode))!!

        // then
        assertEquals(newDcConfig.cleardown, dbDc?.cleardown)
        assertEquals(updatedCleardownDay.name, dbDcUpdate.cleardown)
        assertEquals(updatedProductionStart.name, dbDcUpdate.productionStart)
        assertEquals(newDcConfig.wmsType.value, dbDcUpdate.wmsType)
        assertFalse(dbDcUpdate.enabled)
        assertFalse(dbDcUpdate.hasCleardown)
        assertEquals(updatedByEmail, dbDcUpdate.lastUpdatedByEmail)
        assertTrue(dbDcUpdate.updatedAt.isAfter(dbDcUpdate.createdAt))
        assertFalse(dbDcUpdate.published)

        // check unrelated DC cleardown is left unchanged
        val unrelatedDcConfigStored = dsl
            .fetchOne(DISTRIBUTION_CENTER, DISTRIBUTION_CENTER.DC_CODE.eq(unrelatedDcConfig.dcCode))!!
        assertEquals(unrelatedDcConfig.cleardown?.name, unrelatedDcConfigStored.cleardown)
    }

    @Test
    fun `should return null when attempting to update a non-existent DC`() {
        // when
        val result = runBlocking { dcConfigRepo.updateCleardown("XX", FRIDAY, "aa") }

        // then
        assertNull(result)
    }

    @Test
    fun `should fetch all DC configurations`() {
        // given
        val newDcConfigOne =
            DcConfiguration(
                "VE",
                MONDAY,
                FRIDAY,
                "mkt",
                UTC,
                "GL",
                true,
                true,
                "hello@fresh",
                LocalTime.now().truncatedTo(ChronoUnit.SECONDS),
                wmsType = WmsSystem.UNRECOGNIZED,
                poCutoffTime = LocalTime.now().truncatedTo(ChronoUnit.SECONDS),
            )
        val newDcConfigTwo =
            newDcConfigOne.copy(dcCode = "BV")
        runBlocking { create(newDcConfigOne) }
        runBlocking { create(newDcConfigTwo) }

        // when
        val dcConfigs = runBlocking { dcConfigRepo.getAllDcs("mkt") }

        // then
        assertEquals(2, dcConfigs.size)
        assertEquals(listOf(newDcConfigOne, newDcConfigTwo).sortedBy { it.dcCode }, dcConfigs.sortedBy { it.dcCode })
    }

    @Test
    fun `should return empty DC configurations if none exist`() {
        val dcConfigs = runBlocking { dcConfigRepo.getAllDcs("mkt") }
        assertTrue(dcConfigs.isEmpty())
    }

    @Test
    fun `should fetch all unpublished DC configurations`() {
        // given
        val newDcConfig =
            DcConfiguration(
                "VE",
                MONDAY,
                FRIDAY,
                "mkt",
                UTC,
                null,
                true,
                true,
                "hello@fresh",
                LocalTime.now().truncatedTo(ChronoUnit.SECONDS),
                wmsType = WmsSystem.UNRECOGNIZED,
                poCutoffTime = LocalTime.now().truncatedTo(ChronoUnit.SECONDS),
            )
        runBlocking { create(newDcConfig) }

        // when
        val unpublishedDcConfigs = runBlocking { dcConfigRepo.getUnpublishedDcConfigs() }

        // then
        assertEquals(listOf(newDcConfig), unpublishedDcConfigs)
    }

    @Test
    fun `updates the published flag for a DC Configuration`() {
        // given
        val newDcConfig =
            DcConfiguration(
                "VE",
                MONDAY,
                FRIDAY,
                "mkt",
                UTC,
                null,
                true,
                true,
                "hello@fresh",
                LocalTime.now().truncatedTo(ChronoUnit.SECONDS),
                wmsType = WmsSystem.UNRECOGNIZED,
                poCutoffTime = LocalTime.now().truncatedTo(ChronoUnit.SECONDS),
            )
        runBlocking { create(newDcConfig) }
        val dbRecord =
            dsl.fetchOne(DISTRIBUTION_CENTER, DISTRIBUTION_CENTER.DC_CODE.eq(newDcConfig.dcCode))!!
        val initiallyUpdatedAt = dbRecord.updatedAt
        val newPublishedFlag = !dbRecord.published
        // whenØ
        runBlocking { dcConfigRepo.updatePublishedFlag(newDcConfig.dcCode, newPublishedFlag) }

        // then
        val dbDcUpdate = dsl.fetchOne(DISTRIBUTION_CENTER, DISTRIBUTION_CENTER.DC_CODE.eq(newDcConfig.dcCode))
        assertEquals(newPublishedFlag, dbDcUpdate?.published ?: false)
        assertTrue(dbDcUpdate?.updatedAt?.isAfter(initiallyUpdatedAt) ?: false)
    }

    private suspend fun create(dcConfig: DcConfiguration) {
        dsl
            .insertInto(DISTRIBUTION_CENTER).set(
                DistributionCenterRecord().apply {
                    dcCode = dcConfig.dcCode
                    market = dcConfig.market
                    productionStart = dcConfig.productionStart.name
                    cleardown = dcConfig.cleardown?.name
                    zoneId = dcConfig.zoneId.id
                    globalDc = dcConfig.globalDc
                    enabled = dcConfig.enabled
                    published = false
                    hasCleardown = dcConfig.hasCleardown
                    lastUpdatedByEmail = dcConfig.lastUpdatedByEmail
                    scheduledCleardownTime = dcConfig.scheduledClearDownTime
                    wmsType = dcConfig.wmsType.value
                    poCutoffTime = dcConfig.poCutoffTime
                    brands = dcConfig.brands.toTypedArray()
                },
            ).executeAsync().await()
    }

    companion object {
        private val dataSource = getMigratedDataSource()
        private val dbConfiguration = DefaultConfiguration()
            .apply {
                setSQLDialect(SQLDialect.POSTGRES)
                setDataSource(dataSource)
                setExecutor(Executors.newSingleThreadExecutor())
            }
        private val dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
    }
}
