package com.hellofresh.cif.endToEndTest.db

import javax.sql.DataSource
import org.apache.logging.log4j.kotlin.Logging

class SkuSpecificationViewRepository(private val dataSource: DataSource) {

    fun waitForView() {
        waitFor(
            "No sku specification view record found",
            { refreshSkuSpecificationView() },
            {
                dataSource.connection.use { conn ->
                    conn.createStatement()
                        .executeQuery(" select 1 from sku_specification_view where id is not null").next()
                }
            },
        )
        logger.info("Sku specification view with Sku information ready")
    }

    private fun refreshSkuSpecificationView() {
        dataSource.connection.use { conn ->
            logger.info("Refreshing sku_specification_view")
            conn.createStatement()
                .execute("refresh materialized view concurrently sku_specification_view")
        }
    }

    companion object : Logging
}
