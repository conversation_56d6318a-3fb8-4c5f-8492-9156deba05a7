package com.hellofresh.cif.endToEndTest

const val TOPIC_SKU_DEMAND_FORECAST = "public.demand.sku-demand-forecast.v2"
const val TOPIC_PRIVATE_DISTRIBUTION_CENTER = "csku-inventory-forecast.intermediate.distribution-center.v4"
const val TOPIC_PRIVATE_SKU_SPECIFICATION = "csku-inventory-forecast.intermediate.sku-specification.v1"
const val TOPIC_PURCHASE_ORDERS = "public.supply.procurement.purchase-order.v1"
const val TOPIC_GOODS_RECEIVED_NOTES_TOPIC = "public.distribution-center.inbound.goods-received-note.v1"
const val TOPIC_DEMAND_FORECAST_TOPIC_NAME = "public.ordering.sku-inventory-demand-forecast.v1"
const val TOPIC_SQR_TOPIC_NAME = "public.ordering.supply-quantity-recommendation.v1"
const val TOPIC_SQR_DAILY_TOPIC_NAME = "public.ordering.supply-quantity-recommendation-daily.v1"
const val TOPIC_TRANSFER_ORDERS_TOPIC_NAME = "public.transfer-order.v1"
const val SEND_TIMEOUT = 5L
