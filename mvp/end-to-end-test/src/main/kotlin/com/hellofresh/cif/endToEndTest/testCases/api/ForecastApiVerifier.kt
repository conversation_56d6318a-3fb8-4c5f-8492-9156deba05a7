package com.hellofresh.cif.endToEndTest.testCases.api

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.endToEndTest.testCases.TestCase
import io.ktor.client.HttpClient
import io.ktor.client.engine.cio.CIO
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.plugins.defaultRequest
import io.ktor.client.request.accept
import io.ktor.client.request.get
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType.Application
import io.ktor.http.Parameters
import io.ktor.http.formUrlEncode
import java.time.Duration
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.apache.logging.log4j.kotlin.Logging
import org.awaitility.Awaitility
import org.awaitility.core.ConditionTimeoutException

private const val HTTP_CLIENT_REQUEST_TIMEOUT = 30000L
private const val ASSERTION_TIMEOUT_IN_SECONDS = 720L
private const val CHECK_INTERVAL = 5L
private val timeout: Duration = Duration.ofSeconds(ASSERTION_TIMEOUT_IN_SECONDS)
private const val DC_CODE_PARAM = "dcCode"
private const val WEEKS_PARAM = "weeks"
private const val SKU_CODE_PARAM = "skuCode"

class ForecastApiVerifier(
    forecastApiServer: String,
    forecastApiServerPort: Int,
    private val httpClient: HttpClient = buildHttpClient(forecastApiServer, forecastApiServerPort)
) : Logging {
    fun verify(testCase: TestCase) {
        try {
            Awaitility.await()
                .atMost(timeout)
                .pollInterval(Duration.ofSeconds(CHECK_INTERVAL))
                .until {
                    logger.info("Checking API Calculations")
                    runCatching {
                        assertForecastApiCalculationResults(testCase)
                    }.onFailure { logger.warn("Assertion error", it) }
                        .getOrDefault(false)
                }
        } catch (e: ConditionTimeoutException) {
            logger.warn("Failed to assert the forecast api calculation results.")
            throw AssertionError("Failed to assert the forecast api calculation results.", e)
        }
    }

    private fun assertForecastApiCalculationResults(testCase: TestCase) =
        runCatching {
            val params = getForecastApiParams(testCase)
            val calculationResults =
                runBlocking {
                    getDailyCalculations(params)
                }
            if (calculationResults.calculations.isNotEmpty() &&
                calculationResults.calculations.map {
                    it.date
                }.containsAll(testCase.expectedApiCalculations.calculations.map { it.date })
            ) {
                for (expectedCalculation in testCase.expectedApiCalculations.calculations) {
                    val actualCalculation = calculationResults.calculations.first { calculationApi ->
                        expectedCalculation.date == calculationApi.date
                    }

                    assertCalculations(expectedCalculation, actualCalculation)
                }
                true
            } else {
                false
            }
        }.onFailure { logger.warn("Forecast Api error", it) }
            .getOrDefault(false)

    private fun getForecastApiParams(testCase: TestCase): String {
        val weeks = testCase.expectedApiCalculations.calculations.map { it.week }.toSet()
        val params = Parameters.build {
            append(SKU_CODE_PARAM, testCase.testCskuCode)
            append(DC_CODE_PARAM, testCase.dcCode)
            weeks.forEach { append(WEEKS_PARAM, it) }
        }.formUrlEncode()
        return params
    }

    private fun assertCalculations(
        expectedCalculation: Calculation,
        actualCalculation: Calculation
    ) {
        assertEquals(expectedCalculation.skuId, actualCalculation.skuId)
        assertEquals(expectedCalculation.dailyNeed, actualCalculation.dailyNeed)
        assertEquals(expectedCalculation.closingStock, actualCalculation.closingStock)
        assertEquals(expectedCalculation.consumption, actualCalculation.consumption)
        assertEquals(expectedCalculation.inbound, actualCalculation.inbound)
        assertEquals(expectedCalculation.unusableStock, actualCalculation.unusableStock)
        assertEquals(expectedCalculation.usableStock, actualCalculation.usableStock)
        assertEquals(
            expectedCalculation.expectedInboundTransferOrdersQuantity,
            actualCalculation.expectedInboundTransferOrdersQuantity
        )
        assertEquals(
            expectedCalculation.expectedOutboundTransferOrdersQuantity,
            actualCalculation.expectedOutboundTransferOrdersQuantity
        )
        assertEquals(expectedCalculation.pos, actualCalculation.pos)
    }

    private suspend fun getDailyCalculations(params: String) =
        httpClient.get("/calculation/dailyView?$params")
            .let {
                objectMapper.readValue(it.bodyAsText(), CalculationResult::class.java)
            }

    companion object : Logging {
        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()
        private fun buildHttpClient(forecastApiServer: String, forecastApiServerPort: Int) = HttpClient(CIO) {
            expectSuccess = true
            install(HttpTimeout) {
                requestTimeoutMillis = HTTP_CLIENT_REQUEST_TIMEOUT
            }
            defaultRequest {
                accept(Application.Json)
                host = forecastApiServer
                port = forecastApiServerPort
            }
        }
    }
}
