package com.hellofresh.cif.endToEndTest.db

import com.hellofresh.cif.endToEndTest.testCases.TestCase
import java.time.LocalTime
import java.time.ZonedDateTime
import java.util.UUID
import javax.sql.DataSource
import org.postgresql.util.PGobject

class InventorySnapshotRepository(private val dataSource: DataSource) {

    @Suppress("MagicNumber")
    fun persistInventorySnapshot(testCase: TestCase) {
        val snapshotId = UUID.randomUUID()
        dataSource.connection.use { conn ->
            conn.prepareStatement(
                "insert into inventory_processed_snapshots(dc_code,snapshot_id,snapshot_time) values (?,?,?)"
            )
                .use {
                    it.setObject(1, testCase.dcCode)
                    it.setObject(2, snapshotId)
                    it.setObject(
                        3,
                        ZonedDateTime.of(
                            testCase.lastCleardownDate.minusDays(1).atTime(LocalTime.of(23, 20)),
                            testCase.zoneId,
                        ).toOffsetDateTime(),
                    )
                    it.execute()
                }
        }
        dataSource.connection.use { conn ->
            conn.prepareStatement(
                "insert into inventory_all_snapshots(dc_code,snapshot_id,snapshot_time,sku_id, value) values (?,?,?,?,?)"
            )
                .use {
                    it.setObject(1, testCase.dcCode)
                    it.setObject(2, snapshotId)
                    it.setObject(
                        3,
                        ZonedDateTime.of(
                            testCase.lastCleardownDate.minusDays(1).atTime(LocalTime.of(23, 20)),
                            testCase.zoneId,
                        ).toOffsetDateTime(),
                    )
                    it.setObject(4, testCase.testCskuId)
                    it.setObject(
                        5,
                        PGobject().apply {
                            type = "jsonb"
                            value = testCase.inventory
                        },
                    )
                    it.execute()
                }
        }
    }
}
