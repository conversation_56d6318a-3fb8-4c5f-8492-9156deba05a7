package com.hellofresh.cif.endToEndTest.testCases

import com.google.type.Decimal
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.endToEndTest.model.DataRecord
import com.hellofresh.cif.endToEndTest.testCases.api.Amount
import com.hellofresh.cif.endToEndTest.testCases.api.Calculation
import com.hellofresh.cif.endToEndTest.testCases.api.CalculationResult
import com.hellofresh.cif.sqr.SQRConfiguration
import com.hellofresh.dateUtil.models.toProtoDate
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.v1.SkuInventoryDemandForecastKey
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.v1.SkuInventoryDemandForecastVal
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.v1.SkuInventoryDemandForecastVal.UOM.UOM_UNIT
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.v1.SkuInventoryDemandForecastVal.WeekDate
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.v1.UOM
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendation.v1.SupplyQuantityRecommendationKey
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendation.v1.SupplyQuantityRecommendationVal
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyKey
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyVal
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyVal.PONumber
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyVal.PurchaseOrderData
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyVal.Quantity
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.random.Random

@Suppress("MagicNumber", "LongParameterList", "StringLiteralDuplication")
val happyPath = testCase {
    val testCskuId = UUID.randomUUID()
    val productionStart = DayOfWeek.FRIDAY
    val week = DcWeek(LocalDate.now(UTC), productionStart)
    val productionStartDate = week.getStartDateInDcWeek(productionStart, UTC)
    val cskuCode = getRandomTestCSKUCode()
    val dc = "VE"
    val sourceDc = "BX"
    val poNr = "2044DH107244"
    val transferOrderNr = "2531OET92464"
    val transferOrderId = UUID.randomUUID().toString()
    val poRev = "01"
    val supplierId = UUID.randomUUID()
    val zoneId = ZoneId.of("Europe/Berlin")

    val cskuCategory = "VPM"
    TestCase(
        name = "Happy Path",
        testCskuId = testCskuId,
        testCskuCode = cskuCode,
        date = productionStartDate,
        dcCode = dc,
        zoneId = zoneId,
        lastCleardownDate = productionStartDate,
        dcConfig = DataRecord(
            key = dc,
            value = """
                {"market":"DACH", "production_start":"$productionStart", "cleardown":"$productionStart",
                 "zone_id":"$zoneId", "enabled": true, "has_cleardown": true, "scheduled_clear_down_time": "00:00:00", "brands" : []}
            """.trimIndent(),
        ),
        sourceDcConfig = DataRecord(
            key = sourceDc,
            value = """
                {"market":"DACH", "production_start":"$productionStart", "cleardown":"$productionStart",
                 "zone_id":"$zoneId", "enabled": true, "has_cleardown": true, "scheduled_clear_down_time": "00:00:00", "brands" : []}
            """.trimIndent(),
        ),
        skuCodeToId = DataRecord(
            key = """ {"market": "DACH", "csku_code": "$cskuCode"}""",
            value = """"$testCskuId"""",
        ),
        skuSpecification = DataRecord(
            key = """ "$testCskuId" """,
            value = """
                {
                    "cooling_type": "Cooler (4C)",
                    "name": "End to End Test for $testCskuId",
                    "packaging": "Coolpouch",
                    "sku_code": "$cskuCode",
                    "category": "$cskuCategory",
                    "acceptable_code_life": 0,
                    "market" : "dach",
                    "lead_time" : 70,
                    "std_dev_lead_time" : 1,
                    "time_increment" : 7,
                    "fumigated" : null,
                    "service_level" : 1.88
                }
            """,
        ),
        // We set inventory at the day before of cleardown because that's where
        inventory = """
                {"inventory":[{ "qty":440,
                                "expiry_date":"${format(productionStartDate + 5)}",
                                "location":{"id":"SP-05-08 -4","type":"LOCATION_TYPE_STAGING","transportModuleId":null}
                              }
                             ]
                }
        """.trimMargin(),
        demand = listOf(
            DataRecord(
                key = testCskuId.toString(),
                value = """
                {
                    "dc": "$dc",
                    "date": "${format(productionStartDate)}",
                    "culinary_sku_id": "$testCskuId",
                    "culinary_sku_code": "$cskuCode",
                    "qty": 100,
                    "recipe_index": "2001",
                    "people_count": 2,
                    "pick_count": 3
                }
            """,
            ),
            DataRecord(
                key = testCskuId.toString(),
                value = """
                {
                    "dc": "$dc",
                    "date": "${format(productionStartDate + 1)}",
                    "culinary_sku_id": "$testCskuId",
                    "culinary_sku_code": "$cskuCode",
                    "qty": 50,
                    "recipe_index": "2001",
                    "people_count": 2,
                    "pick_count": 3
                }
            """,
            ),
        ),
        purchaseOrder = DataRecord(
            key = poNr,
            value = """
                {   "po_nr": "$poNr",
                    "po_rev": "$poRev",
                    "po_id": "a8a9d518-6e8f-41cf-9830-bbc62e1812ca",
                    "status": "STATE_APPROVED",
                    "send_time": "2023-11-03T15:52:49Z",
                    "update_time": "2023-11-02T20:10:43Z",
                    "dc_code": "$dc",
                    "expectedArrivalStartTime": "${(productionStartDate + 1).atTime(10, 0).atOffset(ZoneOffset.UTC)}",
                    "expectedArrivalEndTime": "${(productionStartDate + 1).atTime(12, 0).atOffset(ZoneOffset.UTC)}",
                    "supplier_id": "$supplierId",
                    "sku_id": "$testCskuId",
                    "qty": 300
                }
            """,
        ),
        transferOrder = DataRecord(
            key = transferOrderId,
            value = """
                {
                  "id": "$transferOrderId",
                  "sourceDcCode": "BX",
                  "destinationDcCode": "$dc",
                  "creatorEmail": "<EMAIL>",
                  "reasonText": "Regular",
                  "items": [
                    {
                      "id": "1dce8d58-fc80-4cb7-b923-a0565c27ce72",
                      "cskuCode": "PRO-10-122650-1",
                      "price": {
                        "currencyCode": "CAD",
                        "units": "58",
                        "nanos": 790000000
                      },
                      "totalPrice": {
                        "currencyCode": "CAD",
                        "units": "646",
                        "nanos": 689999999
                      },
                      "casePackaging": {
                        "size": {
                          "value": "0"
                        },
                        "unit": "UOM_UNIT"
                      },
                      "orderSize": 11,
                      "supplierId": "$supplierId",
                      "supplierCode": "29080",
                      "skuId": "$testCskuId",
                      "inventoryType": "INVENTORY_INPUT_TYPE_MANUAL",
                      "cskuName": "X3B- Curry Paste (2tbsp)",
                      "quantity": {
                        "value": "0"
                      }
                    }
                  ],
                  "totalPrice": {
                    "currencyCode": "CAD",
                    "units": "61012",
                    "nanos": -126901893
                  },
                  "status": "STATE_ORDERED",
                  "productionWeek": {
                    "year": ${week.year},
                    "week": ${week.week}
                  },
                  "createTime": "${(productionStartDate.plus(2)).atTime(10, 0).atOffset(ZoneOffset.UTC)}",
                  "updateTime": "${(productionStartDate.plus(2)).atTime(11, 0).atOffset(ZoneOffset.UTC)}",
                  "pickupStartTime": "${(productionStartDate.plus(2)).atTime(10, 0).atOffset(ZoneOffset.UTC)}",
                  "pickupEndTime": "${(productionStartDate.plus(2)).atTime(11, 0).atOffset(ZoneOffset.UTC)}",
                  "deliveryStartTime": "${(productionStartDate.plus(2)).atTime(10, 0).atOffset(ZoneOffset.UTC)}",
                  "deliveryEndTime": "${(productionStartDate.plus(2)).atTime(11, 0).atOffset(ZoneOffset.UTC)}",
                  "transferOrderNumber": "$transferOrderNr",
                  "orderItemsChangeReason": {
                    "key": "TRANSPORT_SCHEDULE_CHANGE",
                    "value": "Error in Ordering"
                  },
                  "regionCode": "DE",
                  "sourceDcName": "18Wheels - Ontario",
                  "shippingMethod": "Freight on board",
                  "marketCode": "DE",
                  "sentTime": "2025-07-21T13:25:22.850959Z",
                  "shipping": {
                    "address": {
                      "regionCode": "CA",
                      "postalCode": "L4W 2T6",
                      "administrativeArea": "Ontario",
                      "locality": "Mississauga",
                      "addressLines": [
                        "5491",
                        "Timberlea Boulevard"
                      ],
                      "organization": "CA - YYZ2"
                    },
                    "method": "Freight on board"
                  },
                  "version": 3
                }

            """,
        ),
        goodsReceivedNote = DataRecord(
            key = testCskuId.toString(),
            value = """
                {
                   "po_ref": "$poNr",
                   "quantity": 200
                }
            """.trimIndent(),
        ),
        supplier = DataRecord(
            key = supplierId.toString(),
            value = """
                {
                    "name": "Supplier CO"
                }
            """.trimIndent(),
        ),
        calculationExpected = listOf(
            DataRecord(
                key = """{"csku_id": "$testCskuId", "dc_code": "$dc", "date": "${format(productionStartDate)}"}""",
                value = """
                    {
                        "expired": "0",
                        "opening_stock": "440",
                        "present": "440",
                        "actual_inbound": "200",
                        "actual_inbound_po": "$poNr",
                        "expected_inbound": "0",
                        "expected_inbound_po": "",
                        "demanded": "100",
                        "closing_stock": "540",
                        "daily_needs": "0",
                        "production_week": "$week"
                    }
            """,
            ),
            DataRecord(
                key = """{"csku_id": "$testCskuId", "dc_code": "$dc", "date": "${format(productionStartDate + 1)}"}""",
                value = """
                    {
                        "expired": "340",
                        "opening_stock": "200",
                        "present": "540",
                        "actual_inbound": "0",
                        "actual_inbound_po": "",
                        "expected_inbound": "300",
                        "expected_inbound_po": "$poNr",
                        "demanded": "50",
                        "closing_stock": "150",
                        "daily_needs": "0",
                        "production_week": "$week"
                    }
            """,
            ),
        ),
        proProdCalculationExpected = listOf(
            DataRecord(
                key = """{"csku_id": "$testCskuId", "dc_code": "$dc", "date": "${format(productionStartDate)}"}""",
                value = """
                    {
                        "expired": "0",
                        "opening_stock": "440",
                        "present": "440",
                        "actual_inbound": "200",
                        "actual_inbound_po": "$poNr",
                        "expected_inbound": "0",
                        "expected_inbound_po": "",
                        "demanded": "50",
                        "closing_stock": "590",
                        "daily_needs": "0",
                        "production_week": "$week"
                    }
            """,
            ),
            DataRecord(
                key = """{"csku_id": "$testCskuId", "dc_code": "$dc", "date": "${format(productionStartDate + 1)}"}""",
                value = """
                    {
                        "expired": "390",
                        "opening_stock": "200",
                        "present": "590",
                        "actual_inbound": "0",
                        "actual_inbound_po": "",
                        "expected_inbound": "300",
                        "expected_inbound_po": "$poNr",
                        "demanded": "0",
                        "closing_stock": "200",
                        "daily_needs": "0",
                        "production_week": "$week"
                    }
            """,
            ),
        ),
        expectedProtobufBulkOutput = mapOf(
            skuInventoryBulkKVPair(
                cskuId = testCskuId,
                dcCode = dc,
                date = productionStartDate,
                forecastedDemandedQty = 100,
                forecastedNeededQty = 0,
                productionWeekStartStock = 0,
            ),
            skuInventoryBulkKVPair(
                cskuId = testCskuId,
                dcCode = dc,
                date = productionStartDate + 1,
                forecastedDemandedQty = 50,
                forecastedNeededQty = 0,
                productionWeekStartStock = 0,
            ),
        ),
        expectedProtobufSqr = mapOf(
            supplyQuantityRecommendationPair(
                skuId = testCskuId,
                dcCode = dc,
                dcWeek = week,
                aggregatedDemand = 150,
                inventoryRollover = 440,
                unusableStock = 340,
                inbound = 200,
                incomingPos = 300,
            ),
            supplyQuantityRecommendationPair(
                skuId = testCskuId,
                dcCode = dc,
                dcWeek = ProductionWeek(week.toString(), productionStart, UTC).plusWeeks(1).dcWeek,
                aggregatedDemand = 0,
                inventoryRollover = 150,
                unusableStock = 0,
                inbound = 0,
                incomingPos = 0,
            ),
        ),
        expectedProtobufSqrDaily = mapOf(
            supplyQuantityRecommendationDailyPair(
                skuId = testCskuId,
                dcCode = dc,
                dcWeek = week,
                dateParam = productionStartDate,
                aggregatedDemand = 100,
                inventoryRollover = 440,
                bufferDetails = 110,
                sqr = 0,
            ),
        ),
        expectedApiCalculations = CalculationResult(
            calculations = listOf(
                Calculation(
                    skuId = testCskuId.toString(),
                    skuCode = cskuCode,
                    unusableStock = 0,
                    usableStock = 440,
                    inbound = Amount(amount = 200),
                    consumption = 100,
                    dailyNeed = 0,
                    closingStock = 540,
                    week = "$week",
                    date = format(productionStartDate),
                    pos = listOf(poNr),
                    expectedInboundTransferOrdersQuantity = 0,
                    expectedOutboundTransferOrdersQuantity = 0,
                ),
                Calculation(
                    skuId = testCskuId.toString(),
                    skuCode = cskuCode,
                    unusableStock = 340,
                    usableStock = 200,
                    inbound = Amount(amount = 0),
                    consumption = 50,
                    dailyNeed = 0,
                    closingStock = 150,
                    week = "$week",
                    date = format(productionStartDate + 1),
                    pos = listOf(poNr),
                    expectedInboundTransferOrdersQuantity = 0,
                    expectedOutboundTransferOrdersQuantity = 0,
                )
            ),
        ),
    )
}

@Suppress("LongParameterList")
private fun skuInventoryBulkKVPair(
    cskuId: UUID,
    dcCode: String,
    date: LocalDate,
    forecastedDemandedQty: Int,
    forecastedNeededQty: Int,
    productionWeekStartStock: Int,
    productionStart: DayOfWeek = DayOfWeek.FRIDAY,
): Pair<SkuInventoryDemandForecastKey, SkuInventoryDemandForecastVal> {
    val key = SkuInventoryDemandForecastKey.newBuilder().apply {
        this.skuId = cskuId.toString()
        this.distributionCenterBobCode = dcCode
        this.date = date.toProtoDate()
    }.build()

    val value = SkuInventoryDemandForecastVal.newBuilder().apply {
        this.forecastedDemandedQty = forecastedDemandedQty.toProtoDecimal()
        this.forecastedNeededQty = forecastedNeededQty.toProtoDecimal()
        this.productionWeekStartStock = productionWeekStartStock.toProtoDecimal()
        this.unit = UOM_UNIT
        setProductionWeek(
            WeekDate.newBuilder().apply {
                val dcWeek = DcWeek(date, productionStart)
                year = dcWeek.year
                week = dcWeek.week
            },
        )
    }.build()

    return key to value
}

@Suppress("LongParameterList")
private fun supplyQuantityRecommendationPair(
    dcCode: String,
    dcWeek: DcWeek,
    skuId: UUID,
    aggregatedDemand: Int,
    inventoryRollover: Int,
    inbound: Int,
    incomingPos: Int,
    unusableStock: Int,
) = run {
    val key = SupplyQuantityRecommendationKey.newBuilder()
        .setDistributionCenterBobCode(dcCode)
        .setProductionWeek(
            com.hellofresh.proto.stream.ordering.supplyQuantityRecommendation.v1.ProductionWeek.newBuilder()
                .setWeek(dcWeek.week)
                .setYear(dcWeek.year),
        ).setSkuId(skuId.toString())

    val value = SupplyQuantityRecommendationVal.newBuilder()
        .setDistributionCenterBobCode(key.distributionCenterBobCode)
        .setProductionWeek(key.productionWeek)
        .setSkuId(key.skuId)
        .setSupplyQuantityRecommendation(0.toProtoDecimal())
        .setInventoryRollover(inventoryRollover.toProtoDecimal())
        .setDemand(aggregatedDemand.toProtoDecimal())
        .setRecommendationEnabled(SQRConfiguration.DEFAULT_RECOMMENDATION_ENABLED)
        .setMultiWeekEnabled(SQRConfiguration.DEFAULT_MULTI_WEEK_ENABLED)
        .setInbound(inbound.toProtoDecimal())
        .setIncomingPos(incomingPos.toProtoDecimal())
        .setUnusableStock(unusableStock.toProtoDecimal())
        .setUnit(UOM.UOM_UNIT)

    key.build() to value.build()
}

@Suppress("LongParameterList")
private fun supplyQuantityRecommendationDailyPair(
    dcCode: String,
    dcWeek: DcWeek,
    dateParam: LocalDate,
    skuId: UUID,
    aggregatedDemand: Int,
    inventoryRollover: Int,
    bufferDetails: Int,
    sqr: Int,
) = run {
    val date = com.google.type.Date.newBuilder()
        .setYear(dateParam.year)
        .setMonth(dateParam.monthValue)
        .setDay(dateParam.dayOfMonth)
        .build()
    val key = SupplyQuantityRecommendationDailyKey.newBuilder()
        .setDcCode(dcCode)
        .setDate(
            date,
        ).setSkuId(skuId.toString())

    val purchaseOrdersValue = getPurchaseOrdersValue()
    val inboundsValue = getInboundsValue()
    val value = SupplyQuantityRecommendationDailyVal.newBuilder()
        .setDcCode(key.dcCode)
        .setDate(date)
        .setProductionWeek(
            SupplyQuantityRecommendationDailyVal.ProductionWeek.newBuilder()
                .setWeek(dcWeek.week)
                .setYear(dcWeek.year).build(),
        )
        .setSkuId(key.skuId)
        .setSupplyQuantityRecommendation(
            Quantity.newBuilder().setQty(0.toProtoDecimal())
                .build(),
        )
        .setInventoryRollover(Quantity.newBuilder().setQty(inventoryRollover.toProtoDecimal()).build())
        .setForecastedDemanded(Quantity.newBuilder().setQty(aggregatedDemand.toProtoDecimal()).build())
        .setTouchlessOrderingEnabled(false)
        .setBufferDetails(Quantity.newBuilder().setQty(bufferDetails.toProtoDecimal()).build())
        .setSupplyQuantityRecommendation(Quantity.newBuilder().setQty(sqr.toProtoDecimal()).build())
        .setPurchaseOrder(purchaseOrdersValue)
        .setInbounds(inboundsValue)
        .setUnit(UOM.UOM_UNIT)
    key.build() to value.build()
}

private fun getInboundsValue(): PurchaseOrderData.Builder =
    PurchaseOrderData.newBuilder()
        .apply {
            addAllPoNumbers(
                listOf(
                    PONumber.newBuilder()
                        .setPoNumber("2044DH107244")
                        .setQty(200.toProtoDecimal())
                        .build(),
                ),
            )
        }

private fun getPurchaseOrdersValue(): PurchaseOrderData.Builder =
    PurchaseOrderData.newBuilder()
        .apply {
            addAllPoNumbers(
                listOf(
                    PONumber.newBuilder()
                        .setPoNumber("")
                        .setQty(0.toProtoDecimal())
                        .build(),
                ),
            )
        }

private fun Int.toProtoDecimal() = Decimal.newBuilder().setValue(this.toString()).build()

@Suppress("MagicNumber")
private fun getRandomTestCSKUCode() = "VPM-00-".plus(Random.nextInt(9999)).plus("-").plus(Random.nextInt(11))
