package com.hellofresh.cif.endToEndTest.db

import javax.sql.DataSource
import org.apache.logging.log4j.kotlin.Logging

class DcConfigWeightsViewRepository(private val dataSource: DataSource) {

    fun waitForDcConfigWeightView() {
        waitFor(
            "No dc config weight view record found",
            { refreshDcConfigWeightView() },
            {
                dataSource.connection.use { conn ->
                    conn.createStatement()
                        .executeQuery(" select 1 from dc_config_weight_view where dc_code is not null").next()
                }
            },
        )
        logger.info("Dc Config Weight view with PO information ready")
    }

    private fun refreshDcConfigWeightView() {
        dataSource.connection.use { conn ->
            logger.info("Refreshing Dc Config Weight view")
            conn.createStatement()
                .execute("refresh materialized view concurrently dc_config_weight_view")
        }
    }

    companion object : Logging
}
