package com.hellofresh.cif.endToEndTest.kafka.producer

import com.hellofresh.cif.endToEndTest.SEND_TIMEOUT
import com.hellofresh.cif.endToEndTest.model.DataRecord
import java.util.Properties
import java.util.concurrent.TimeUnit.SECONDS
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerRecord

class KafkaJsonRecordProducer(properties: Properties) : KafkaProducer<String, String>(properties) {
    fun produceJsonData(topic: String, record: DataRecord<String, String>, partition: Int? = null) {
        produceRecord(topic, record.key, record.value, partition)
    }

    private fun produceRecord(topic: String, key: String, record: String, partition: Int?) {
        send(ProducerRecord(topic, partition, key, record)).get(SEND_TIMEOUT, SECONDS)
    }
}
