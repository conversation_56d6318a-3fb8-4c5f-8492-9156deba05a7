package com.hellofresh.cif.endToEndTest.db

import com.hellofresh.cif.endToEndTest.testCases.TestCase
import javax.sql.DataSource

class SqrShortShelfLifeConfRepository(private val dataSource: DataSource) {
    @Suppress("MagicNumber")
    fun persistSqrShortShelfLifeConf(testCase: TestCase) {
        dataSource.connection.use { conn ->
            conn.prepareStatement(
                "insert into sqr_short_shelf_life_conf(" +
                    "dc_code, date, sku_id, buffer_percentage, buffer_additional) values (?,?,?,?,?)"
            )
                .use {
                    it.setObject(1, testCase.dcCode)
                    it.setObject(2, testCase.date)
                    it.setObject(3, testCase.testCskuId)
                    it.setObject(4, 10)
                    it.setObject(5, 100)
                    it.execute()
                }
        }
    }
}
