package com.hellofresh.cif.endToEndTest.kafka.producer

import com.fasterxml.jackson.databind.JsonNode
import com.google.type.Decimal
import com.google.type.Money
import com.hellofresh.cif.endToEndTest.SEND_TIMEOUT
import com.hellofresh.cif.endToEndTest.TOPIC_TRANSFER_ORDERS_TOPIC_NAME
import com.hellofresh.cif.endToEndTest.objectMapper
import com.hellofresh.cif.endToEndTest.testCases.TestCase
import com.hellofresh.cif.lib.kafka.serde.ProtoSerde
import com.hellofresh.dateUtil.models.toProtoTimestamp
import com.hellofresh.proto.stream.transferOrder.v1.CasePackaging
import com.hellofresh.proto.stream.transferOrder.v1.InventoryInputType
import com.hellofresh.proto.stream.transferOrder.v1.State
import com.hellofresh.proto.stream.transferOrder.v1.TransferOrder
import com.hellofresh.proto.stream.transferOrder.v1.TransferOrderItem
import java.time.OffsetDateTime
import java.util.Properties
import java.util.UUID
import java.util.concurrent.TimeUnit
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.common.serialization.StringSerializer

@Suppress("MagicNumber")
class KafkaTransferOrderProtoProducer(properties: Properties) :
    KafkaProducer<String, TransferOrder>(
        properties,
        StringSerializer(),
        ProtoSerde<TransferOrder>().serializer(),
    ) {

    fun produceData(testCase: TestCase) {
        val json = objectMapper.readTree(testCase.transferOrder.value)

        val transferOrderId = testCase.transferOrder.key
        val inboundTransferOrder = createTransferOrder(json, quantity = "0")
        val outboundTransferOrder = createTransferOrder(
            json,
            "VE",
            "BX",
            "0",
            transferOrderNumber = "2531OET92465",
        )

        send(
            ProducerRecord(
                TOPIC_TRANSFER_ORDERS_TOPIC_NAME,
                null,
                transferOrderId,
                inboundTransferOrder,
            ),
        ).get(SEND_TIMEOUT, TimeUnit.SECONDS)

        send(
            ProducerRecord(
                TOPIC_TRANSFER_ORDERS_TOPIC_NAME,
                null,
                UUID.randomUUID().toString(),
                outboundTransferOrder,
            ),
        ).get(SEND_TIMEOUT, TimeUnit.SECONDS)
    }

    private fun createTransferOrder(
        json: JsonNode,
        sourceDcCode: String = "BX",
        destinationDcCode: String = "VE",
        quantity: String = "100",
        transferOrderNumber: String = "2531OET92464"
    ): TransferOrder? {
        val itemsArray = json.get("items")
        val week = json.get("productionWeek").get("week")
        val year = json.get("productionWeek").get("year")
        if (itemsArray == null || !itemsArray.isArray || itemsArray.size() == 0) {
            return null
        }
        val firstItem = itemsArray[0]
        val skuId = firstItem.get("skuId")?.asText()

        val startTime = OffsetDateTime.parse(json["deliveryStartTime"].textValue()).toProtoTimestamp()
        val endTime = OffsetDateTime.parse(json["deliveryStartTime"].textValue()).toProtoTimestamp()
        val builder = TransferOrder.newBuilder()
            .setId(UUID.randomUUID().toString())
            .setSourceDcCode(sourceDcCode)
            .setDestinationDcCode(destinationDcCode)
            .setCreatorEmail("<EMAIL>")
            .setReasonText("transfer orders end to end test")
            .setStatus(State.valueOf("STATE_ORDERED"))
            .setProductionWeek(
                com.hellofresh.proto.stream.transferOrder.v1.ProductionWeek.newBuilder()
                    .setYear(year.intValue())
                    .setWeek(week.intValue())
                    .build(),
            )
            .setCreateTime(startTime)
            .setUpdateTime(endTime)
            .setPickupStartTime(
                startTime
            )
            .setPickupEndTime(
                endTime
            )
            .setDeliveryStartTime(
                startTime
            )
            .setDeliveryEndTime(
                endTime
            )
            .setSentTime(startTime)
            .setTransferOrderNumber(transferOrderNumber)
            .setSourceDcName("CA - Valley")
            .setShippingMethod("Vendor delivered")
            .setMarketCode("DE")
            .setVersion(1)

        builder.addItems(getProtoTransferOrderItem(skuId = UUID.fromString(skuId), quantity = quantity))
        return builder.build()
    }

    @SuppressWarnings("LongParameterList")
    private fun getProtoTransferOrderItem(
        id: UUID = UUID.randomUUID(),
        cskuCode: String = "PHF-10-88380-4",
        cskuName: String = "X3B- Garlic, Unpeeled Clove",
        supplierId: UUID = UUID.fromString("64ba864f-d7b1-4216-8c98-90fc32f77335"),
        supplierCode: String = "301330",
        supplierSkuId: UUID = UUID.randomUUID(),
        skuId: UUID = UUID.fromString("77946983-7dc1-41fc-9f85-655131fce4a7"),
        orderSize: Int = 100,
        quantity: String = "100",
    ): TransferOrderItem =
        TransferOrderItem.newBuilder()
            .setId(id.toString())
            .setCskuCode(cskuCode)
            .setCskuName(cskuName)
            .setSupplierId(supplierId.toString())
            .setSupplierCode(supplierCode)
            .setSupplierSkuId(supplierSkuId.toString())
            .setSkuId(skuId.toString())
            .setOrderSize(orderSize)
            .setInventoryType(InventoryInputType.INVENTORY_INPUT_TYPE_MANUAL)
            .setPrice(
                Money.newBuilder()
                    .setCurrencyCode("CAD")
                    .setNanos(990000000)
                    .build(),
            )
            .setTotalPrice(
                Money.newBuilder()
                    .setCurrencyCode("CAD")
                    .setUnits(98)
                    .setNanos(10000000)
                    .build(),
            )
            .setCasePackaging(
                CasePackaging.newBuilder()
                    .setSize(
                        Decimal.newBuilder()
                            .setValue("1")
                            .build(),
                    )
                    .setUnit(CasePackaging.UOM.UOM_UNIT)
                    .build(),
            )
            .setQuantity(
                Decimal.newBuilder()
                    .setValue(quantity)
                    .build(),
            )
            .build()
}
