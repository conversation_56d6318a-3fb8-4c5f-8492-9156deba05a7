package com.hellofresh.cif.endToEndTest.db

import javax.sql.DataSource
import org.apache.logging.log4j.kotlin.Logging

class TransferOrderViewsRepository(private val dataSource: DataSource) {

    fun waitForTransferOrdersViewForTOInfo() {
        waitFor(
            "No transfer orders view record found",
            { refreshTransferOrdersView() },
            {
                dataSource.connection.use { conn ->
                    conn.createStatement()
                        .executeQuery(" select 1 from transfer_orders_grn_view where to_number is not null").next()
                }
            },
        )
        logger.info("Transfer orders view with TO information ready")
    }

    private fun refreshTransferOrdersView() {
        dataSource.connection.use { conn ->
            logger.info("Refreshing transfer orders view")
            conn.createStatement()
                .execute("refresh materialized view concurrently transfer_orders_grn_view")
        }
    }

    companion object : Logging
}
