package com.hellofresh.cif.endToEndTest.testCases.api

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

@JsonIgnoreProperties(ignoreUnknown = true)
data class CalculationResult(
    val calculations: List<Calculation>
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Calculation(
    val skuId: String,
    val skuCode: String,
    val unusableStock: Int,
    val usableStock: Int,
    val inbound: Amount,
    val consumption: Int,
    val dailyNeed: Int,
    val closingStock: Int,
    val week: String,
    val date: String,
    val pos: List<String>,
    val expectedInboundTransferOrdersQuantity: Int,
    val expectedOutboundTransferOrdersQuantity: Int,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Amount(
    val amount: Int
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Po(
    val poRef: String,
    val dcCode: String,
    val ordered: Int,
    val received: Int,
    val supplierId: String,
    val supplierName: String
)
