package com.hellofresh.cif.endToEndTest.db

import com.hellofresh.cif.endToEndTest.objectMapper
import com.hellofresh.cif.endToEndTest.testCases.TestCase
import java.util.UUID
import javax.sql.DataSource

class SupplierRepository(private val dataSource: DataSource) {

    fun persistSupplierData(testCase: TestCase) {
        val supplierId = UUID.fromString(testCase.supplier.key)
        val supplierName = objectMapper.readTree(testCase.supplier.value)["name"].textValue()
        dataSource.connection.use { conn ->

            conn.prepareStatement("insert into supplier(id, name) values (?,?)")
                .use {
                    it.setObject(1, supplierId)
                    it.setString(2, supplierName)
                    it.execute()
                }
        }
    }
}
