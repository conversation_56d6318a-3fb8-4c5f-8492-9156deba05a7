package com.hellofresh.cif.endToEndTest

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.endToEndTest.asserters.DBSource
import com.hellofresh.cif.endToEndTest.asserters.DBSource.Companion.CalculationMode
import com.hellofresh.cif.endToEndTest.asserters.KafkaSource
import com.hellofresh.cif.endToEndTest.db.DcConfigWeightsViewRepository
import com.hellofresh.cif.endToEndTest.db.InventorySnapshotRepository
import com.hellofresh.cif.endToEndTest.db.PurchaseOrderViewsRepository
import com.hellofresh.cif.endToEndTest.db.SkuSpecificationViewRepository
import com.hellofresh.cif.endToEndTest.db.SqrShortShelfLifeConfRepository
import com.hellofresh.cif.endToEndTest.db.SupplierRepository
import com.hellofresh.cif.endToEndTest.db.TransferOrderViewsRepository
import com.hellofresh.cif.endToEndTest.kafka.producer.KafkaDemandProtoProducer
import com.hellofresh.cif.endToEndTest.kafka.producer.KafkaGRNProtoProducer
import com.hellofresh.cif.endToEndTest.kafka.producer.KafkaJsonRecordProducer
import com.hellofresh.cif.endToEndTest.kafka.producer.KafkaPurchaseOrderProtoProducer
import com.hellofresh.cif.endToEndTest.kafka.producer.KafkaTransferOrderProtoProducer
import com.hellofresh.cif.endToEndTest.testCases.TestCase
import com.hellofresh.cif.endToEndTest.testCases.api.ForecastApiVerifier
import com.hellofresh.cif.endToEndTest.testCases.happyPath
import com.hellofresh.cif.lib.kafka.serde.serde
import com.hellofresh.logging.withContext
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.v1.SkuInventoryDemandForecastKey
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.v1.SkuInventoryDemandForecastVal
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendation.v1.SupplyQuantityRecommendationKey
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendation.v1.SupplyQuantityRecommendationVal
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyKey
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyVal
import com.hellofresh.service.Application
import com.hellofresh.service.runApplication
import com.hellofresh.sku.models.SkuSpecification
import java.util.Properties
import javax.sql.DataSource
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.producer.ProducerConfig.BOOTSTRAP_SERVERS_CONFIG
import org.apache.kafka.clients.producer.ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG
import org.apache.kafka.clients.producer.ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG
import org.apache.kafka.common.serialization.StringSerializer
import org.postgresql.ds.PGSimpleDataSource

fun main() = runApplication {
    withContext { ctx ->
        ctx["tier"] = config["tier"]

        runTest(happyPath)
    }
}

val objectMapper: ObjectMapper = jacksonObjectMapper().findAndRegisterModules()

fun Application.runTest(testCase: TestCase) {
    logger.info(
        "Running end to end tests for: ${testCase.name} with csku_id: ${testCase.skuCodeToId.key} and date ${testCase.date}",
    )
    logger.info("Test case details: $testCase")

    val dataSource = PGSimpleDataSource().apply {
        serverNames = arrayOf(config["db.host"])
        databaseName = config["db.name"]
        user = config["db.user"]
        password = config["db.pass"]
    }
    val purchaseOrderViewsRepository = PurchaseOrderViewsRepository(dataSource)

    val transferOrderViewsRepository = TransferOrderViewsRepository(dataSource)

    val jsonBrokerProperties = Properties().also {
        it[BOOTSTRAP_SERVERS_CONFIG] = config["kafka.bootstrap.servers"]
        it[KEY_SERIALIZER_CLASS_CONFIG] = StringSerializer::class.java.name
        it[VALUE_SERIALIZER_CLASS_CONFIG] = StringSerializer::class.java.name
    }

    // insert sku specs enables calculations job
    insertSkuSpec(testCase, jsonBrokerProperties)

    SupplierRepository(dataSource).apply {
        persistSupplierData(testCase)
    }

    InventorySnapshotRepository(dataSource).apply {
        persistInventorySnapshot(testCase)
    }

    KafkaJsonRecordProducer(jsonBrokerProperties).apply {
        produceJsonData(
            TOPIC_PRIVATE_DISTRIBUTION_CENTER,
            testCase.dcConfig,
        )
    }

    KafkaJsonRecordProducer(jsonBrokerProperties).apply {
        produceJsonData(
            TOPIC_PRIVATE_DISTRIBUTION_CENTER,
            testCase.sourceDcConfig,
        )
    }

    DcConfigWeightsViewRepository(dataSource).waitForDcConfigWeightView()

    KafkaPurchaseOrderProtoProducer(jsonBrokerProperties).produceData(testCase)

    KafkaTransferOrderProtoProducer(jsonBrokerProperties).produceData(testCase)

    KafkaDemandProtoProducer(jsonBrokerProperties).produceDemandData(testCase)

    purchaseOrderViewsRepository.waitForPurchaseOrdersViewForPOInfo()
    transferOrderViewsRepository.waitForTransferOrdersViewForTOInfo()

    // GRN Service needs sku specs and po info view data
    KafkaGRNProtoProducer(jsonBrokerProperties).produceGRNData(testCase)

    purchaseOrderViewsRepository.waitForPurchaseOrdersView()

    SqrShortShelfLifeConfRepository(dataSource).apply {
        persistSqrShortShelfLifeConf(testCase)
    }

    runBlocking { runAssertions(testCase, dataSource, jsonBrokerProperties) }

    logger.info("End to end tests passed for : ${testCase.name}")
}

private suspend fun Application.runAssertions(
    testCase: TestCase,
    dataSource: DataSource,
    brokerProperties: Properties
) {
    kotlinx.coroutines.withContext(Dispatchers.IO) {
        launch {
            testCase.calculationExpected.forEach { DBSource(dataSource, CalculationMode.PROD).assertEventually(it) }
        }
        launch {
            testCase.proProdCalculationExpected.forEach {
                DBSource(
                    dataSource,
                    CalculationMode.PREPROD,
                ).assertEventually(it)
            }
        }

        launch {
            KafkaSource(
                TOPIC_DEMAND_FORECAST_TOPIC_NAME,
                brokerProperties,
                serde<SkuInventoryDemandForecastKey>(),
                serde<SkuInventoryDemandForecastVal>(),
            ).assertEventually(testCase.expectedProtobufBulkOutput)
        }

        launch {
            KafkaSource(
                TOPIC_SQR_TOPIC_NAME,
                brokerProperties,
                serde<SupplyQuantityRecommendationKey>(),
                serde<SupplyQuantityRecommendationVal>(),
            ).assertEventuallySqr(testCase.expectedProtobufSqr, dataSource)
        }

        launch {
            val forecastApiServer = config["forecast.api.server"]
            val forecastApiServerPort = config["forecast.api.port"]
            ForecastApiVerifier(forecastApiServer, forecastApiServerPort.toInt()).verify(testCase)
        }

        launch {
            KafkaSource(
                TOPIC_SQR_DAILY_TOPIC_NAME,
                brokerProperties,
                serde<SupplyQuantityRecommendationDailyKey>(),
                serde<SupplyQuantityRecommendationDailyVal>(),
            ).assertEventuallySqr(testCase.expectedProtobufSqrDaily, dataSource)
        }
    }
}

private fun Application.insertSkuSpec(testCase: TestCase, jsonBrokerProperties: Properties) {
    val inventoryDataSource = PGSimpleDataSource().apply {
        serverNames = arrayOf(config["db.host.inventory"])
        databaseName = config["db.name.inventory"]
        user = config["db.user.inventory"]
        password = config["db.pass.inventory"]
    }

    // TODO evaluate if it makes sense to add the kafka-db-sync to the e2e stack
    DBFixtures(inventoryDataSource).insertSkuFixtures(testCase.skuSpecification.value)
        .also {
            KafkaJsonRecordProducer(jsonBrokerProperties).apply {
                produceJsonData(
                    TOPIC_PRIVATE_SKU_SPECIFICATION,
                    testCase.skuSpecification,
                )
            }
        }

    SkuSpecificationViewRepository(inventoryDataSource).waitForView()
}

private class DBFixtures(private val dataSource: DataSource) {

    fun insertSkuFixtures(value: String) {
        val skuSpecification = objectMapper.readValue(value, SkuSpecification::class.java)
        dataSource.connection.use { conn ->
            val query = "INSERT INTO sku_specification(id,cooling_type,name,packaging,code,category,acceptable_code_life,market, uom) " +
                "VALUES('${happyPath.testCskuId}'," +
                "'${skuSpecification.coolingType}'," +
                "'${skuSpecification.name}'," +
                "'${skuSpecification.packaging}'," +
                "'${skuSpecification.skuCode}'," +
                "'${skuSpecification.category}'," +
                "'${skuSpecification.acceptableCodeLife}'," +
                "'${skuSpecification.market}'," +
                "'${skuSpecification.uom}'::uom" +
                ")"

            conn.createStatement().execute(query)
        }
    }
}
