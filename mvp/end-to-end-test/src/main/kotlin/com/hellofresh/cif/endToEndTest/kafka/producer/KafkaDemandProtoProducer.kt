package com.hellofresh.cif.endToEndTest.kafka.producer

import com.google.type.Decimal
import com.hellofresh.cif.endToEndTest.SEND_TIMEOUT
import com.hellofresh.cif.endToEndTest.TOPIC_SKU_DEMAND_FORECAST
import com.hellofresh.cif.endToEndTest.objectMapper
import com.hellofresh.cif.endToEndTest.testCases.TestCase
import com.hellofresh.cif.lib.kafka.serde.ProtoSerde
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastKey
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.RecipesBreakdown
import java.util.Properties
import java.util.concurrent.TimeUnit.SECONDS
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerRecord

class KafkaDemandProtoProducer(properties: Properties) :
    KafkaProducer<SkuDemandForecastKey, SkuDemandForecastVal>(
        properties,
        ProtoSerde<SkuDemandForecastKey>().serializer(),
        ProtoSerde<SkuDemandForecastVal>().serializer(),
    ) {

    private fun String.toProtoDate(): com.google.type.Date = this.split("-").let {
        com.google.type.Date.newBuilder()
            .setYear(it[0].toInt())
            .setMonth(it[1].toInt())
            .setDay(it[2].toInt())
            .build()
    }

    fun produceDemandData(testCase: TestCase) {
        testCase.demand.forEach { demand ->
            val json = objectMapper.readTree(demand.value)
            val key = SkuDemandForecastKey.newBuilder()
                .setDate(json["date"].textValue().toProtoDate())
                .setDistributionCenterBobCode(json["dc"].textValue())
                .setSkuId(json["culinary_sku_id"].textValue())
                .build()

            val value = SkuDemandForecastVal.newBuilder().apply {
                setTotalQty(Decimal.newBuilder().setValue(json["qty"].longValue().toString())).build()
                addRecipesBreakdown(
                    RecipesBreakdown.newBuilder().apply {
                        brand = "UNKNOWN"
                        recipeIndex = json["recipe_index"].textValue()
                        qty = Decimal.newBuilder().setValue(json["qty"].longValue().toString()).build()
                    },
                )
            }
                .build()

            send(ProducerRecord(TOPIC_SKU_DEMAND_FORECAST, key, value)).get(SEND_TIMEOUT, SECONDS)
        }
        flush()
    }
}
