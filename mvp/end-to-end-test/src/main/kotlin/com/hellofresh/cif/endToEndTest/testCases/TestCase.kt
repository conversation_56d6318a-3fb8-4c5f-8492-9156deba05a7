package com.hellofresh.cif.endToEndTest.testCases

import com.hellofresh.cif.endToEndTest.model.DataRecord
import com.hellofresh.cif.endToEndTest.testCases.api.CalculationResult
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.v1.SkuInventoryDemandForecastKey
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.v1.SkuInventoryDemandForecastVal
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendation.v1.SupplyQuantityRecommendationKey
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendation.v1.SupplyQuantityRecommendationVal
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyKey
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyVal
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.UUID

data class TestCase(
    val name: String,
    val testCskuId: UUID,
    val testCskuCode: String,
    val date: LocalDate,
    val dcCode: String,
    val zoneId: ZoneId,
    val lastCleardownDate: LocalDate,
    val sourceDcConfig: DataRecord<String, String>,
    val dcConfig: DataRecord<String, String>,
    val skuCodeToId: DataRecord<String, String>,
    val skuSpecification: DataRecord<String, String>,
    val inventory: String,
    val demand: List<DataRecord<String, String>>,
    val purchaseOrder: DataRecord<String, String>,
    val transferOrder: DataRecord<String, String>,
    val goodsReceivedNote: DataRecord<String, String>,
    val supplier: DataRecord<String, String>,

    // This the column_name to value mapping we expect to see in the DB.
    val calculationExpected: List<DataRecord<String, String>>,
    val proProdCalculationExpected: List<DataRecord<String, String>>,

    val expectedProtobufBulkOutput: Map<SkuInventoryDemandForecastKey, SkuInventoryDemandForecastVal>,
    val expectedProtobufSqr: Map<SupplyQuantityRecommendationKey, SupplyQuantityRecommendationVal>,
    val expectedProtobufSqrDaily: Map<SupplyQuantityRecommendationDailyKey, SupplyQuantityRecommendationDailyVal>,
    val expectedApiCalculations: CalculationResult
)

fun testCase(fn: () -> TestCase) = fn()

fun format(date: LocalDate) = date.format(DateTimeFormatter.ofPattern("uuuu-MM-dd"))
operator fun LocalDate.plus(n: Long) = this.plusDays(n)
operator fun LocalDate.minus(n: Long) = this.minusDays(n)
