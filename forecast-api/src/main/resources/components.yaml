components:
    responses:
        apiErrorResponse:
            description: Error response
            content:
                application/json:
                    schema:
                        $ref: '#/components/schemas/ErrorResponse'

    parameters:
        AcceptHeader:
            in: header
            name: 'Accept'
            description: The client preferred response media type
            schema:
                type: string
                example: text/csv
                default: application/json
        sortByParam:
            in: query
            name: 'sortBy'
            required: false
            schema:
                type: string
                enum: [ skuName, skuCode ]
                default: skuName
        dcCodePathParam:
            in: path
            name: 'dcCode'
            required: true
            schema:
                $ref: '#/components/schemas/DcCode'
        weekPathParam:
            in: path
            name: 'week'
            required: true
            schema:
                $ref: '#/components/schemas/YearWeek'
        dcCodesQueryParam:
            in: query
            name: 'dcCode'
            required: true
            description: A comma separated list of DC codes, (ex VE,CH)
            schema:
                type: array
                minItems: 1
                items:
                    $ref: '#/components/schemas/DcCode'
        snapShotIdQueryParam:
            in: query
            name: 'snapShotId'
            required: false
            description: snapshot UUID
            schema:
                $ref: '#/components/schemas/SnapshotId'

        dcCodeQueryParam:
            in: query
            name: 'dcCode'
            required: true
            description: dc code (ex VE,CH)
            schema:
                $ref: '#/components/schemas/DcCode'
        weekQueryParam:
            in: query
            name: 'week'
            required: true
            description: Year Week
            schema:
                $ref: '#/components/schemas/YearWeek'
        pageParam:
            in: query
            name: 'page'
            required: false
            schema:
                type: string
                default: '0'
            description: Requested page for the corresponding limit
        skuCountParam:
            in: query
            name: 'skuCount'
            required: false
            schema:
                type: string
                default: '6'
            description: >-
                Number of skus per page.
        weeksParamRequired:
            in: query
            name: 'weeks'
            required: true
            allowEmptyValue: false
            schema:
                type: array
                minItems: 0
                items:
                    $ref: '#/components/schemas/YearWeek'
        weeksParam:
            in: query
            name: 'weeks'
            required: false
            allowEmptyValue: true
            schema:
                type: array
                minItems: 0
                items:
                    $ref: '#/components/schemas/YearWeek'
        marketParam:
            in: path
            name: 'market'
            required: false
            allowEmptyValue: false
            schema:
                type: string
        marketQueryParam:
            in: query
            name: 'market'
            required: true
            allowEmptyValue: false
            schema:
                type: string
        fileTypeQueryParam:
            name: 'fileType'
            in: query
            required: false
            allowEmptyValue: false
            schema:
                type: string
                enum:
                    - STOCK_INVENTORY
                    - STOCK_UPDATE
        skuIdParam:
            in: path
            name: 'skuId'
            required: true
            schema:
                type: string
                format: uuid
                example: 'b3fc9c10-cb68-4ec3-a576-894ada82ec95'
        noteIdPathParam:
            in: path
            name: 'noteId'
            required: true
            schema:
                type: string
                format: uuid
                example: 'b3fc9c10-cb68-4ec3-a576-894ada82ec95'
        skuCodesParam:
            in: query
            name: 'skuCode'
            required: false
            schema:
                type: array
                minItems: 0
                items:
                    type: string
            allowEmptyValue: true
            description: List of the skuCodes for which the calculations have to be fetched.
        skuIdsParam:
            in: query
            name: 'skuId'
            required: false
            schema:
                type: array
                minItems: 0
                items:
                    type: string
                    format: uuid
            allowEmptyValue: true
            description: List of the skuIds for which data have to be fetched.
        skuCategoriesParam:
            in: query
            name: 'skuCategory'
            required: false
            schema:
                type: array
                minItems: 0
                items:
                    type: string
                    example: 'Protein'
            allowEmptyValue: true
            description: List of the skuCategories for which the calculations have to be fetched.
        additionalFiltersParam:
            in: query
            name: 'additionalFilters'
            required: false
            allowEmptyValue: true
            schema:
                type: array
                items:
                    $ref: '#/components/schemas/AdditionalFilterRequest'
        consumptionDaysAheadParam:
            in: query
            name: 'consumptionDaysAhead'
            required: false
            allowEmptyValue: true
            schema:
                type: integer
                example: 1
            description: Number of days to check consumption/stock that many number of days ahead.
        locationInBoxParam:
            in: query
            name: 'locationInBox'
            required: false
            allowEmptyValue: true
            schema:
                type: array
                minItems: 0
                items:
                    type: string
                    example: 'Assembly-Loose'
            description: List of Assembly/Kitted SKUs for which the calculations have to be fetched.
        closingStockLessThanOrEqual:
            in: query
            name: 'closingStockLessThanOrEqual'
            description: Fetch the calculations only for the skus where closing stock is less or equal than the given number.
            required: false
            schema:
                type: integer
                format: int64
        inventoryRefreshParam:
            in: query
            name: 'inventoryRefreshType'
            required: false
            allowEmptyValue: true
            schema:
                type: string
                example: 'cleardown'
            description: Which inventory quantities are considered. Supported values are cleardown and live. The default is cleardown.
        supplierIdParam:
            in: query
            name: 'supplierId'
            required: false
            allowEmptyValue: true
            schema:
                type: array
                minItems: 0
                items:
                    type: string
                    format: uuid
                    example: '866594c6-441f-441c-962c-b80412a89e73'
            description: supplier UUID
        activeSuppliersNameParam:
            in: query
            name: 'activeSupplierName'
            required: false
            allowEmptyValue: true
            schema:
                type: array
                minItems: 0
                items:
                    type: string
                    example: 'Jos Poell BV'
            description: active supplier name
        poDueInLessThanOrEqual:
            in: query
            name: 'poDueInLessThan'
            description: Fetch the Sku with PoDueIn more than this value
            required: false
            schema:
                type: integer
                minimum: -7
                maximum: 10
        poDueInGreaterThanOrEqual:
            in: query
            name: 'poDueInGreaterThan'
            description: Fetch the Sku with PoDueIn less than this value
            required: false
            schema:
                type: integer
                minimum: -7
                maximum: 10
        expiringInGreaterThanOrEqual:
            in: query
            name: 'expiringInGreaterThanOrEqual'
            description: Fetch the projected waste for Skus with month more than this value
            required: false
            schema:
                type: integer
                minimum: 0
                maximum: 11
        expiringInLessThanOrEqual:
            in: query
            name: 'expiringInLessThanOrEqual'
            description: Fetch the projected waste for Skus with month less than this value
            required: false
            schema:
                type: integer
                minimum: 1
                maximum: 12

    schemas:
        Configuration:
            type: object
            minProperties: 0
            description: Key as `dcCode`.
            additionalProperties:
                $ref: '#/components/schemas/CountryDcConfigurationResponse'
            example:
                ve:
                    market: 'DACH'
                    minWeek: '2021-W49'
                    currentWeek: '2022-W07'
                    startDate: '2022-02-14'
                    productionStartDay: 'Monday'
                    clearDownDay: 'Monday'
                    lastCleardown: '2023-01-23'
                    lastCleardownTime: '2023-01-03T10:15:30Z'

        CountryDcConfigurationResponse:
            type: object
            required:
                - market
                - clearDownDay
                - minWeek
                - currentWeek
                - startDate
                - productionStartDay
            properties:
                market:
                    $ref: '#/components/schemas/MarketResponse'
                minWeek:
                    $ref: '#/components/schemas/YearWeek'
                currentWeek:
                    $ref: '#/components/schemas/YearWeek'
                startDate:
                    type: string
                    format: date
                    description: First day of the current HF week
                productionStartDay:
                    $ref: '#/components/schemas/DayOfWeekResponse'
                clearDownDay:
                    $ref: '#/components/schemas/DayOfWeekResponse'
                lastCleardown:
                    type: string
                    format: date
                    description: Date of the latest cleardown for this Distribution Center
                    example: 2023-01-23
                lastCleardownTime:
                    type: string
                    format: date-time
                    example: 2023-01-03T10:12:30Z

        SkuResponse:
            type: object
            properties:
                skuCode:
                    type: string
                    example: 'BAK-999'
                skuId:
                    type: string
                    format: uuid
                    example: '111A-222B-333C-444D'
                skuName:
                    type: string
                    example: Chicken breast 190g

        YearWeek:
            type: string
            description: Hellofresh Week Format
            pattern: '^\d{4}-W\d{2}$'
            example: '2022-W07'

        MarketResponse:
            type: string
            description: Hellofresh Market code.
            pattern: '^[A-Z]*$'
            example: 'DACH'

        CountryResponse:
            type: string
            description: ISO-2 country code
            pattern: '^[A-Z]{2}$'
            example: 'DE'

        DcCode:
            type: string
            description: dcCode in upper caps
            pattern: '^[A-Z]+$'
            example: 'VE'

        SkuId:
            type: string
            format: uuid
            example: 'ce91483f-7361-4914-9a95-573e5bcbab99'

        SnapshotId:
            type: string
            format: uuid
            example: 'ce91483f-7361-4914-9a95-573e5bcbab99'

        SupplierResponse:
            type: object
            properties:
                id:
                    type: string
                    format: uuid
                name:
                    type: string

        ActiveSupplierResponse:
            type: object
            properties:
                name:
                    type: string
                    example: "Jos Poell BV"

        CalculationFiltersResponse:
            type: object
            required:
                - ingredientCategories
                - skuSet
                - locationInBox
                - suppliers
                - activeSuppliers
            properties:
                ingredientCategories:
                    type: array
                    items:
                        type: string
                    description: The code of the Sku category (ex. PTN), can have multiple values
                        as separate params.(ex. PTN)
                skuSet:
                    type: array
                    items:
                        $ref: '#/components/schemas/SkuResponse'
                locationInBox:
                    type: array
                    items:
                        type: string
                    description: List of Assembly/Kitted SKUs (ex. Assembly-Loose, Pack Chilled), can have multiple values
                        as separate params.(ex. Assembly-Loose, Pack Chilled)
                suppliers:
                    type: array
                    items:
                        $ref: '#/components/schemas/SupplierResponse'
                activeSuppliers:
                    type: array
                    items:
                        $ref: '#/components/schemas/ActiveSupplierResponse'

        DailyCalculationsResponse:
            type: object
            required:
                - calculations
            properties:
                totalPages:
                    type: integer
                    minimum: 0
                page:
                    type: integer
                    minimum: 0
                totalSkusAtRiskCount:
                    type: integer
                    minimum: 0
                totalSkusCount:
                    type: integer
                    minimum: 0
                calculations:
                    type: array
                    minItems: 0 # we might not have any calculations with a given filter
                    items:
                        $ref: '#/components/schemas/DailyCalculationResponse'

        StockUpdateSimulationResponse:
            type: object
            required:
                - stockUpdates
                - calculations
            properties:
                stockUpdates:
                    type: array
                    items:
                        $ref: '#/components/schemas/StockUpdateV2Item'
                calculations:
                    type: array
                    minItems: 0 # we might not have any calculations with a given filter
                    items:
                        $ref: '#/components/schemas/DailyCalculationResponse'

        DailyCalculationResponse:
            allOf:
                -   $ref: '#/components/schemas/CalculationResponse'
                -   required:
                        - date
                    type: object
                    properties:
                        date:
                            type: string
                            format: date
                            example: "2022-03-23"
                        storageStock:
                            type: number
                        stagingStock:
                            type: number

        FileExportResponse:
            type: object
            required:
                - requestId
                - status
            properties:
                requestId:
                    type: string
                    format: uuid
                parameters:
                    type: string
                    format: json
                status:
                    type: string
                    enum:
                        - Pending
                        - Failed
                        - Completed
                    default: Pending
                fileUrl:
                    type: string

        WeeklyCalculationsResponse:
            type: object
            required:
                - calculations
            properties:
                totalPages:
                    type: integer
                    minimum: 0
                page:
                    type: integer
                    minimum: 0
                totalSkusAtRiskCount:
                    type: integer
                    minimum: 0
                totalSkusCount:
                    type: integer
                    minimum: 0
                calculations:
                    type: array
                    minItems: 0 # we might not have any calculations with a given filter
                    items:
                        $ref: '#/components/schemas/CalculationResponse'

        CalculationResponse:
            type: object
            description: Calculations for an skuId and date.
            required:
                - skuId
                - skuCode
                - unusableStock
                - usableStock
                - incomingPos
                - inbound
                - consumption
                - actualConsumption
                - dailyNeed
                - closingStock
                - skuName
                - skuCategories
                - dcCode
                - atRisk
                - week
                - uom
                - status
                - expectedInboundTransferOrders
                - expectedInboundTransferOrdersQuantity
                - actualInboundTransferOrders
                - actualInboundTransferOrdersQuantity
                - expectedOutboundTransferOrders
                - expectedOutboundTransferOrdersQuantity
            properties:
                skuId:
                    type: string
                    format: uuid
                skuCode:
                    type: string
                    example: PRO-10-49040-1
                unusableStock:
                    type: number
                unusableStockDetails:
                    type: array
                    items:
                        $ref: '#/components/schemas/unusableStockByType'
                usableStock:
                    type: number
                incomingPos:
                    $ref: '#/components/schemas/ExpectedInboundResponse'
                inbound:
                    $ref: '#/components/schemas/ActualInboundResponse'
                pos:
                    type: array
                    minItems: 0
                    items:
                        type: string
                consumption:
                    type: number
                actualConsumption:
                    type: number
                dailyNeed:
                    type: number
                netNeeds:
                    type: number
                closingStock:
                    type: number
                skuName:
                    type: string
                    example: Olives 200g
                skuCategories:
                    type: string
                    example: SPI,PHF
                dcCode:
                    type: string
                atRisk:
                    type: boolean
                week:
                    $ref: '#/components/schemas/YearWeek'
                safetyStock:
                    type: number
                strategy:
                    type: string
                    description: 'The strategy used for the safety stock calculation, e.g. "ALGORITHM_FORECASTVARIANCE"'
                safetyStockNeeds:
                    type: number
                poDueIn:
                    type: integer
                    format: int64
                subIn:
                    type: integer
                subOut:
                    type: integer
                consumptionDetails:
                    $ref: '#/components/schemas/ConsumptionDetail'
                stockUpdate:
                    type: number
                uom:
                    $ref: '#/components/schemas/UomEnum'
                status:
                    $ref: '#/components/schemas/CalculationStatus'
                expectedInboundTransferOrders:
                    type: array
                    minItems: 0
                    items:
                        type: string
                expectedInboundTransferOrdersQuantity:
                    type: number
                actualInboundTransferOrders:
                    type: array
                    minItems: 0
                    items:
                        type: string
                actualInboundTransferOrdersQuantity:
                    type: number
                expectedOutboundTransferOrders:
                    type: array
                    minItems: 0
                    items:
                        type: string
                expectedOutboundTransferOrdersQuantity:
                    type: number

        ConsumptionDetail:
            type: object
            properties:
                consumption:
                    type: array
                    items:
                        $ref: '#/components/schemas/consumptionByBrand'
                prekitting:
                    type: integer
                    format: int64
                substituted:
                    type: integer
                    format: int64
        DemandsRefreshResponse:
            type: object
            required:
                - demandRefresh
            properties:
                demandRefresh:
                    type: array
                    items:
                        $ref: '#/components/schemas/DemandRefreshResponseForDc'

        DemandPerWeeksResponse:
            type: object
            required:
                - demand
            properties:
                timestamp:
                    type: string
                    format: date-time
                demand:
                    type: array
                    items:
                        $ref: '#/components/schemas/Demand'
        Demand:
            required:
                - skuId
                - dcCode
                - date
                - demandQty
            properties:
                skuId:
                    type: string
                    format: uuid
                dc:
                    type: string
                date:
                    type: string
                    format: date
                    example: "2022-03-23"
                qty:
                    type: integer
                    format: int64
                    minimum: 0
        consumptionByBrand:
            type: object
            required:
                - brand
                - qty
            properties:
                brand:
                    type: string
                    example: 'HelloFresh'
                    description: 'will have the different brand names like HelloFresh, EveryPlate, etc'
                qty:
                    type: integer
                    format: int64
        unusableStockByType:
            type: object
            required:
                - type
                - qty
            properties:
                type:
                    type: string
                    example: 'Waste'
                    description: 'will have the different unusable stock types i.e waste, donation, transfer, quarantine, shelf life, etc'
                qty:
                    type: number
        DemandRefreshResponseForDc:
            type: object
            required:
                - dcCode
                - lastUpdates
            properties:
                dcCode:
                    $ref: '#/components/schemas/DcCode'
                lastUpdates:
                    type: array
                    items:
                        $ref: '#/components/schemas/LastUpdatedDemandForWeek'

        LastUpdatedDemandForWeek:
            type: object
            required:
                - week
                - lastUpdated
            properties:
                week:
                    $ref: '#/components/schemas/YearWeek'
                lastUpdated:
                    type: string
                    format: date-time
                    description: The ISO8601 formatted UTC timestamp of the most recent demand update for this Distribution Center
                    example: 2023-01-03T10:15:30Z

        PurchaseOrderResponseItem:
            type: object
            properties:
                poId:
                    type: string
                    format: uuid
                    example: '2201VE01120_E1'
                    description: 'will be dropped in favour of poRef once the FE is switched'
                poRef:
                    type: string
                    example: '2201VE01120_E1'
                dcCode:
                    type: string
                ordered:
                    type: integer
                received:
                    type: integer
                supplierId:
                    type: string
                    format: uuid
                supplierName:
                    type: string

        PurchaseOrdersDetailResponse:
            type: object
            required:
                - pos
            properties:
                pos:
                    type: array
                    items:
                        allOf:
                            -   $ref: '#/components/schemas/PurchaseOrderResponseItem'
                        properties:
                            status:
                                $ref: '#/components/schemas/PoStatus'
                            deliveredTimes:
                                type: array
                                deprecated: true
                                items:
                                    type: string
                                    format: date-time
                                    example: 2023-01-03T10:15:30Z
                            deliveries:
                                type: array
                                items:
                                    $ref: '#/components/schemas/Delivery'
                            inboundStartTime:
                                type: string
                                format: date-time
                                example: 2023-01-03T10:12:30Z
                            inboundEndTime:
                                type: string
                                format: date-time
                                example: 2023-01-03T10:15:30Z
                            week:
                                $ref:
                                    '#/components/schemas/YearWeek'
                            asns:
                                type: array
                                items:
                                    $ref: '#/components/schemas/Asn'
        PoStatus:
            type: string
            enum: [ "Planned", "Sent", "Approved", "ASN Received", "Delivery Open", "Delivered", "Overdue" ]
        Asn:
            type: object
            description: Advanced Shipment Notice
            properties:
                asnId:
                    type: string
                plannedDeliveryTime:
                    type: string
                    format: date-time
                quantity:
                    type: integer
                    format: int64

        ActualInboundResponse:
            type: object
            deprecated: true
            description: 'Total actual inbound stock quantity'
            properties:
                amount:
                    type: number
        ExpectedInboundResponse:
            type: object
            deprecated: true
            description: 'Amount of expected inbound stock along with the list of purchase orders'
            properties:
                amount:
                    type: number

        DayOfWeekResponse:
            type: string
            enum:
                - MONDAY
                - TUESDAY
                - WEDNESDAY
                - THURSDAY
                - FRIDAY
                - SATURDAY
                - SUNDAY
            example: SUNDAY

        AdditionalFilterRequest:
            type: string
            description: A comma-separated list of additional filters
            enum:
                - with_inventory_shortage
                - with_inbound_shortage
                - with_consumption_only
                - with_projected_waste
                - with_no_consumption_only
                - with_sku_subbed_in_only
                - with_sku_subbed_out_only
                - with_net_needs_only

        ErrorResponse:
            type: object
            description: 'Describes the error from the server'
            properties:
                reason:
                    type: string
                    example: "fieldName is missing"

        SkuDetailsResponse:
            type: object
            properties:
                skuDetails:
                    type: array
                    minItems: 0
                    items:
                        $ref: '#/components/schemas/SkuDetailResponse'
        GenerateUploadUrlResponse:
            type: object
            description: 'Response containing the preSigned URL for the S3 file'
            properties:
                url:
                    type: string
                    description: 'PreSigned URL for the S3 file'

        IngredientDepletionResponse:
            type: object
            properties:
                ingredientSummary:
                    type: array
                    items:
                        $ref: '#/components/schemas/IngredientSummary'
            required:
                - ingredientSummary

        IngredientSummary:
            type: object
            properties:
                site:
                    type: string
                    description: The distribution center site code
                skuCode:
                    type: string
                    description: The SKU code
                brand:
                    type: string
                    description: The brand associated the dc of the sku
                skuName:
                    type: string
                    description: The name of the SKU
                category:
                    type: string
                    description: The category of the SKU
                uom:
                    type: string
            required:
                - site
                - brand
                - skuCode
                - skuName
                - category
                - uom

            description: The unit of measurement for the SKU
        SkuDetailResponse:
            type: object
            properties:
                expiryDate:
                    type: string
                    format: date
                    nullable: true
                dcCode:
                    $ref: '#/components/schemas/DcCode'
                isUsable:
                    type: boolean
                    example: 'true'
                quantity:
                    type: integer
                    minimum: 0
                    format: int64
                unusableStockReason:
                    type: string
                    nullable: true
                    example: "Expiry date exceeded"

        StockVarianceResponse:
            type: object
            required:
                - dc
                - skus
            properties:
                dc:
                    type: object
                    required:
                        - code
                    properties:
                        code:
                            $ref: '#/components/schemas/DcCode'
                        cleardown:
                            $ref: '#/components/schemas/DayOfWeekResponse'
                period:
                    $ref: '#/components/schemas/PeriodResponse'
                skus:
                    type: array
                    minItems: 0
                    items:
                        $ref: '#/components/schemas/SkuStockVarianceResponse'

        StockLiveVarianceReportingResponse:
            type: object
            required:
                - stockLiveVariances
                - totalCleardownVariancePercent
            properties:
                totalCleardownVariancePercent:
                    $ref: '#/components/schemas/TotalCleardownVariancePercent'
                stockLiveVariances:
                    $ref: '#/components/schemas/StockLiveVariances'

        StockInboundVarianceReportingResponse:
            type: object
            required:
                - inboundVariances
            properties:
                poVsInboundAverage:
                    type: integer
                    format: int64
                asnVsInboundAverage:
                    type: integer
                    format: int64
                inboundVariances:
                    type: array
                    items:
                        $ref: '#/components/schemas/InboundVarianceValueResponse'

        TotalCleardownVariancePercent:
            type: object
            required:
                - productionCleardownVariancePercent
                - liveCleardownVariancePercent
            properties:
                productionCleardownVariancePercent:
                    type: integer
                    format: int64
                liveCleardownVariancePercent:
                    type: integer
                    format: int64

        StockLiveVariances:
            type: array
            items:
                $ref: '#/components/schemas/StockLiveVariance'

        StockLiveVariance:
            type: object
            required:
                - skuId
                - skuCode
                - skuName
                - skuCategories
                - data
                - productionCleardownVariancePercent
                - liveCleardownVariancePercent
            properties:
                skuId:
                    type: string
                    format: uuid
                skuCode:
                    type: string
                    example: 'BAK-999'
                skuName:
                    type: string
                    example: Chicken breast 190g
                skuCategories:
                    type: string
                    example: 'SPI'
                productionCleardownVariancePercent:
                    type: integer
                    format: int64
                liveCleardownVariancePercent:
                    type: integer
                    format: int64
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/InventoryValueResponse'

        InboundVarianceValueResponse:
            type: object
            required:
                - poRef
                - skuName
                - skuId
                - skuCode
                - skuCategories
            properties:
                poRef:
                    type: string
                    example: '2201VE01120_E1'
                asnId:
                    type: string
                    example: 'ASN128989'
                supplierId:
                    type: string
                    format: uuid
                supplierName:
                    type: string
                    example: 'Chickens Farm'
                skuId:
                    type: string
                    format: uuid
                skuName:
                    type: string
                    example: 'Chicken breast 190g'
                skuCode:
                    type: string
                    example: 'BAK-999'
                skuCategories:
                    type: string
                    example: 'BAK'
                poVsInbound:
                    type: integer
                    format: int64
                asnVsInbound:
                    type: integer
                    format: int64

        InventoryValueResponse:
            type: object
            required:
                - date
                - liveClosingStock
                - closingStock
            properties:
                date:
                    type: string
                    format: date
                    description: Date for the inventory values
                    example: 2023-01-23
                inventoryQty:
                    type: number
                liveClosingStock:
                    type: number
                closingStock:
                    type: number

        SkuStockVarianceResponse:
            type: object
            description: 'the period for which this was calculated'
            required:
                - sku
                - consumption
                - inbound
                - stock
            properties:
                skuCategories:
                    type: string
                    example: SPI,PHF
                sku:
                    $ref: '#/components/schemas/SkuResponse'
                consumption:
                    type: object
                    required:
                        - calculated
                        - theoretical
                        - missing
                    properties:
                        calculated:
                            type: integer
                            format: int64
                            minimum: 0
                        theoretical:
                            type: integer
                            format: int64
                            minimum: 0
                        missing:
                            type: integer
                            format: int64
                inbound:
                    type: integer
                    format: int64
                    minimum: 0
                stock:
                    type: object
                    properties:
                        opening:
                            type: integer
                            format: int64
                            minimum: 0
                        closing:
                            type: integer
                            format: int64
                            minimum: 0
                        waste:
                            type: integer
                            format: int64
                            minimum: 0

        PeriodResponse:
            type: object
            required:
                - from
                - to
            description: 'the period for which this was calculated'
            properties:
                from:
                    $ref: '#/components/schemas/YearWeek'
                to:
                    $ref: '#/components/schemas/YearWeek'

        CalculationExperimentRequest:
            type: array
            items:
                required:
                    - date
                    - experiment
                properties:
                    date:
                        type: string
                        format: date
                        example: "2022-03-23"
                    experiment:
                        type: integer

        CalculationExperimentV2Request:
            type: array
            items:
                required:
                    - date
                    - stockUpdate
                properties:
                    date:
                        type: string
                        format: date
                        example: "2024-12-02"
                    stockUpdate:
                        type: integer


        DailyCalculationExperimentsResponse:
            type: array
            items:
                $ref: '#/components/schemas/DailyCalculationExperimentResponse'
            description: 'List of calculations sorted by date in ascending order.'

        DailyCalculationExperimentResponse:
            allOf:
                -   $ref: '#/components/schemas/CalculationExperimentResponse'
                -   required:
                        - date
                    type: object
                    properties:
                        date:
                            type: string
                            format: date
                            example: "2022-03-23"

        CalculationWeeklyExperimentsRequest:
            type: array
            items:
                required:
                    - week
                    - experiment
                properties:
                    week:
                        $ref: '#/components/schemas/YearWeek'
                    experiment:
                        type: integer

        WeeklyCalculationExperimentsResponse:
            type: array
            items:
                $ref: '#/components/schemas/WeeklyCalculationExperimentResponse'
            description: 'List of weekly calculations sorted by date in ascending order.'

        WeeklyCalculationExperimentResponse:
            allOf:
                -   $ref: '#/components/schemas/CalculationExperimentResponse'
                -   required:
                        - week
                    type: object
                    properties:
                        week:
                            $ref: '#/components/schemas/YearWeek'

        CalculationExperimentResponse:
            type: object
            required:
                - opening
                - incoming
                - inbound
                - unusable
                - closing
                - consumption
                - actualConsumption
                - uom
            description: 'The calculation for the experiment.'
            properties:
                incoming:
                    type: integer
                    minimum: 0
                inbound:
                    type: integer
                    minimum: 0
                unusable:
                    type: integer
                    minimum: 0
                closing:
                    type: integer
                    minimum: 0
                opening:
                    type: integer
                    minimum: 0
                consumption:
                    type: integer
                    minimum: 0
                experiment:
                    type: integer
                actualConsumption:
                    type: integer
                    minimum: 0
                uom:
                    $ref: '#/components/schemas/UomEnum'

        CreateNoteRequest:
            type: object
            description: attributes to create a note
            required:
                - skuId
                - weeks
                - dcCodes
                - text
            properties:
                skuId:
                    type: string
                    format: uuid
                weeks:
                    type: array
                    minItems: 1
                    items:
                        $ref: '#/components/schemas/YearWeek'
                dcCodes:
                    type: array
                    minItems: 1
                    items:
                        $ref: '#/components/schemas/DcCode'
                text:
                    type: string
        UpdateNoteRequest:
            type: object
            description: attributes to update a note
            required:
                - weeks
                - text
            properties:
                weeks:
                    type: array
                    items:
                        $ref: '#/components/schemas/YearWeek'
                text:
                    type: string

        CreateNoteResponse:
            allOf:
                -   $ref: '#/components/schemas/NoteResponse'
                -   required:
                        - skuId
                    type: object
                    properties:
                        skuId:
                            type: string
                            format: uuid

        GetNotesResponse:
            type: object
            required:
                - notesBySkuId
            properties:
                notesBySkuId:
                    type: array
                    items:
                        $ref: '#/components/schemas/NotesResponse'

        NotesResponse:
            type: object
            required:
                - skuId
                - atRisk
                - notes
            properties:
                skuId:
                    type: string
                    format: uuid
                atRisk:
                    type: array
                    minItems: 0
                    items:
                        properties:
                            dcCode:
                                $ref: '#/components/schemas/DcCode'
                            week:
                                $ref: '#/components/schemas/YearWeek'
                    example: '[{dc:"BV", week: "2022-W20"}]'

                notes:
                    type: array
                    items:
                        $ref: '#/components/schemas/NoteResponse'
                    description: 'List of notes'

        NoteResponse:
            type: object
            description: Note about a forecast calculation
            required:
                - id
                - authorEmail
                - weeks
                - text
                - createdAt
                - isEdited
            properties:
                id:
                    type: string
                    format: uuid
                authorEmail:
                    type: string
                authorName:
                    type: string
                dcCodes:
                    type: array
                    items:
                        $ref: '#/components/schemas/DcCode'
                weeks:
                    type: array
                    items:
                        $ref: '#/components/schemas/YearWeek'
                text:
                    type: string
                createdAt:
                    type: string
                    format: date-time
                    example: '2023-02-02T12:34:56Z'
                isEdited:
                    type: boolean

        SupplierLeadTimesResponse:
            type: array
            items:
                $ref: '#/components/schemas/SupplierLeadTimeResponse'
            description: 'List of suppliers with lead time'

        SupplierLeadTimeResponse:
            type: object
            description: Supplier with lead time information
            required:
                - supplierId
                - supplierName
            properties:
                supplierId:
                    type: string
                    format: uuid
                supplierName:
                    type: string
                leadTime:
                    type: integer
                    format: int64

        ProjectedWastesResponse:
            type: object
            properties:
                projectedWasteResponses:
                    type: array
                    items:
                        $ref: '#/components/schemas/ProjectedWasteResponse'
                    description: 'Projected wastes information'
                totalPages:
                    type: integer
                    minimum: 0
                page:
                    type: integer
                    minimum: 0

        ProjectedWasteResponse:
            type: object
            description: Projected waste information
            required:
                - dcCode
                - sku
            properties:
                dcCode:
                    $ref: '#/components/schemas/DcCode'
                sku:
                    $ref: '#/components/schemas/SkuResponse'
                quantity:
                    type: integer
                    minimum: 0
                    format: int64
                date:
                    type: string
                    format: date
                    example: "2022-03-23"
                isUsable:
                    type: boolean
                    example: 'true'
                unusableStockReason:
                    type: string
                    nullable: true
                    example: "Expiry date exceeded"

        Delivery:
            type: object
            description: Details of a delivery.
            required:
                - id
                - quantity
                - time
            properties:
                id:
                    type: string
                    example: '2247NI040942_O1-001'
                state:
                    type: string
                    enum:
                        - OPEN
                        - CLOSED
                quantity:
                    type: integer
                    minimum: 0
                time:
                    type: string
                    format: date-time
                    example: '2023-02-02T12:34:56Z'

        SkuDemand:
            type: object
            description: Demand qty for an sku and date in a dc.
            required:
                - skuId
                - dc
                - date
                - quantity
            properties:
                skuId:
                    type: string
                    format: UUID
                dc:
                    $ref: '#/components/schemas/DcCode'
                date:
                    type: string
                    format: date
                quantity:
                    type: integer
                    minimum: 0

        StockSimulationResponse:
            type: object
            description: Stock simulation for an sku in a dc.
            required:
                - stockUpdates
            properties:
                stockUpdates:
                    type: array
                    items:
                        $ref: '#/components/schemas/StockUpdateItem'

        StockUpdateV2Response:
            type: object
            description: Stock Update V2 Response
            required:
                - stockUpdates
                - calculations
            properties:
                stockUpdates:
                    type: array
                    items:
                        $ref: '#/components/schemas/StockUpdateV2Item'
                calculations:
                    type: array
                    minItems: 0 # we might not have any calculations with a given filter
                    items:
                        $ref: '#/components/schemas/DailyCalculationResponse'

        SkuStockUpdatesResponse:
            type: object
            description: Stock updates for an sku in a dc.
            properties:
                reason:
                    $ref: '#/components/schemas/Reason'
                reasonDetails:
                    type: string
                stockUpdates:
                    type: array
                    items:
                        $ref: '#/components/schemas/StockUpdateItem'
        StockUpdatesVersionsResponse:
            type: object
            description: All Stock updates for an sku in a dc for a certain week.
            required:
                - stockUpdates
            properties:
                stockUpdates:
                    type: array
                    items:
                        $ref: '#/components/schemas/StockUpdateWithVersions'
        Reason:
            type: string
            enum: [ "WMS Issue", "Recovery Box", "Stock Recount", "Substitution", "Waste", "Internal Usage", "Other" ]

        StockUpdateWithVersions:
            type: object
            required:
                - dc
                - date
                - skuId
                - skuCode
                - skuName
                - skuCategory
                - versions
            properties:
                dc:
                    type: string
                date:
                    type: string
                    format: date
                    example: "2024-04-17"
                skuId:
                    type: string
                    format: uuid
                skuCode:
                    type: string
                    example: 'BAK-10-31435-1'
                skuName:
                    type: string
                    example: '3P Prem Gap'
                skuCategory:
                    type: string
                    example: 'BAK'
                versions:
                    type: array
                    items:
                        $ref: '#/components/schemas/StockUpdateVersionItem'

        StockUpdateVersionItem:
            type: object
            required:
                - version
                - quantity
                - reason
                - deleted
                - username
                - updateTimeStamp
            properties:
                version:
                    type: integer
                quantity:
                    type: integer
                    format: int64
                reason:
                    type: string
                    example: 'WMS Issue'
                reasonDetails:
                    type: string
                    example: 'WMS Issue'
                deleted:
                    type: boolean
                username:
                    type: string
                updateTimeStamp:
                    type: string
                    format: date-time

        StockUpdateV2Item:
            type: object
            required:
                - date
            properties:
                date:
                    type: string
                    format: date
                    example: "2024-12-04"
                quantity:
                    type: number
                version:
                    type: integer

        StockUpdateItem:
            type: object
            required:
                - date
                - previousClosingStock
                - updatedClosingStock
                - deleted
            properties:
                date:
                    type: string
                    format: date
                    example: "2024-04-17"
                previousClosingStock:
                    type: integer
                    format: int64
                stockUpdateQuantity:
                    type: integer
                    format: int64
                updatedClosingStock:
                    type: integer
                    format: int64
                deleted:
                    type: boolean
                    example: 'false'
                version:
                    type: integer
                    format: integer

        StockUpdateRequest:
            type: object
            required:
                - reason
                - stockUpdates
            properties:
                reason:
                    $ref: '#/components/schemas/Reason'
                reasonDetails:
                    type: string
                stockUpdates:
                    type: array
                    items:
                        type: object
                        required:
                            - date
                            - stockUpdateQuantity
                            - version
                        properties:
                            date:
                                type: string
                                format: date
                                example: "2024-04-17"
                            stockUpdateQuantity:
                                type: integer
                                format: int64
                            version:
                                type: integer
                                format: integer

        StockSimulationRequest:
            type: object
            required:
                - stockUpdates
            properties:
                stockUpdates:
                    $ref: '#/components/schemas/DayStockUpdate'

        CurrentSkuStockUpdatesResponse:
            type: object
            description: Current Allowed Stock Updates.
            required:
                - stockUpdates
            properties:
                stockUpdates:
                    type: array
                    minItems: 0
                    items:
                        $ref: '#/components/schemas/StockUpdateV2Item'

        SupplyQuantityRecommendationRequest:
            type: object
            required:
                - skuId
                - dcCode
                - week
                - recommendationEnabled
                - safetyMultiplier
            properties:
                skuId:
                    type: string
                    format: uuid
                dcCode:
                    type: string
                week:
                    $ref: '#/components/schemas/YearWeek'
                recommendationEnabled:
                    type: boolean
                multiWeekEnabled:
                    type: boolean
                    default: false
                safetyMultiplier:
                    type: number
                skuRiskRating:
                    $ref: '#/components/schemas/SkuRiskRatingEnum'

        DayStockUpdate:
            type: array
            items:
                type: object
                required:
                    - date
                    - stockUpdateQuantity
                properties:
                    date:
                        type: string
                        format: date
                        example: "2024-04-17"
                    stockUpdateQuantity:
                        type: integer
                        format: int64

        sslSqr:
            type: object
            required:
                - dcCode
                - data
                - skuId
                - skuCode
                - skuName
                - skuCategory
            properties:
                dcCode:
                    $ref: '#/components/schemas/DcCode'
                skuId:
                    $ref: '#/components/schemas/SkuId'
                skuCode:
                    type: string
                    example: 'BAK-999'
                skuName:
                    type: string
                skuCategory:
                    type: string
                data:
                    type: array
                    items:
                        required:
                            - week
                            - sqrs
                        properties:
                            week:
                                $ref: '#/components/schemas/YearWeek'
                            touchlessOrdering:
                                type: boolean
                                default: false
                            sqrs:
                                type: array
                                items:
                                    $ref: '#/components/schemas/sqr'

        sqr:
            type: object
            required:
                - date
                - openingStock
                - unusableStock
                - consumption
                - bufferPercentage
                - bufferAdditional
                - sqrQuantity
                - uom
            properties:
                date:
                    type: string
                    format: date
                    example: 2025-01-23
                openingStock:
                    type: number
                unusableStock:
                    type: number
                stockUpdates:
                    type: number
                stockUpdatesVersion:
                    type: integer
                consumption:
                    type: number
                bufferPercentage:
                    type: number
                bufferAdditional:
                    type: number
                sqrQuantity:
                    type: number
                uom:
                    $ref: '#/components/schemas/UomEnum'

        sslSqrSimulationRequest:
            type: object
            required:
                - skus
            properties:
                data:
                    type: array
                    items:
                        required:
                            - week
                            - sqrs
                        properties:
                            week:
                                $ref: '#/components/schemas/YearWeek'
                            touchlessOrdering:
                                type: boolean
                                default: false
                            sqrs:
                                type: array
                                items:
                                    $ref: '#/components/schemas/sqrSimulation'

        sqrSimulation:
            type: object
            required:
                - date
            properties:
                date:
                    type: string
                    format: date
                    example: 2025-01-23
                stockUpdates:
                    type: number
                bufferPercentage:
                    type: number
                bufferAdditional:
                    type: number
                uom:
                    $ref: '#/components/schemas/UomEnum'
        sqrSku:
            type: object
            required:
                - skuId
                - skuCode
                - skuName
                - skuCategory
                - inventoryRollover
                - demand
                - supplyQuantityRecommendation
                - recommendationEnabled
                - multiWeekEnabled
                - skuRiskRating
                - state
                - updatedAt
            properties:
                skuId:
                    type: string
                    format: uuid
                    example: '111A-222B-333C-444D'
                skuCode:
                    type: string
                    example: 'BAK-999'
                skuName:
                    type: string
                    example: Chicken breast 190g
                skuCategory:
                    type: string
                    example: 'PHF'
                inventoryRollover:
                    type: number
                stockUpdates:
                    type: integer
                    format: int64
                demand:
                    type: number
                safetyStock:
                    type: number
                safetyStockRiskMultiplier:
                    type: number
                skuRiskRating:
                    $ref: '#/components/schemas/SkuRiskRatingEnum'
                bufferPercentage:
                    type: number
                supplyQuantityRecommendation:
                    type: number
                recommendationEnabled:
                    type: boolean
                multiWeekEnabled:
                    type: boolean
                state:
                    $ref: '#/components/schemas/SQRStateEnum'
                uom:
                    $ref: '#/components/schemas/UomEnum'
                updatedAt:
                    type: string
                    format: date-time
                    description: The ISO8601 formatted UTC timestamp of the most recent demand update for this Distribution Center
                    example: 2023-01-03T10:15:30Z
        SQRStateEnum:
            type: string
            enum:
                - PENDING
                - PROCESSED
        UomEnum:
            type: string
            enum:
                - UOM_UNIT
                - UOM_KG
                - UOM_LBS
                - UOM_OZ
                - UOM_LITRE
                - UOM_GAL
                - UOM_UNRECOGNIZED

        CalculationStatus:
            type: string
            enum:
                - PENDING
                - PROCESSED

        SkuRiskRatingEnum:
            type: string
            enum:
                - CRITICAL
                - HIGH
                - MEDIUM
                - MEDIUM_LOW
                - LOW
            example: MEDIUM
            default: MEDIUM

        supplyQuantityRecommendationResponse:
            type: object
            description: Supply quantity recommendation
            required:
                - dcCode
                - week
                - skus
            properties:
                dcCode:
                    $ref: '#/components/schemas/DcCode'
                week:
                    $ref: '#/components/schemas/YearWeek'
                skus:
                    type: array
                    items:
                        $ref: '#/components/schemas/sqrSku'
        shortShelfLifeUpdateRequest:
            type: object
            required:
                - sqrs
            properties:
                touchlessOrdering:
                    type: boolean
                    default: false
                stockUpdate:
                    type: object
                    required:
                        - reason
                        - reasonDetails
                    properties:
                        reason:
                            $ref: '#/components/schemas/Reason'
                        reasonDetails:
                            type: string
                sqrs:
                    type: array
                    items:
                        required:
                            - date
                        properties:
                            date:
                                type: string
                                format: date
                                example: 2025-01-23
                            stockUpdates:
                                type: number
                            stockUpdatesVersion:
                                type: integer
                            bufferPercentage:
                                type: number
                            bufferAdditional:
                                type: number

        shortShelfLifePutResponse:
            $ref: '#/components/schemas/sslSqr'
        shortShelfLifeResponse:
            type: object
            description: Short Shelf Life Response
            required:
                - skus
                - totalNoOfSkus
            properties:
                skus:
                    type: array
                    items:
                        $ref: '#/components/schemas/sslSqr'
                totalNoOfSkus:
                    type: number
                    example: 10

        shortShelfLifeSimulationResponse:
            type: object
            description: Short Shelf Life Simulation Response
            required:
                - sslSqr
            properties:
                sslSqr:
                   $ref: '#/components/schemas/sslSqr'

        fileUploadResponse:
            type: object
            description: File uploads
            required:
                - id
                - fileName
                - market
                - dcCodes
                - authorName
                - authorEmail
                - createdAt
                - status
                - message
                - fileType
            properties:
                id:
                    type: string
                    format: uuid
                    example: 'b3fc9c10-cb68-4ec3-a576-894ada82ec95'
                fileName:
                    type: string
                market:
                    $ref: '#/components/schemas/MarketResponse'
                dcCodes:
                    type: array
                    items:
                        $ref: '#/components/schemas/DcCode'
                authorName:
                    type: string
                authorEmail:
                    type: string
                createdAt:
                    type: string
                    format: date-time
                    example: '2025-03-18T13:34:56Z'
                message:
                    type: string
                status:
                    type: string
                    enum:
                        - Imported
                        - Error
                fileType:
                    type: string
                    enum:
                        - STOCK_INVENTORY
                        - STOCK_UPDATE
                    x-enum-varnames:
                        - STOCK_INVENTORY
                        - STOCK_UPDATE

        TransferOrdersDetailResponse:
            type: object
            required:
                - transferOrders
            properties:
                transferOrders:
                    type: array
                    items:
                        properties:
                            transferOrderNumber:
                                type: string
                            sourceDcCode:
                                type: string
                            destinationDcCode:
                                type: string
                            status:
                                $ref: '#/components/schemas/TransferOrderStatus'
                            inboundStartTime:
                                type: string
                                format: date-time
                                example: 2023-01-03T10:12:30Z
                            inboundEndTime:
                                type: string
                                format: date-time
                                example: 2023-01-03T10:15:30Z
                            week:
                                $ref:
                                    '#/components/schemas/YearWeek'
                            skus:
                                type: array
                                items:
                                    $ref: '#/components/schemas/TransferOrderResponseSkuItem'
        TransferOrderStatus:
            type: string
            enum: [ "Reserved", "Ordered", "Delivered", "Cancelled", "Deleted" ]
        TransferOrderResponseSkuItem:
            type: object
            properties:
                skuId:
                    type: string
                    format: uuid
                supplierId:
                    type: string
                    format: uuid
                supplierName:
                    type: string
                deliveries:
                    type: array
                    items:
                        $ref: '#/components/schemas/Delivery'

    securitySchemes:
        BearerAuth:
            type: http
            scheme: bearer
