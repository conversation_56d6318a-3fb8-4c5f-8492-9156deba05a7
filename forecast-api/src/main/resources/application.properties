application.name=forecast-api
db.connections=10
db.master.connections=3
db.timeout=PT30S

boot.warmup=true

calculation.csv.parallelism=5

HF_STATSIG_SDK_KEY=statsig-sdk-key

HF_AZURE_ISSUER=issuer
HF_AZURE_CLIENT_ID=test
HF_AZURE_JWKS_URI=https://azure-jwks-uri

csv.brands.consumption.markets=AU,NZ

#The market where the demand type will be shown in exported files
demand.type.enabled.markets=AU

# Apply the prekitting demand from the previous to the cleardown day for pre-production for these dcs.
# It should be in sync with calculator job or move it as featureflag or dynamic config
preproduction.cleardown.dcs=VE
