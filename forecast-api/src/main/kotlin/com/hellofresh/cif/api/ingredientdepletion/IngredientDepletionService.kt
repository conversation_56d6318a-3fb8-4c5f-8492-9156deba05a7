package com.hellofresh.cif.api.ingredientdepletion

import com.hellofresh.cif.distributionCenterLib.DcConfigService

class IngredientDepletionService(
    private val ingredientDepletionRepository: IngredientDepletionRepository,
    private val dcConfigService: DcConfigService,
) {
    suspend fun getIngredientDepletionData(
        brand: String,
        dcCodes: List<String>?
    ): IngredientDepletionResponseDto {
        val brandDcCodes = dcCodes ?: dcConfigService.dcConfigurations.values.filter { it.brands.contains(brand) }
            .map { it.dcCode }
        return ingredientDepletionRepository.getIngredientDepletionData(brandDcCodes)
    }
}
