package com.hellofresh.cif.api.ingredientdepletion

import com.hellofresh.cif.models.SkuUOM
import kotlinx.serialization.Serializable

@Serializable
data class IngredientDepletionResponseDto(
    val ingredientSummary: List<IngredientSummaryDto>
)

@Serializable
data class IngredientSummaryDto(
    val site: String,
    val skuCode: String,
    val skuName: String,
    val category: String,
    val uom: SkuUOM
)
