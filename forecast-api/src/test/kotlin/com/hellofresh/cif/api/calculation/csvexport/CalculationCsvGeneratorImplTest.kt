package com.hellofresh.cif.api.calculation.csvexport

import com.hellofresh.cif.api.calculation.AdditionalFilter
import com.hellofresh.cif.api.calculation.CalculationRequest
import com.hellofresh.cif.api.calculation.CalculationsPage
import com.hellofresh.cif.api.calculation.CalculationsService
import com.hellofresh.cif.api.calculation.DailyView
import com.hellofresh.cif.api.calculation.InventoryRefreshType
import com.hellofresh.cif.api.calculation.Page
import com.hellofresh.cif.api.calculation.SortBy
import com.hellofresh.cif.api.calculation.WeeklyView
import com.hellofresh.cif.api.calculation.csvexport.CalculationViewType.DAILY
import com.hellofresh.cif.api.calculation.csvexport.CalculationViewType.WEEKLY
import com.hellofresh.cif.api.fileexport.FileExportService
import com.hellofresh.cif.api.fileexport.repository.FileExportRequest
import com.hellofresh.cif.api.schema.enums.ExportStatus.Completed
import com.hellofresh.cif.api.schema.enums.ExportStatus.Failed
import com.hellofresh.cif.api.schema.enums.ExportStatus.Pending
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.purchaseorder.PurchaseOrderRepository
import com.hellofresh.cif.s3.FileObject
import com.hellofresh.cif.s3.S3FileService
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import java.net.URI
import java.time.DayOfWeek.MONDAY
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class CalculationCsvGeneratorImplTest {

    private val fileExportService = mockk<FileExportService>()
    private val s3FileService = mockk<S3FileService>(relaxed = true)
    private val calculationsService = mockk<CalculationsService>()
    private val purchaseOrderRepository = mockk<PurchaseOrderRepository>()
    private val dcConfigService = mockk<DcConfigService>()
    private val calculationsCsvConverter = mockk<CalculationsCsvConverter>()

    private val calculationCsvGenerator =
        CalculationCsvGeneratorImpl(
            fileExportService,
            s3FileService,
            calculationsService,
            PurchaseOrderCSVService(purchaseOrderRepository, dcConfigService),
            calculationsCsvConverter,
            SimpleMeterRegistry(),
            "",
        )

    private val calculationRequest = CalculationRequest(
        dcCodes = listOf("dc1", "dc2"),
        weeks = listOf("2024-W34", "2024-W35"),
        pageRequest = Page(page = 1, skuCount = 50),
        skuCodes = listOf("skuCodes"),
        skuCategories = listOf("skuCategories"),
        additionalFilters = setOf(AdditionalFilter.WITH_INBOUND_SHORTAGE, AdditionalFilter.WITH_CONSUMPTION_ONLY),
        consumptionDaysAhead = 1,
        inventoryRefreshType = InventoryRefreshType.CLEARDOWN,
        sortBy = SortBy.SKU_NAME,
        locationInBox = listOf("locationInBox"),
        supplierIds = listOf(UUID.randomUUID(), UUID.randomUUID()),
        poDueInMax = 2,
        poDueInMin = 4,
        closingStockLessThanOrEqual = 21342,
    )

    private val fileExportRequest =
        FileExportRequest(
            requestId = UUID.randomUUID(),
            jsonParameters = null,
            status = Pending,
            fileUrl = null,
            createdAt = LocalDateTime.now(ZoneOffset.UTC),
            updatedAt = LocalDateTime.now(ZoneOffset.UTC),
        )

    @BeforeEach
    fun beforeEach() {
        every {
            dcConfigService.dcConfigurations
        } returns emptyMap()
    }

    @Test
    fun `generation is done week by week for daily view`() {
        val daileView1 = mockk<DailyView>()
        val daileView2 = mockk<DailyView>()
        coEvery {
            calculationsService.getDailyCalculations(calculationRequest.copy(weeks = calculationRequest.weeks.take(1)))
        } returns CalculationsPage(listOf(daileView1), 1, 0, 1)

        coEvery {
            calculationsService.getDailyCalculations(calculationRequest.copy(weeks = calculationRequest.weeks.drop(1)))
        } returns CalculationsPage(listOf(daileView2), 1, 0, 1)

        val csvContent = "theContent"
        coEvery {
            calculationsCsvConverter.convertDailyToCsv(listOf(daileView1, daileView2), emptyList())
        } returns csvContent

        val fileObject = FileObject("key", Instant.now())
        coEvery { s3FileService.putObject(any(), any(), csvContent, any()) } returns fileObject

        val url = "http://localhost"
        coEvery { s3FileService.getPresignedGetUrl(any(), fileObject.key, any()) } returns URI(url).toURL()

        val expectedFileExportRequest = fileExportRequest.copy(status = Completed, fileUrl = url.toString())
        coEvery { fileExportService.updateFileExportRequest(expectedFileExportRequest) } returns expectedFileExportRequest

        val result = runBlocking {
            calculationCsvGenerator.generateCsvFile(
                DAILY,
                calculationRequest,
                fileExportRequest,
            )
        }

        assertEquals(expectedFileExportRequest, result)
    }

    @Test
    fun `generation is done week by week for weekly view`() {
        val weeklyView1 = mockk<WeeklyView>()
        val weeklyView2 = mockk<WeeklyView>()
        coEvery {
            calculationsService.getWeeklyCalculations(calculationRequest.copy(weeks = calculationRequest.weeks.take(1)))
        } returns CalculationsPage(listOf(weeklyView1), 1, 0, 1)

        coEvery {
            calculationsService.getWeeklyCalculations(calculationRequest.copy(weeks = calculationRequest.weeks.drop(1)))
        } returns CalculationsPage(listOf(weeklyView2), 1, 0, 1)

        val csvContent = "theContent"
        coEvery {
            calculationsCsvConverter.convertWeeklyToCsv(listOf(weeklyView1, weeklyView2), emptyList())
        } returns csvContent

        val fileObject = FileObject("key", Instant.now())
        coEvery { s3FileService.putObject(any(), any(), csvContent, any()) } returns fileObject

        val url = "http://localhost"
        coEvery { s3FileService.getPresignedGetUrl(any(), fileObject.key, any()) } returns URI(url).toURL()

        val expectedFileExportRequest = fileExportRequest.copy(status = Completed, fileUrl = url.toString())
        coEvery { fileExportService.updateFileExportRequest(expectedFileExportRequest) } returns expectedFileExportRequest

        val result = runBlocking {
            calculationCsvGenerator.generateCsvFile(
                WEEKLY,
                calculationRequest,
                fileExportRequest,
            )
        }

        assertEquals(expectedFileExportRequest, result)
    }

    @Test
    fun `csv generation fails when fetching calculation data`() {
        coEvery {
            calculationsService.getDailyCalculations(calculationRequest.copy(weeks = calculationRequest.weeks.take(1)))
        } throws Exception()

        val expectedFileExportRequest = fileExportRequest.copy(status = Failed, fileUrl = null)
        coEvery { fileExportService.updateFileExportRequest(expectedFileExportRequest) } returns expectedFileExportRequest

        val result = runBlocking {
            calculationCsvGenerator.generateCsvFile(
                DAILY,
                calculationRequest,
                fileExportRequest,
            )
        }

        assertEquals(expectedFileExportRequest, result)
    }

    @Test
    fun `generation fails when s3 action fails`() {
        val newRequest = calculationRequest.copy(weeks = calculationRequest.weeks.take(1))
        val dailyView1 = mockk<DailyView>()
        coEvery {
            calculationsService.getDailyCalculations(newRequest)
        } returns CalculationsPage(listOf(dailyView1), 1, 0, 1)

        val csvContent = "theContent"
        coEvery { calculationsCsvConverter.convertDailyToCsv(listOf(dailyView1), emptyList()) } returns csvContent

        coEvery { s3FileService.putObject(any(), any(), csvContent, any()) } throws Exception()

        val expectedFileExportRequest = fileExportRequest.copy(status = Failed, fileUrl = null)
        coEvery { fileExportService.updateFileExportRequest(expectedFileExportRequest) } returns expectedFileExportRequest

        val result = runBlocking {
            calculationCsvGenerator.generateCsvFile(
                DAILY,
                calculationRequest,
                fileExportRequest,
            )
        }

        assertEquals(expectedFileExportRequest, result)
    }

    @Test
    fun `generation fetches purchase orders for requested weeks`() {
        val dcCode = "DC"
        val dcWeek1 = DcWeek(LocalDate.now(UTC).plusWeeks(1), MONDAY)
        val dcWeek2 = DcWeek(LocalDate.now(UTC).plusWeeks(2), MONDAY)
        val calcRequest = calculationRequest.copy(
            dcCodes = listOf(dcCode),
            weeks = listOf(dcWeek1.value, dcWeek2.value),
        )

        every {
            dcConfigService.dcConfigurations
        } returns mapOf(dcCode to DistributionCenterConfiguration.default(dcCode).copy(productionStart = MONDAY))

        val daileView1 = mockk<DailyView>()
        val daileView2 = mockk<DailyView>()

        coEvery {
            calculationsService.getDailyCalculations(any())
        } returns CalculationsPage(listOf(daileView1), 1, 0, 1) andThen CalculationsPage(listOf(daileView2), 1, 0, 1)

        val po1 = mockk<PurchaseOrder>()
        val po2 = mockk<PurchaseOrder>()
        coEvery {
            purchaseOrderRepository.findPurchaseOrders(
                setOf(dcCode),
                DateRange(dcWeek1.getStartDateInDcWeek(MONDAY, UTC), dcWeek1.getLastDateInDcWeek(MONDAY, UTC)),
            )
        } returns listOf(po1)
        coEvery {
            purchaseOrderRepository.findPurchaseOrders(
                setOf(dcCode),
                DateRange(dcWeek2.getStartDateInDcWeek(MONDAY, UTC), dcWeek2.getLastDateInDcWeek(MONDAY, UTC)),
            )
        } returns listOf(po2)

        val csvContent = "theContent"
        coEvery {
            calculationsCsvConverter.convertDailyToCsv(listOf(daileView1, daileView2), listOf(po1, po2))
        } returns csvContent

        val fileObject = FileObject("key", Instant.now())
        coEvery { s3FileService.putObject(any(), any(), csvContent, any()) } returns fileObject

        val url = "http://localhost"
        coEvery { s3FileService.getPresignedGetUrl(any(), fileObject.key, any()) } returns URI(url).toURL()

        val expectedFileExportRequest = fileExportRequest.copy(status = Completed, fileUrl = url.toString())
        coEvery { fileExportService.updateFileExportRequest(expectedFileExportRequest) } returns expectedFileExportRequest

        val result = runBlocking {
            calculationCsvGenerator.generateCsvFile(
                DAILY,
                calcRequest,
                fileExportRequest,
            )
        }

        assertEquals(expectedFileExportRequest, result)
    }
}
