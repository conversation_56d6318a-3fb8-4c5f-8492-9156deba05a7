package com.hellofresh.cif.api.calculation

import com.hellofresh.cif.api.calculation.InventoryRefreshType.CLEARDOWN
import com.hellofresh.cif.api.calculation.fixtures.DEFAULT_TEST_STRATEGY
import com.hellofresh.cif.api.calculation.generated.model.UnusableStockByType
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import java.math.BigDecimal
import java.math.BigDecimal.ONE
import java.math.BigDecimal.TEN
import java.math.BigDecimal.ZERO
import java.time.DayOfWeek.MONDAY
import java.time.DayOfWeek.TUESDAY
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

const val DAYS = 7

class UnusableStockDecoratorTest {

    private val dcConfigService = mockk<DcConfigService>()

    private val defaultDc = DistributionCenterConfiguration.Companion.default("D1").copy(
        productionStart = TUESDAY,
        cleardown = MONDAY,
    )
    private val otherDc = DistributionCenterConfiguration.Companion.default("D2").copy(
        productionStart = defaultDc.productionStart,
        cleardown = defaultDc.productionStart,
    )

    @BeforeEach
    fun beforeEach() {
        every { dcConfigService.dcConfigurations } returns listOf(defaultDc, otherDc).associateBy { it.dcCode }
    }

    @Test
    fun `should launch extra calculation request with previous weeks when production start and cleardown doesnt match for dc`() {
        val week1 = ProductionWeek(LocalDate.now(), defaultDc.productionStart)
        val week2 = week1.plusWeeks(5)
        val weeks = setOf(week1, week2)
        val calculationRequest = CalculationRequest(
            listOf(defaultDc.dcCode, otherDc.dcCode),
            weeks.map { it.dcWeek.value },
            AllPages,
            consumptionDaysAhead = 0,
            inventoryRefreshType = CLEARDOWN,
        )

        val originalCalculationsPage = CalculationsPage(
            calculationPage = dailyViewsWithUnusable(
                Sku(UUID.randomUUID(), "PHF-0001", "Sku Name ", ""),
                week1,
                defaultDc,
            ),
            totalPages = 1,
            totalSkuAtRiskCount = 0,
            totalSkuCount = 1,
        )
        val calculationQueryMock = mockk<suspend ((CalculationRequest) -> CalculationsPage<DailyView>)>()
        coEvery { calculationQueryMock.invoke(any()) } returns emptyCalculationPage

        val result = runBlocking {
            UnusableStockDecorator(
                calculationRequest,
                dcConfigService,
            ).decorate(originalCalculationsPage, calculationQueryMock)
        }

        assertAccumulatedDailyViews(originalCalculationsPage.calculationPage, result.calculationPage, defaultDc)
        assertEquals(originalCalculationsPage.totalPages, result.totalPages)
        assertEquals(originalCalculationsPage.totalSkuCount, result.totalSkuCount)
        assertEquals(originalCalculationsPage.totalSkuAtRiskCount, result.totalSkuAtRiskCount)

        coVerify(exactly = 1) {
            calculationQueryMock.invoke(
                CalculationRequest(
                    dcCodes = calculationRequest.dcCodes,
                    weeks = weeks.map { it.minusWeeks(1).dcWeek.value },
                    pageRequest = AllPages,
                    consumptionDaysAhead = calculationRequest.consumptionDaysAhead,
                    inventoryRefreshType = calculationRequest.inventoryRefreshType,
                ),
            )
        }
    }

    @Test
    fun `should accumulate unusable stock using scheduled cleardowns`() {
        val week = ProductionWeek(LocalDate.now(), defaultDc.productionStart)
        val previousWeek = week.minusWeeks(1)

        val sku1 = Sku(UUID.randomUUID(), "PHF-0001", "Sku Name1 ", "")
        val sku2 = Sku(UUID.randomUUID(), "PHF-0002", "Sku Name2 ", "")
        val dailyViewSku1 = dailyViewsWithUnusable(sku1, week, defaultDc)
        val dailyViewSku1Previous = dailyViewsWithUnusable(sku1, previousWeek, defaultDc)
        val dailyViewSku2 = dailyViewsWithUnusable(sku2, week, otherDc)

        val calculationRequest = CalculationRequest(
            listOf(defaultDc.dcCode, otherDc.dcCode),
            listOf(week.dcWeek.value),
            Page(1, 6),
            consumptionDaysAhead = 0,
            inventoryRefreshType = CLEARDOWN,
        )

        val calculationsPage = CalculationsPage(
            calculationPage = dailyViewSku1 + dailyViewSku2,
            totalPages = 1,
            totalSkuCount = 2,
            totalSkuAtRiskCount = 0,
        )
        val calculationQueryMock = mockk<suspend ((CalculationRequest) -> CalculationsPage<DailyView>)>()
        coEvery {
            calculationQueryMock.invoke(
                CalculationRequest(
                    dcCodes = calculationRequest.dcCodes,
                    weeks = listOf(previousWeek.dcWeek.value),
                    pageRequest = AllPages,
                    consumptionDaysAhead = calculationRequest.consumptionDaysAhead,
                    inventoryRefreshType = calculationRequest.inventoryRefreshType,
                    skuCodes = calculationsPage.calculationPage.map { it.sku.skuCode }.distinct().toList(),
                ),
            )
        } returns CalculationsPage(
            calculationPage = dailyViewSku1Previous,
            totalPages = 1,
            totalSkuCount = 1,
            totalSkuAtRiskCount = 0,
        )

        val calculationsPageResult = runBlocking {
            UnusableStockDecorator(calculationRequest, dcConfigService).decorate(calculationsPage, calculationQueryMock)
        }.calculationPage

        assertEquals(dailyViewSku1.size, calculationsPageResult.filter { it.sku == sku1 }.size)
        assertAccumulatedDailyViews(
            dailyViewSku1 + dailyViewSku1Previous,
            calculationsPageResult
                .filter { it.sku == sku1 && it.calculation.dcCode == defaultDc.dcCode && it.productionDay.week == week.dcWeek.value },
            defaultDc,
        )

        assertEquals(dailyViewSku2.size, calculationsPageResult.filter { it.sku == sku2 }.size)
        assertAccumulatedDailyViews(
            dailyViewSku2,
            calculationsPageResult
                .filter { it.sku == sku2 && it.calculation.dcCode == otherDc.dcCode && it.productionDay.week == week.dcWeek.value },
            otherDc,
        )
    }

    @Test
    fun `should accumulate unusable when production start and cleardown matches for dc`() {
        val week = ProductionWeek(LocalDate.now(), otherDc.productionStart)
        val sku = Sku(UUID.randomUUID(), "PHF-0002", "Sku Name2 ", "")
        val dailyViewSku = dailyViewsWithUnusable(sku, week, otherDc)
        val calculationRequest = CalculationRequest(
            listOf(otherDc.dcCode),
            listOf(week.dcWeek.value),
            AllPages,
            consumptionDaysAhead = 0,
            inventoryRefreshType = CLEARDOWN,
        )

        val originalCalculationsPage = CalculationsPage(
            calculationPage = dailyViewSku,
            totalPages = 1,
            totalSkuCount = 2,
            totalSkuAtRiskCount = 0,
        )

        val calculationQueryMock = mockk<suspend ((CalculationRequest) -> CalculationsPage<DailyView>)>()

        val calculationsPage = runBlocking {
            UnusableStockDecorator(calculationRequest, dcConfigService)
                .decorate(originalCalculationsPage, calculationQueryMock)
        }.calculationPage

        assertEquals(dailyViewSku.size, calculationsPage.size)
        assertAccumulatedDailyViews(dailyViewSku, calculationsPage, otherDc)

        coVerify(exactly = 0) { calculationQueryMock.invoke(any()) }
    }

    @ParameterizedTest
    @EnumSource(InventoryRefreshType::class)
    fun `should not accumulate unusable stock when refresh type in not cleardown`(
        inventoryRefreshType: InventoryRefreshType
    ) {
        val week = ProductionWeek(LocalDate.now(), defaultDc.productionStart)

        val sku = Sku(UUID.randomUUID(), "PHF-0002", "Sku Name2 ", "")
        val dailyViewSku = dailyViewsWithUnusable(sku, week, otherDc)

        val calculationRequest = CalculationRequest(
            listOf(otherDc.dcCode),
            listOf(week.dcWeek.value),
            AllPages,
            consumptionDaysAhead = 0,
            inventoryRefreshType = inventoryRefreshType,
        )

        val calculationQueryMock = mockk<suspend ((CalculationRequest) -> CalculationsPage<DailyView>)>()
        val calculationsPage = runBlocking {
            UnusableStockDecorator(calculationRequest, dcConfigService)
                .decorate(
                    CalculationsPage(
                        calculationPage = dailyViewSku,
                        totalPages = 1,
                        totalSkuCount = 2,
                        totalSkuAtRiskCount = 0,
                    ),
                    calculationQueryMock,
                )
        }.calculationPage

        assertEquals(dailyViewSku.size, calculationsPage.size)

        if (CLEARDOWN == inventoryRefreshType) {
            assertAccumulatedDailyViews(dailyViewSku, calculationsPage, otherDc)
        } else {
            assertEquals(dailyViewSku, calculationsPage)
        }
    }

    private fun assertAccumulatedDailyViews(originalDailyViews: List<DailyView>, accumulatedDailyViews: List<DailyView>, dc: DistributionCenterConfiguration) {
        accumulatedDailyViews
            .sortedBy { it.productionDay.day }
            .forEach { dailyView ->
                val expectedViews = originalDailyViews
                    .filter { dc.getCleardown(dailyView.productionDay.day) == dc.getCleardown(it.productionDay.day) }
                    .filter { c -> c.productionDay.day <= dailyView.productionDay.day }
                assertEquals(
                    expectedViews.sumOf { it.calculation.unusableStock },
                    dailyView.calculation.unusableStock,
                )
                assertEquals(
                    expectedViews.mapNotNull { it.calculation.unusableStockDetails }.flatten()
                        .groupBy { it.type }
                        .map { (type, details) -> UnusableStockByType(type, details.sumOf { it.qty }) }.toSet(),
                    dailyView.calculation.unusableStockDetails?.toSet(),
                )
            }
    }

    companion object {

        private val emptyCalculationPage = CalculationsPage<DailyView>(
            calculationPage = emptyList(),
            totalPages = 1,
            totalSkuCount = 0,
            totalSkuAtRiskCount = 0,
        )

        fun dailyViewsWithUnusable(
            sku: Sku,
            productionWeek: ProductionWeek,
            distributionCenterConfiguration: DistributionCenterConfiguration
        ) = (0L until DAYS).map {
            val emptyInbound = ONE
            val date =
                productionWeek.dcWeek.getStartDateInDcWeek(
                    distributionCenterConfiguration.productionStart,
                    distributionCenterConfiguration.zoneId,
                ).plusDays(it)
            DailyView(
                ProductionDay(
                    productionWeek.dcWeek.value,
                    date,
                ),
                sku,
                Calculation(
                    uom = UOM_UNIT,
                    ZERO,
                    unusableStock = it.toBigDecimal(),
                    ZERO,
                    emptyInbound,
                    emptySet(),
                    ZERO,
                    ZERO,
                    ZERO,
                    ZERO,
                    distributionCenterConfiguration.dcCode,
                    false,
                    null,
                    strategy = DEFAULT_TEST_STRATEGY,
                    null,
                    ZERO,
                    ZERO,
                    null,
                    TEN,
                    0,
                    unusableStockDetails = listOf(UnusableStockByType("${it % 2}", BigDecimal(it))),
                ),
            )
        }
    }
}
