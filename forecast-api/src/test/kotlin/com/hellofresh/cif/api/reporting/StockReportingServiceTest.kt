package com.hellofresh.cif.api.reporting

import com.hellofresh.cif.api.calculation.CulinarySkuFixture
import com.hellofresh.cif.api.calculation.FIXTURE_DC
import com.hellofresh.cif.api.calculation.emptyCalculationRecord
import com.hellofresh.cif.api.calculation.firstCulinarySku
import com.hellofresh.cif.api.calculation.secondCulinarySku
import com.hellofresh.cif.api.calculation.thirdCulinarySku
import com.hellofresh.cif.api.configuration.Sku
import com.hellofresh.cif.api.po.PurchaseOrderService
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.Asn
import com.hellofresh.cif.models.purchaseorder.PoSkuVariance
import com.hellofresh.cif.models.purchaseorder.PoVariance
import com.hellofresh.cif.models.purchaseorder.Supplier
import io.mockk.coEvery
import io.mockk.mockk
import java.math.BigDecimal
import java.time.DayOfWeek
import java.time.DayOfWeek.FRIDAY
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.temporal.TemporalAdjusters
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class StockReportingServiceTest {
    private val stockReportingRepositoryMock: StockReportingRepository = mockk()
    private val purchaseOrderServiceMock: PurchaseOrderService = mockk()
    private val sut = StockReportingService(stockReportingRepositoryMock, purchaseOrderServiceMock)
    private val cleardownDay = DayOfWeek.valueOf("FRIDAY")
    private val closestCleardownDate = LocalDate.now().with(TemporalAdjusters.previousOrSame(cleardownDay))

    @Test
    fun `should aggregate stock variance for a sku`() {
        // given
        val sku = firstCulinarySku()
        val calculations = weekCalculations(sku, 1, 2)

        coEvery {
            stockReportingRepositoryMock.stockVarianceFromCleardown(FIXTURE_DC, closestCleardownDate)
        } returns listOf(calculations)

        // when
        val reports = runBlocking { sut.getStockVarianceReport(FIXTURE_DC, closestCleardownDate) }

        // then
        assertEquals(1, reports.size)
        val report = reports.first()
        assertReport(calculations, report)
    }

    @Test
    fun `should aggregate stock variances for multiple skus`() {
        // given
        val calculations = listOf(firstCulinarySku(), secondCulinarySku(), thirdCulinarySku()).mapIndexed { ix, sku ->
            weekCalculations(sku, ix.toLong(), ix + 1L)
        }
        coEvery {
            stockReportingRepositoryMock.stockVarianceFromCleardown(FIXTURE_DC, closestCleardownDate)
        } returns calculations

        // when
        val report = runBlocking { sut.getStockVarianceReport(FIXTURE_DC, closestCleardownDate) }

        // then
        assertEquals(3, report.size)
        report.forEachIndexed { ix, r ->
            assertReport(calculations[ix], r)
        }
    }

    // This is the variance example from https://docs.google.com/spreadsheets/d/1sBsp1RzDtqr8KxEX9_DbdpyBw1wuVPTOo2Ug908AVnk/edit?usp=sharing
    @Test
    fun `should correctly calculate variance from example`() {
        val cleardownDate = LocalDate.now()
        val givenWeek = DcWeek(cleardownDate, FRIDAY).toString()

        val inputs = listOf(
            DayValues(cleardownDate.minusDays(7), 1040, 600, 510),
            DayValues(cleardownDate.minusDays(6), 1130, 540, 455),
            DayValues(cleardownDate.minusDays(5), 1215, 650, 864),
            DayValues(cleardownDate.minusDays(4), 1001, 0, 259),
            DayValues(cleardownDate.minusDays(3), 742, 600, 542),
            DayValues(cleardownDate.minusDays(2), 800, 550, 533),
            DayValues(cleardownDate.minusDays(1), 817, 270, 411),
            DayValues(cleardownDate, 4250, 200, 104),
        )
        val culinarySkuFixture = firstCulinarySku()
        val calculations = inputs.map {
            emptyCalculationRecord(givenWeek, cleardownDate.minusWeeks(1), culinarySkuFixture)
                .apply {
                    openingStock = it.openingStock.toBigDecimal()
                    expired = BigDecimal.ZERO
                    actualInbound = it.actualInbound.toBigDecimal()
                    demanded = it.demand.toBigDecimal()
                }
        }
        val stockCalculation = calculations.map {
            StockCalculation(
                culinarySkuFixture.toSkuSpecificationViewRecord(),
                it,
            )
        }
        val weekCalculations = WeekCalculations(
            stockCalculation.last(),
            stockCalculation.dropLast(1),
        )

        coEvery {
            stockReportingRepositoryMock.stockVarianceFromCleardown(any(), cleardownDate)
        } returns listOf(weekCalculations)

        // when
        val report = runBlocking { sut.getStockVarianceReport("any", cleardownDate) }

        // then
        assertEquals(1, report.size)
        val variance = report.first()
        assertEquals(1040, variance.openingStockBeginningOfWeek)
        assertEquals(3210, variance.weekActualInbound)
        assertEquals(0, variance.weekUnusable)
        assertEquals(0, variance.calculatedConsumption)
        assertEquals(3574, variance.weekConsumption)
        assertEquals(3574, variance.unallocatedStock)
    }

    @Test
    fun `should return an empty stock variance report if no variance is found in the DB`() {
        // given
        coEvery { stockReportingRepositoryMock.stockVarianceFromCleardown(any(), LocalDate.now()) } returns emptyList()

        // when
        val report = runBlocking { sut.getStockVarianceReport("any", LocalDate.now()) }

        // then
        assertTrue(report.isEmpty())
    }

    @ParameterizedTest
    @MethodSource("getInboundVarianceInputs")
    fun `should return inbound variance percentage for the given inputs`(
        expectedQuantity: Long?,
        deliveredQuantity: Long?,
        shippedQuantity: Long?,
        poVsInboundVariance: Long?,
        asnVsInboundVariance: Long?,
    ) {
        val poReference = "test-po-reference"
        val skuId = UUID.randomUUID()
        val skuName = "test-sku-name"
        val skuCode = "test-sku-code"
        val skuCategory = "test-sku-category"
        val asnId = "test-asn-id"
        val supplierName = "test-supplier-name"
        val purchaseOrderSkuForInboundVariance = PoSkuVariance(
            skuId = skuId,
            skuName = skuName,
            skuCode = skuCode,
            skuCategory = skuCategory,
            expectedQuantity = if (expectedQuantity != null) SkuQuantity.fromLong(expectedQuantity) else null,
            deliveredQuantity = if (deliveredQuantity != null) SkuQuantity.fromLong(deliveredQuantity) else null,
        )

        val asnsList = if (shippedQuantity == null) {
            emptyList()
        } else {
            listOf(
                Asn(
                    id = asnId,
                    skuId = skuId,
                    plannedDeliveryTime = ZonedDateTime.now(),
                    shippedQuantity = SkuQuantity.fromLong(shippedQuantity),
                )
            )
        }

        val poVariance = PoVariance(
            poReference = poReference,
            poId = UUID.randomUUID(),
            supplier = Supplier(UUID.randomUUID(), supplierName),
            poSkuVariances = listOf(purchaseOrderSkuForInboundVariance),
            asns = asnsList,
        )
        val purchaseOrderForInboundVariances = listOf(poVariance)
        coEvery {
            purchaseOrderServiceMock.findPoVariance(any(), any())
        } returns purchaseOrderForInboundVariances

        val report = runBlocking { sut.getStockInboundVarianceReport("IT", DcWeek("2023-W30")) }

        val stockInboundVarianceSku = report.first()
        assertEquals(poReference, stockInboundVarianceSku.poRef)
        assertEquals(supplierName, stockInboundVarianceSku.supplierName)
        assertEquals(skuId, stockInboundVarianceSku.skuId)
        assertEquals(skuId, stockInboundVarianceSku.skuId)
        assertEquals(skuName, stockInboundVarianceSku.skuName)
        assertEquals(skuCode, stockInboundVarianceSku.skuCode)
        assertEquals(skuCategory, stockInboundVarianceSku.skuCategory)
        if (asnsList.isNotEmpty()) assertEquals(asnId, stockInboundVarianceSku.asnId)
        if (asnsList.isEmpty()) assertNull(stockInboundVarianceSku.asnId)
        assertEquals(poVsInboundVariance, stockInboundVarianceSku.poVsInbound?.getValue()?.toLong())
        when (asnVsInboundVariance) {
            0L -> assertEquals(asnVsInboundVariance, stockInboundVarianceSku.asnVsInbound?.getValue()?.toLong() ?: 0)
            null -> assertEquals(asnVsInboundVariance, stockInboundVarianceSku.asnVsInbound?.getValue()?.toLong())
        }
    }
    private fun assertReport(
        givenWeekCalculations: WeekCalculations,
        report: StockVariance,
    ) {
        val latestCleardownCalculation = givenWeekCalculations.latestCleardown
        val previousWeekCleardownCalculation = givenWeekCalculations.previousWeek.first()
        val openingStockLatestCleardown = latestCleardownCalculation.calculationRecord.openingStock
        val previousWeekCalculations = givenWeekCalculations.previousWeek

        val expected = Sku(
            latestCleardownCalculation.sku.code,
            latestCleardownCalculation.sku.id,
            latestCleardownCalculation.sku.name,
        )
        assertEquals(expected, report.sku)
        assertEquals(latestCleardownCalculation.calculationRecord.date.dayOfWeek, report.cleardownDay)
        assertEquals(latestCleardownCalculation.calculationRecord.productionWeek, report.currentCleardownWeek.value)
        assertEquals(
            previousWeekCleardownCalculation.calculationRecord.productionWeek,
            report.previousCleardownWeek.value
        )

        assertEquals(
            latestCleardownCalculation.calculationRecord.openingStock.toLong(),
            report.openingStockLatestCleardown
        )
        assertEquals(
            previousWeekCleardownCalculation.calculationRecord.openingStock.toLong(),
            report.openingStockPreviousCleardown
        )
        assertEquals(
            previousWeekCalculations.sumOf { it.calculationRecord.actualInbound }.toLong(),
            report.weekActualInbound
        )
        assertEquals(previousWeekCalculations.sumOf { it.calculationRecord.demanded }.toLong(), report.weekConsumption)
        assertEquals(previousWeekCalculations.sumOf { it.calculationRecord.expired }.toLong(), report.weekUnusable)
        assertEquals(
            previousWeekCalculations.first().calculationRecord.openingStock.toLong(),
            report.openingStockBeginningOfWeek
        )
        val givenCalculatedConsumption =
            previousWeekCalculations.first().calculationRecord.openingStock +
                previousWeekCalculations.sumOf { c -> c.calculationRecord.actualInbound } -
                previousWeekCalculations.sumOf { c -> c.calculationRecord.expired } -
                openingStockLatestCleardown
        assertEquals(givenCalculatedConsumption.toLong(), report.calculatedConsumption)
        assertEquals(
            givenCalculatedConsumption.toLong() - previousWeekCalculations.sumOf { c -> c.calculationRecord.demanded }.toLong(),
            report.missing
        )
        assertEquals(
            previousWeekCalculations.sumOf {
                it.calculationRecord.demanded
            }.toLong() - givenCalculatedConsumption.toLong(),
            report.unallocatedStock
        )
    }

    private fun weekCalculations(
        sku: CulinarySkuFixture,
        closingStockCleardown: Long,
        openingStockAfterCleardown: Long
    ): WeekCalculations {
        val cleardownDay = LocalDate.now()
        val previousCleardownDay = cleardownDay.minusWeeks(1)
        val givenWeek = DcWeek(cleardownDay, MONDAY).toString()
        val previousWeek = DcWeek(previousCleardownDay, MONDAY).toString()
        val calculationRecordCleardown = emptyCalculationRecord(givenWeek, cleardownDay, sku)
            .apply {
                closingStock = closingStockCleardown.toBigDecimal()
                actualInbound = BigDecimal(3)
            }

        val calcFirstDayOfWeek = emptyCalculationRecord(givenWeek, cleardownDay.plusDays(1), sku)
            .apply {
                openingStock = openingStockAfterCleardown.toBigDecimal()
                actualInbound = BigDecimal(11)
            }
        val calculationsRestOfWeek = (2L..7L).map {
            emptyCalculationRecord(previousWeek, cleardownDay.plusDays(it.toLong()), sku)
                .apply {
                    actualInbound = it.toBigDecimal()
                    openingStock = it.toBigDecimal()
                }
        }

        val weekCalculations = listOf(calcFirstDayOfWeek, *calculationsRestOfWeek.toTypedArray())
        val stockCalculations = weekCalculations.map {
            StockCalculation(
                sku.toSkuSpecificationViewRecord(),
                calculationRecord = it,
            )
        }
        return WeekCalculations(
            StockCalculation(
                sku.toSkuSpecificationViewRecord(),
                calculationRecord = calculationRecordCleardown,
            ),
            stockCalculations,
        )
    }
    companion object {
        @JvmStatic
        @Suppress("unused")
        private fun getInboundVarianceInputs() = Stream.of(
            Arguments.of(100L, 90L, 100L, 10L, 10L), // "(PO QTY-QTY Received)/ (PO QTY) %"
            Arguments.of(0L, 90L, 100L, 100L, 10L), // "If PO QTY received 0 then variance is 100%."
            Arguments.of(100L, 0L, 100L, 100L, 100L), // "If QTY received is 0 then variance is 100%."
            Arguments.of(0L, 0L, 100L, 0L, 100L), // "If QTY received and PO QTY is 0 then variance is 0%."
            Arguments.of(100L, 900L, 100L, 100L, 100L), // "Any variance >100% is 100%"
            Arguments.of(100L, 90L, 100L, 10L, 10L), // "(ASN QTY-QTY Received)/ (ASN QTY) %"
            Arguments.of(0L, 90L, 0L, 100L, 100L), // "If ASN QTY received 0 then variance is 100%.",
            Arguments.of(100L, 0L, 100L, 100L, 100L), // "If QTY received is 0 then variance is 100%."
            Arguments.of(100L, 0L, 0L, 100L, 0L), // "If QTY received and ASN QTY is 0 then variance is 0%."
            Arguments.of(null, 100L, 100L, null, 0L), // If PO QTY is not available then null should appear.
            Arguments.of(100L, 100L, null, 0L, null), // If ASN QTY is not available then null should appear.
            Arguments.of(100L, null, 100L, 100L, 100L), // If GRN is not available then 100% should appear.
        )
    }
}

data class DayValues(
    val date: LocalDate,
    val openingStock: Long,
    val actualInbound: Long,
    val demand: Long
)
