package com.hellofresh.cif.api.stockupdate

import com.hellofresh.cif.api.calculation.generated.model.Reason
import com.hellofresh.cif.api.calculation.generated.model.Reason.OTHER
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateItem
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateRequest
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateRequestStockUpdatesInner
import com.hellofresh.cif.api.stockupdate.repository.StockUpdateApiRepository
import com.hellofresh.cif.api.user.LoggedInUserInfo
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.models.DayCalculationResult
import com.hellofresh.cif.calculator.models.default
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepository
import com.hellofresh.cif.inventory.StockUpdate
import com.hellofresh.cif.inventory.StockUpdateService
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepository
import com.hellofresh.sku.models.SkuSpecification
import com.hellofresh.sku.models.default
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class StockUpdateApiServiceTest {

    private val stockUpdateCalculationService = mockk<StockUpdateCalculationService>()
    private val stockUpdateService = mockk<StockUpdateService>()
    private val stockUpdateApiRepository = mockk<StockUpdateApiRepository>()
    private val dcRepository = mockk<DcRepository>()
    private val skuInputDataRepository = mockk<SkuInputDataRepository>()

    private val stockUpdateApiService = StockUpdateApiService(
        stockUpdateService,
        stockUpdateCalculationService,
        stockUpdateApiRepository,
        DcConfigService(SimpleMeterRegistry(), dcRepository),
        skuInputDataRepository,
    )

    private val dcCode = "DC"
    private val skuId = UUID.randomUUID()

    @Test
    fun `getStockUpdates should call stockUpdateService and build response`() {
        val today = LocalDate.now()
        val existingStockUpdates = mapOf(
            today to buildStockUpdate(BigDecimal(100), "Waste"),
            today.minusDays(5) to null,
        )
        val mockCalculations = StockUpdateComparison(
            listOf(createDayCalculationResult(today, 200)),
            listOf(createDayCalculationResult(today, 300)),
            emptyMap(),
        )
        coEvery { stockUpdateService.getCurrentStockUpdate(dcCode, skuId) } returns existingStockUpdates
        coEvery {
            stockUpdateCalculationService.runStockUpdatesComparison(dcCode, skuId, any(), any())
        } returns mockCalculations

        runBlocking {
            val response = stockUpdateApiService.getStockUpdates(dcCode, skuId, CalculatorMode.PRODUCTION)
            coVerify { stockUpdateService.getCurrentStockUpdate(dcCode, skuId) }
            coVerify {
                stockUpdateCalculationService.runStockUpdatesComparison(
                    dcCode,
                    skuId,
                    CalculatorMode.PRODUCTION,
                    any(),
                    any()
                )
            }
            Assertions.assertEquals(2, response.stockUpdates?.size)
            Assertions.assertEquals(today, response.stockUpdates?.get(0)?.date)
            Assertions.assertEquals(300, response.stockUpdates?.get(0)?.updatedClosingStock)
            Assertions.assertEquals(200, response.stockUpdates?.get(0)?.previousClosingStock)
            Assertions.assertEquals(100, response.stockUpdates?.get(0)?.stockUpdateQuantity)
            Assertions.assertEquals(Reason.WASTE, response.reason)
        }
    }

    @Test
    fun `getStockUpdates should return null if not in enum`() {
        val today = LocalDate.now()
        val existingStockUpdates = mapOf(
            today to buildStockUpdate(BigDecimal(100), "incorrectReason"),
            today.minusDays(5) to null,
        )
        val mockCalculations = StockUpdateComparison(emptyList(), emptyList(), emptyMap())
        coEvery { stockUpdateService.getCurrentStockUpdate(dcCode, skuId) } returns existingStockUpdates
        coEvery {
            stockUpdateCalculationService.runStockUpdatesComparison(dcCode, skuId, any(), any())
        } returns mockCalculations

        runBlocking {
            val response = stockUpdateApiService.getStockUpdates(dcCode, skuId, CalculatorMode.PRODUCTION)
            coVerify { stockUpdateService.getCurrentStockUpdate(dcCode, skuId) }
            coVerify {
                stockUpdateCalculationService.runStockUpdatesComparison(
                    dcCode,
                    skuId,
                    CalculatorMode.PRODUCTION,
                    any(),
                    any()
                )
            }
            Assertions.assertEquals(null, response.reason)
        }
    }

    @ParameterizedTest
    @CsvSource("PRODUCTION", "PRE_PRODUCTION")
    fun `stockUpdates are simulated for given modes`(calculatorMode: CalculatorMode) {
        val yesterday = LocalDate.now()
        val dayBeforeYesterday = yesterday.minusDays(1)
        val stockUpdateValue = BigDecimal(100)
        val dateRange = DateRange(dayBeforeYesterday, yesterday)
        coEvery {
            stockUpdateService.getCurrentStockUpdateRange(dcCode)
        } returns dateRange

        coEvery { stockUpdateService.getCurrentStockUpdate(dcCode, skuId) } returns mapOf(
            dayBeforeYesterday to null,
            yesterday to null,
        )
        val skuSpecification = SkuSpecification.Companion.default.copy(uom = SkuUOM.entries.random())
        coEvery {
            skuInputDataRepository.fetchSkus(setOf(skuId))
        } returns mapOf(skuId to skuSpecification)

        val yesterdayCalculationResult = createDayCalculationResult(yesterday, 100)
        val yesterdayCalculationResultWithStockUpdate = yesterdayCalculationResult.copy(
            closingStock = yesterdayCalculationResult.closingStock + SkuQuantity.fromLong(50),
        )

        val dayBeforeYesterdayCalculationResult = createDayCalculationResult(dayBeforeYesterday, 200)

        coEvery {
            stockUpdateCalculationService.runStockUpdatesComparisonWithoutUom(
                dcCode, skuId, calculatorMode,
                mapOf(CalculationKey(skuId, dcCode, yesterday) to stockUpdateValue), emptySet(),
            )
        } returns StockUpdateComparison(
            defaultCalculation = listOf(dayBeforeYesterdayCalculationResult, yesterdayCalculationResult),
            stockUpdateCalculations = listOf(dayBeforeYesterdayCalculationResult, yesterdayCalculationResultWithStockUpdate),
            stockUpdates = mapOf(CalculationKey(skuId, dcCode, yesterday) to SkuQuantity.fromBigDecimal(stockUpdateValue, UOM_UNIT)),
        )

        val stockUpdates = runBlocking {
            stockUpdateApiService.simulateStockUpdates(
                dcCode,
                skuId,
                calculatorMode,
                mapOf(yesterday to stockUpdateValue)
            )
        }

        assertEquals(
            StockUpdateItem(
                dayBeforeYesterday,
                dayBeforeYesterdayCalculationResult.closingStock.getValue().toLong(),
                dayBeforeYesterdayCalculationResult.closingStock.getValue().toLong(),
                false,
                null,
            ),
            stockUpdates.first { it.date == dayBeforeYesterday },
        )

        assertEquals(
            StockUpdateItem(
                yesterday,
                yesterdayCalculationResult.closingStock.getValue().toLong(),
                yesterdayCalculationResultWithStockUpdate.closingStock.getValue().toLong(),
                false,
                stockUpdateValue.toLong(),
            ),
            stockUpdates.first { it.date == yesterday },
        )
    }

    @Test
    fun `fail if there is no valid stock update date range for given dc`() {
        val today = LocalDate.now()
        coEvery {
            stockUpdateService.getCurrentStockUpdate(dcCode, skuId)
        } returns emptyMap()

        assertFailsWith<IllegalArgumentException> {
            runBlocking {
                stockUpdateApiService.simulateStockUpdates(
                    dcCode,
                    skuId,
                    CalculatorMode.PRODUCTION,
                    mapOf(today to BigDecimal.TEN)
                )
            }
        }
    }

    @Test
    fun `stock update simulation is not allowed for dates out of valid date range`() {
        val today = LocalDate.now()
        val yesterday = today.minusDays(1)
        coEvery {
            stockUpdateService.getCurrentStockUpdate(dcCode, skuId)
        } returns mapOf(yesterday to null)

        assertFailsWith<IllegalArgumentException> {
            runBlocking {
                stockUpdateApiService.simulateStockUpdates(
                    dcCode,
                    skuId,
                    CalculatorMode.PRODUCTION,
                    mapOf(today to BigDecimal.TEN)
                )
            }
        }
    }

    @Test
    fun `stock update creation is not allowed for dates out of valid date range`() {
        val yesterday = LocalDate.now().minusDays(1)
        val beforeYesterday = LocalDate.now()
        val dateRange = DateRange.oneDay(yesterday)
        coEvery {
            stockUpdateService.getCurrentStockUpdateRange(dcCode)
        } returns dateRange
        coEvery {
            dcRepository.fetchDcConfigurations()
        } returns listOf(DistributionCenterConfiguration.default(dcCode))
        val skuSpecification = SkuSpecification.Companion.default.copy(uom = SkuUOM.entries.random())
        coEvery {
            skuInputDataRepository.fetchSkus(setOf(skuId))
        } returns mapOf(skuId to skuSpecification)

        assertFailsWith<IllegalArgumentException> {
            runBlocking {
                stockUpdateApiService.upsertStockUpdate(
                    StockUpdateRequest(
                        OTHER,
                        listOf(
                            StockUpdateRequestStockUpdatesInner(
                                beforeYesterday.minusDays(1),
                                111,
                                1,
                            ),
                            StockUpdateRequestStockUpdatesInner(
                                yesterday.minusDays(1),
                                5,
                                1,
                            ),
                        ),
                        "other",
                    ),
                    skuId,
                    dcCode,
                    mockk<LoggedInUserInfo>(relaxed = true),
                )
            }
        }
    }

    @Test
    fun `stock update with 0 value are flagged as deleted`() {
        val yesterday = LocalDate.now().minusDays(1)
        val beforeYesterday = yesterday.minusDays(1)
        val dateRange = DateRange(beforeYesterday, yesterday)
        coEvery {
            stockUpdateService.getCurrentStockUpdateRange(dcCode)
        } returns dateRange
        val distributionCenterConfiguration = DistributionCenterConfiguration.default(dcCode)

        val expectedStockUpdates = listOf(mockk<StockUpdate>())
        coEvery {
            dcRepository.fetchDcConfigurations()
        } returns listOf(distributionCenterConfiguration)
        val skuSpecification = SkuSpecification.Companion.default.copy(uom = SkuUOM.entries.random())
        coEvery {
            skuInputDataRepository.fetchSkus(setOf(skuId))
        } returns mapOf(skuId to skuSpecification)
        coEvery {
            stockUpdateApiRepository.upsertStockUpdate(any())
        } returns expectedStockUpdates

        val stockUpdatesRequest = listOf(
            StockUpdateRequestStockUpdatesInner(
                beforeYesterday,
                0,
                1,
            ),
            StockUpdateRequestStockUpdatesInner(
                yesterday,
                5,
                1,
            ),
        )
        val updatedStockUpdates = runBlocking {
            stockUpdateApiService.upsertStockUpdate(
                StockUpdateRequest(
                    OTHER,
                    stockUpdatesRequest,
                    "other",
                ),
                skuId,
                dcCode,
                mockk<LoggedInUserInfo>(relaxed = true),
            )
        }
        assertEquals(expectedStockUpdates, updatedStockUpdates)

        coVerify {
            stockUpdateApiRepository.upsertStockUpdate(
                withArg { updates ->
                    updates.forEach { expectedUpdate ->

                        val requestUpdate = stockUpdatesRequest.first { it.date == expectedUpdate.date }

                        assertEquals(expectedUpdate.skuId, skuId)
                        assertEquals(expectedUpdate.dcCode, dcCode)
                        assertEquals(
                            expectedUpdate.week,
                            DcWeek(expectedUpdate.date, distributionCenterConfiguration.productionStart).toString(),
                        )
                        assertEquals(
                            expectedUpdate.quantity.getValue(),
                            requestUpdate.stockUpdateQuantity.toBigDecimal(),
                        )
                        assertEquals(expectedUpdate.quantity.unitOfMeasure, skuSpecification.uom)
                        assertEquals(expectedUpdate.deleted, requestUpdate.stockUpdateQuantity == 0L)
                        assertEquals(expectedUpdate.version, requestUpdate.version)
                    }
                },
            )
        }
    }

    private fun buildStockUpdate(quantity: BigDecimal, reason: String): StockUpdate =
        StockUpdate(
            skuId, dcCode, LocalDate.now(), "week",
            SkuQuantity.fromBigDecimal(quantity, UOM_UNIT),
            reason, null, "authorName", "authorEmail", 1, LocalDateTime.now(UTC), false,
        )

    private fun createDayCalculationResult(
        date: LocalDate,

        closingStock: Long,
    ) = DayCalculationResult.default().copy(
        cskuId = skuId,
        date = date,
        dcCode = dcCode,
        closingStock = SkuQuantity.fromLong(closingStock),
    )
}
