package com.hellofresh.cif.api.calculation

import com.hellofresh.cif.api.calculation.CalculationServiceMapper.toDailyCalculation
import com.hellofresh.cif.api.calculation.db.CalculationRecord
import com.hellofresh.cif.models.SkuQuantity
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.mockk
import java.math.BigDecimal.ONE
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking

class MultiDcCalculationServiceWeeklyViewsTest : AbstractCalculationServiceViewsTest() {
    private val service =
        CalculationsService(
            SimpleMeterRegistry(),
            dcConfigService,
            usableInventoryEvaluatorMock,
            calculationsPendingStockUpdateServiceMock,
            prodCalculationRepositoryMock,
            mockk(),
            statsigFeatureFlagClient,
        )

    private val dcCodes = listOf("BV", "GR")
    private val dcWeek = "2022-W11"
    private val today = LocalDate.now()

    @Test
    fun `should aggregate one weekly calculation per sku when requesting weekly calculations for two DCs having the same sku`() {
        val sku = Sku(
            skuId = UUID.randomUUID(),
            skuCode = "ABC-00-00000-0",
            skuCategories = "ABC",
            skuName = "aSkuName",
        )

        val dayCalculationsForSku = 7
        val dailyCalculationsPerDc = dcCodes.associateWith { dcCode ->
            (0 until dayCalculationsForSku).map {
                givenCalculation(dcCode, sku, dcWeek, today.plusDays(it.toLong()))
            }
        }

        val calculationRequestOneSkuPerPage = givenRequest(dcWeek, *dcCodes.toTypedArray())
        mockRepository(calculationRequestOneSkuPerPage, dailyCalculationsPerDc.values.flatten())

        // when
        val result = runBlocking { service.getWeeklyCalculations(calculationRequestOneSkuPerPage) }

        // then
        val calculationPage = result.calculationPage
        val givenWeeklyViews = computeGivenWeeklyViews(dailyCalculationsPerDc)

        assertEquals(givenWeeklyViews, calculationPage)
    }

    @Test
    fun `should aggregate one weekly calculation per sku when requesting weekly calculations for multiple DCs having different skus`() {
        val dcsWithSkus = dcCodes.associateWith {
            Sku(
                skuId = UUID.randomUUID(),
                skuCode = "ABC-00-00000-0",
                skuCategories = "ABC",
                skuName = "aSkuName",
            )
        }
        val dayCalculationsForSku = 7
        val givenDailyCalculations = dcsWithSkus.mapValues { (dcCode, sku) ->
            (0 until dayCalculationsForSku).map {
                givenCalculation(dcCode, sku, dcWeek, today.plusDays(it.toLong()))
            }
        }
        val calculationRequestOneSkuPerPage = givenRequest(dcWeek, *dcCodes.toTypedArray())
        mockRepository(calculationRequestOneSkuPerPage, givenDailyCalculations.values.flatten())
        val expectedWeeklyViews = computeGivenWeeklyViews(givenDailyCalculations)

        // when
        val result = runBlocking {
            service.getWeeklyCalculations(calculationRequestOneSkuPerPage).calculationPage
        }
        assertEquals(expectedWeeklyViews, result)
    }

    private fun computeGivenWeeklyViews(calculationsPerDc: Map<String, List<CalculationRecord>>): List<WeeklyView> {
        val dailyCalculationsPerDc =
            calculationsPerDc.mapValues { (_, values) ->
                values.map {
                    toDailyCalculation(it, usableInventoryEvaluatorMock)
                }
            }
        val givenDailyCalculations = dailyCalculationsPerDc.values.flatten()
        val givenWeeklyViews = givenDailyCalculations
            .groupBy { Pair(it.sku, it.calculation.dcCode) }
            .map { weeklyView(dcWeek, it.key.first, it.value) }
            .map { view ->
                val dailyViews = dailyCalculationsPerDc[view.calculation.dcCode]
                // opening stock is the opening stock of the first day in the weeklyView
                val usableStock = dailyViews?.first()?.calculation?.usableStock ?: ONE.negate()
                // closing stock is the closing stock of the last day in the weeklyView
                val closingStock = dailyViews?.last()?.calculation?.closingStock ?: ONE.negate()
                val consumption = dailyViews?.first()?.calculation?.consumption ?: ONE.negate()
                val safetyStock = dailyViews?.first()?.calculation?.safetyStock ?: ONE.negate()
                val netNeedsCalculation = SkuQuantity.fromBigDecimal(usableStock - (consumption - safetyStock))
                view.copy(
                    calculation = view.calculation.copy(
                        usableStock = usableStock,
                        closingStock = closingStock,
                        storageStock = ONE.negate(),
                        netNeeds = SkuQuantity.max(SkuQuantity.ZERO, netNeedsCalculation).getValue(),
                    ),
                )
            }
        return givenWeeklyViews
    }
}
