package com.hellofresh.cif.api.supplyQuantityRecommendation

import com.hellofresh.cif.api.calculation.fixtures.default
import com.hellofresh.cif.api.supplyQuantityRecommendation.model.SQRState
import com.hellofresh.cif.api.supplyQuantityRecommendation.model.StockUpdate
import com.hellofresh.cif.api.supplyQuantityRecommendation.repository.StockUpdatesReadRepository
import com.hellofresh.cif.api.supplyQuantityRecommendation.repository.SupplyQuantityRecommendation
import com.hellofresh.cif.api.supplyQuantityRecommendation.repository.SupplyQuantityRecommendationRepository
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepository
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity.Companion.fromLong
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import com.hellofresh.cif.safetystock.SafetyStock
import com.hellofresh.cif.safetystock.SafetyStockConfiguration
import com.hellofresh.cif.safetystock.SafetyStockConfigurations
import com.hellofresh.cif.safetystock.model.SkuRiskRating
import com.hellofresh.cif.safetystock.model.SkuRiskRating.MEDIUM
import com.hellofresh.cif.safetystock.repository.SafetyStockConfigurationRepository
import com.hellofresh.cif.safetystock.repository.SafetyStockRepository
import com.hellofresh.cif.sqr.SQRConfigurations
import com.hellofresh.cif.sqr.repository.SupplyQuantityRecommendationConfigRepository
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.mockk
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZoneOffset.UTC
import java.time.temporal.TemporalAdjusters
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class SupplyQuantityRecommendationServiceTest {
    private val supplyQuantityRecommendationRepository = mockk<SupplyQuantityRecommendationRepository>(relaxed = true)
    private val stockUpdatesReadRepository = mockk<StockUpdatesReadRepository>(relaxed = true)
    private val supplyQuantityRecommendationConfigRepository =
        mockk<SupplyQuantityRecommendationConfigRepository>(relaxed = true)
    private val safetyStockRepository =
        mockk<SafetyStockRepository>(relaxed = true)
    private val safetyStockConfigurationRepository =
        mockk<SafetyStockConfigurationRepository>(relaxed = true)
    private val dcRepository = mockk<DcRepository>(relaxed = true)
    private val supplyQuantityRecommendationService = SupplyQuantityRecommendationService(
        supplyQuantityRecommendationRepository,
        stockUpdatesReadRepository,
        supplyQuantityRecommendationConfigRepository,
        safetyStockRepository,
        safetyStockConfigurationRepository,
        DcConfigService(SimpleMeterRegistry(), dcRepository),
    )
    private val dcCode = "VE"
    private val week = "2024-W21"
    private val skuId = UUID.randomUUID()
    private val distributionCenterConfiguration = DistributionCenterConfiguration.default(dcCode)

    @BeforeTest
    fun before() {
        coEvery {
            dcRepository.fetchDcConfigurations()
        } returns listOf(distributionCenterConfiguration)

        coEvery {
            supplyQuantityRecommendationConfigRepository.fetchSupplyQuantityRecommendationConfigurations(
                week,
                distributionCenterConfiguration,
            )
        } returns SQRConfigurations(emptyList())
    }

    @Test
    fun `should get accumulated stock updates for future weeks`() {
        val week = ProductionWeek(LocalDate.now(UTC).plusWeeks(1), distributionCenterConfiguration.productionStart)
        val weekString = week.dcWeek.toString()
        val lastProductionDay = week.dcWeek.getLastDateInDcWeek(
            distributionCenterConfiguration.productionStart,
            distributionCenterConfiguration.zoneId,
        )
        coEvery {
            supplyQuantityRecommendationRepository.fetchSupplyQuantityRecommendations(dcCode, weekString)
        } returns listOf(SupplyQuantityRecommendation.default(skuId, dcCode, weekString))

        coEvery {
            stockUpdatesReadRepository.fetchStockUpdates(
                dcCode,
                DateRange(
                    distributionCenterConfiguration.getLatestCleardown(),
                    lastProductionDay,
                ),
            )
        } returns listOf(
            StockUpdate(
                skuId = skuId, dcCode = dcCode, date = distributionCenterConfiguration.getLatestCleardown(),
                week = DcWeek(distributionCenterConfiguration.getLatestCleardown(), distributionCenterConfiguration.productionStart).toString(),
                quantity = fromLong(2000, UOM_UNIT),
            ),
            StockUpdate(
                skuId = skuId, dcCode = dcCode, date = lastProductionDay, week = weekString, quantity = fromLong(1000, UOM_UNIT),
            ),
        )
        runBlocking {
            val result = supplyQuantityRecommendationService.getSupplyQuantityRecommendations(dcCode, weekString)
            assertEquals(BigDecimal(3000), result.skus.first().stockUpdates)
        }
    }

    @Test
    fun `should get stock updates for old weeks`() {
        val week =
            ProductionWeek(
                distributionCenterConfiguration.getLatestCleardown().minusDays(1),
                distributionCenterConfiguration.cleardown,
            )
        val weekString = week.dcWeek.toString()
        val startProductionDay = week.dcWeek.getStartDateInDcWeek(
            distributionCenterConfiguration.productionStart,
            distributionCenterConfiguration.zoneId,
        )
        val lastProductionDay = week.dcWeek.getLastDateInDcWeek(
            distributionCenterConfiguration.productionStart,
            distributionCenterConfiguration.zoneId,
        )

        coEvery {
            supplyQuantityRecommendationRepository.fetchSupplyQuantityRecommendations(dcCode, weekString)
        } returns listOf(SupplyQuantityRecommendation.default(skuId, dcCode, weekString))

        coEvery {
            stockUpdatesReadRepository.fetchStockUpdates(
                dcCode,
                DateRange(
                    startProductionDay.with(TemporalAdjusters.previousOrSame(distributionCenterConfiguration.cleardown)),
                    lastProductionDay.with(TemporalAdjusters.previousOrSame(distributionCenterConfiguration.cleardown)),
                ),
            )
        } returns listOf(
            StockUpdate(
                skuId = skuId, dcCode = dcCode, date = startProductionDay,
                week = weekString,
                quantity = fromLong(1000, UOM_UNIT),
            ),
            StockUpdate(
                skuId = skuId, dcCode = dcCode, date = lastProductionDay, week = weekString, quantity = fromLong(1000, UOM_UNIT),
            ),
        )
        runBlocking {
            val result = supplyQuantityRecommendationService.getSupplyQuantityRecommendations(dcCode, weekString)
            assertEquals(2000.toBigDecimal(), result.skus.first().stockUpdates)
        }
    }

    @Test
    fun `should get stock updates for week for non cleardown dcs`() {
        val dc = DistributionCenterConfiguration.Companion.default(dcCode).copy(hasCleardown = false)
        val week =
            ProductionWeek(
                dc.getProductionStartDate(LocalDate.now(dc.zoneId)),
                dc.productionStart,
            )
        val weekString = week.dcWeek.toString()
        val startProductionDay = week.dcWeek.getStartDateInDcWeek(
            distributionCenterConfiguration.productionStart,
            distributionCenterConfiguration.zoneId,
        )
        val lastProductionDay = week.dcWeek.getLastDateInDcWeek(
            distributionCenterConfiguration.productionStart,
            distributionCenterConfiguration.zoneId,
        )
        coEvery {
            dcRepository.fetchDcConfigurations()
        } returns listOf(dc)

        coEvery {
            supplyQuantityRecommendationRepository.fetchSupplyQuantityRecommendations(dcCode, weekString)
        } returns listOf(SupplyQuantityRecommendation.default(skuId, dcCode, weekString))

        coEvery {
            stockUpdatesReadRepository.fetchStockUpdates(
                dcCode,
                DateRange(
                    startProductionDay,
                    lastProductionDay,
                ),
            )
        } returns listOf(
            StockUpdate(
                skuId = skuId, dcCode = dcCode, date = startProductionDay,
                week = weekString,
                quantity = fromLong(1000, UOM_UNIT),
            ),
            StockUpdate(
                skuId = skuId, dcCode = dcCode, date = lastProductionDay, week = weekString, quantity = fromLong(1000, UOM_UNIT),
            ),
        )
        runBlocking {
            val result = supplyQuantityRecommendationService.getSupplyQuantityRecommendations(dcCode, weekString)
            assertEquals(BigDecimal(2000), result.skus.first().stockUpdates)
        }
    }

    @Test
    fun `should not update the stock updates if the key (dcCode, week, skuId) are not matching`() {
        val week =
            ProductionWeek(
                distributionCenterConfiguration.getLatestCleardown(),
                distributionCenterConfiguration.cleardown,
            )
        val weekString = week.dcWeek.toString()
        val lastProductionDay = week.dcWeek.getLastDateInDcWeek(
            distributionCenterConfiguration.productionStart,
            distributionCenterConfiguration.zoneId,
        )

        coEvery {
            supplyQuantityRecommendationRepository.fetchSupplyQuantityRecommendations(dcCode, weekString)
        } returns listOf(SupplyQuantityRecommendation.default(skuId, dcCode, weekString))

        coEvery {
            stockUpdatesReadRepository.fetchStockUpdates(
                dcCode,
                DateRange(
                    distributionCenterConfiguration.getLatestCleardown(),
                    lastProductionDay,
                ),
            )
        } returns listOf(
            StockUpdate(
                skuId = UUID.randomUUID(), dcCode = dcCode, date = LocalDate.now(UTC), week = weekString, quantity = fromLong(2000, UOM_UNIT),
            ),
        )
        runBlocking {
            val result = supplyQuantityRecommendationService.getSupplyQuantityRecommendations(dcCode, weekString)
            assertNull(result.skus.first().stockUpdates)
        }
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "2, 2, 22, 22, PROCESSED",
            "2, 1, 22, 22, PENDING",
            "2, 2, 11, 22, PENDING",
            "2, 2, 22, null, PENDING",
            "2, 2, null, 22, PENDING",
            "2, 2, null, null, PROCESSED",
        ],
        nullValues = ["null"],
    )
    fun `should return pending state when safety stock is not processed`(
        sourceRiskMultiplier: BigDecimal,
        calculatedRiskMultiplier: BigDecimal,
        calculatedSafetyStock: Long?,
        sqrSafetyStock: BigDecimal?,
        state: SQRState
    ) {
        coEvery {
            supplyQuantityRecommendationRepository.fetchSupplyQuantityRecommendations(dcCode, week)
        } returns listOf(SupplyQuantityRecommendation.default(skuId, dcCode, week).copy(safetyStock = sqrSafetyStock))

        coEvery {
            safetyStockConfigurationRepository.fetchSafetyStockConfigurations(
                week,
                distributionCenterConfiguration,
            )
        } returns SafetyStockConfigurations(
            listOf(
                SafetyStockConfiguration(
                    dcCode, skuId,
                    ProductionWeek(
                        week,
                        distributionCenterConfiguration.productionStart,
                        distributionCenterConfiguration.zoneId,
                    ),
                    sourceRiskMultiplier,
                    MEDIUM,
                ),
            ),
        )

        coEvery {
            safetyStockRepository.fetchSafetyStock(
                week,
                distributionCenterConfiguration.dcCode,
            )
        } returns (
            calculatedSafetyStock?.let {
                listOf(
                    SafetyStock(
                        dcCode, skuId, week,
                        calculatedSafetyStock,
                        calculatedRiskMultiplier,
                        MEDIUM,
                        strategy = "default",
                    ),
                )
            } ?: emptyList()
            )

        runBlocking {
            val result = supplyQuantityRecommendationService.getSupplyQuantityRecommendations(dcCode, week)
            assertEquals(sqrSafetyStock, result.skus.first().safetyStock)
            assertEquals(sourceRiskMultiplier, result.skus.first().safetyStockRiskMultiplier)
            assertEquals(state, result.skus.first().state)
        }
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "MEDIUM, MEDIUM, PROCESSED",
            "MEDIUM, LOW, PENDING",
            "LOW, LOW, PROCESSED",
            "CRITICAL, HIGH, PENDING",
        ],
    )
    fun `should return pending state when safety stock sku risk rating is not processed`(
        sourceSkuRiskRating: SkuRiskRating,
        calculatedSkuRiskRating: SkuRiskRating,
        state: SQRState
    ) {
        val supplyQuantityRecommendation = SupplyQuantityRecommendation.default(skuId, dcCode, week)
        coEvery {
            supplyQuantityRecommendationRepository.fetchSupplyQuantityRecommendations(dcCode, week)
        } returns listOf(supplyQuantityRecommendation)

        coEvery {
            safetyStockConfigurationRepository.fetchSafetyStockConfigurations(
                week,
                distributionCenterConfiguration,
            )
        } returns SafetyStockConfigurations(
            listOf(
                SafetyStockConfiguration(
                    dcCode, skuId,
                    ProductionWeek(
                        week,
                        distributionCenterConfiguration.productionStart,
                        distributionCenterConfiguration.zoneId,
                    ),
                    BigDecimal.ONE,
                    sourceSkuRiskRating,
                ),
            ),
        )

        coEvery {
            safetyStockRepository.fetchSafetyStock(
                week,
                distributionCenterConfiguration.dcCode,
            )
        } returns listOf(
            SafetyStock(
                dcCode, skuId, week,
                supplyQuantityRecommendation.safetyStock!!.toLong(),
                BigDecimal.ONE, calculatedSkuRiskRating,
                strategy = "default",
            ),
        )

        runBlocking {
            val result = supplyQuantityRecommendationService.getSupplyQuantityRecommendations(dcCode, week)
            assertEquals(sourceSkuRiskRating, result.skus.first().skuRiskRating)
            assertEquals(state, result.skus.first().state)
        }
    }
}
