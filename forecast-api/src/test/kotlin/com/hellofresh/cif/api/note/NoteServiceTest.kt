package com.hellofresh.cif.api.note

import com.hellofresh.cif.distributionCenter.models.DcWeek
import io.mockk.coEvery
import io.mockk.mockk
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

internal class NoteServiceTest {
    private val noteRepository = mockk<NoteRepository>()
    private val noteService = NoteService(noteRepository)

    @Test
    fun `throws note not found exception while updating note`() {
        coEvery {
            noteRepository.updateNote(any(), any(), any(), any(), any())
        } throws IllegalArgumentException("Expected to update 1 record but found instead")

        val exception = assertThrows<NoteNotFoundException> {
            runBlocking {
                noteService.updateNote(updateNoteRequest())
            }
        }
        assertTrue(exception.message!!.contains("Expected to update 1 record but found instead"))
    }

    @Test
    fun `returns the filtered notes`() {
        val dcCodes = setOf("VE", "TO")
        val dcWeeks = setOf(DcWeek("2023-W12"))
        val note = createNote()

        val findNotesRequest = FindNotesRequest(dcCodes, dcWeeks, setOf(note.id), emptySet(), emptySet(), emptySet())

        coEvery {
            noteRepository.getNotes(findNotesRequest)
        } returns listOf(note)

        val result = runBlocking {
            noteService.getNotes(findNotesRequest)
        }
        assertEquals(1, result[note.skuId]?.size)
        assertFalse(result[note.skuId]?.get(0)?.isDeleted!!)
        assertFalse(result[note.skuId]?.get(0)?.isEdited!!)
    }

    private fun createNote() =
        Note(
            id = UUID.randomUUID(),
            skuId = UUID.randomUUID(),
            authorEmail = UUID.randomUUID().toString(),
            authorName = UUID.randomUUID().toString(),
            dcCodes = setOf("DC1", "DC2"),
            weeks = setOf(
                DcWeek("2023-W23"),
                DcWeek("2023-W24"),
            ),
            text = UUID.randomUUID().toString(),
            createdAt = OffsetDateTime.now(ZoneOffset.UTC),
            atRisk = listOf(AtRisk("DC1", DcWeek("2023-W24"))),
            0,
            false,
            false,
        )

    private fun updateNoteRequest() =
        UpdateNoteRequest(
            noteId = UUID.randomUUID(),
            authorEmail = "mockAuthorEmail",
            authorName = "mockAuthorName",
            dcWeeks = setOf(DcWeek("2023-W50")),
            text = UUID.randomUUID().toString(),
        )
}
