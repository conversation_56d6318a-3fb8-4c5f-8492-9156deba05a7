package com.hellofresh.cif.api.calculation.experiment

import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.cif.api.calculation.generated.model.ErrorResponse
import com.hellofresh.cif.api.calculation.generated.model.UomEnum.LITRE
import com.hellofresh.cif.api.calculation.generated.model.WeeklyCalculationExperimentResponse
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.user.AuthUtils.addAuthHeader
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.distributionCenter.models.DcWeek
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.http.formUrlEncode
import io.ktor.server.testing.testApplication
import io.mockk.coEvery
import io.mockk.mockk
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertContentEquals
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import kotlin.time.Duration
import kotlinx.coroutines.runBlocking

internal class WeeklyCalculationsExperimentApiTest {
    private val dcCode = "VE"
    private val calculationExperimentService = mockk<CalculationExperimentService>(relaxed = true)
    private val week = "2022-W44"
    private val jwtSecret = "testSecret"
    private val jwksURI = "https://test.com"
    private val authorName = "testAuthor"
    private val authorEmail = "<EMAIL>"

    @Test
    fun `should be able to get weekly experimental calculations for the given input`() {
        val skuId = UUID.randomUUID()
        val expectedExperiment = BigDecimal(1000L)
        val paramWeek1 = "2022-W45"
        val paramWeek2 = "2022-W46"
        val weeklyCalculationExperimentResponse = WeeklyCalculationExperimentResponse(
            1, 1000, 2, 3,
            5, 4, 6, LITRE, week, 1000,
        )
        coEvery {
            calculationExperimentService.getWeeklyCalculationExperiment(
                WeeklyCalculationExperimentData(
                    dcCode, skuId, setOf(paramWeek1, paramWeek2), CalculatorMode.PRODUCTION,
                    mapOf(DcWeek(week) to expectedExperiment),
                ),
            )
        } returns listOf(weeklyCalculationExperimentResponse)

        runBlocking {
            post(
                WeeklyCalculationExperimentReqParams(
                    setOf(dcCode),
                    skuId.toString(),
                ),
                week,
                expectedExperiment.toLong(),
                setOf(paramWeek1, paramWeek2),
            )
                .apply {
                    assertEquals(HttpStatusCode.OK, status)
                    val weeklyCalculationExperimentResponses = objectMapper.readValue(
                        bodyAsText(),
                        Array<WeeklyCalculationExperimentResponse>::class.java,
                    )
                    assertContentEquals(
                        arrayOf(weeklyCalculationExperimentResponse),
                        weeklyCalculationExperimentResponses,
                    )
                    assertEquals(expectedExperiment.toInt(), weeklyCalculationExperimentResponses[0].experiment!!)
                }
        }
    }

    @Test
    fun `should return Bad Request while getting weekly experimental calculations with invalid skuId`() {
        val invalidSkuId = "invalid-sku-id-not-a-UUID"
        runBlocking {
            post(
                WeeklyCalculationExperimentReqParams(
                    setOf(dcCode),
                    invalidSkuId,
                ),
                week,
                100,
            )
                .apply {
                    assertEquals(HttpStatusCode.BadRequest, status)
                    assertEquals("""{"reason":"Invalid UUID string: $invalidSkuId"}""", bodyAsText())
                }
        }
    }

    @Test
    fun `should return server error while getting weekly experimental calculations with more than 1 DCs as input`() {
        val skuId = UUID.randomUUID().toString()
        runBlocking {
            post(
                WeeklyCalculationExperimentReqParams(
                    setOf(dcCode, "BX"),
                    skuId,
                ),
                week,
                100,
            )
                .apply {
                    assertTheErrorResponse("Multiple DCs are not supported")
                }
        }
    }

    private suspend fun HttpResponse.assertTheErrorResponse(expectedErrorMessage: String) {
        val errorResponse = objectMapper.readValue(bodyAsText(), ErrorResponse::class.java)
        assertEquals(HttpStatusCode.BadRequest, status)
        assertNotNull(errorResponse.reason)
        assertTrue(errorResponse.reason!!.contains(expectedErrorMessage))
    }

    @Test
    fun `should return bad request while getting weekly experimental calculations with zero DC as input`() {
        val skuId = UUID.randomUUID().toString()
        runBlocking {
            post(
                WeeklyCalculationExperimentReqParams(
                    emptySet(),
                    skuId,
                ),
                week,
                100,
            )
                .apply {
                    assertTheErrorResponse("dcCode is not specified")
                }
        }
    }

    private fun post(
        params: WeeklyCalculationExperimentReqParams,
        week: String,
        experiment: Long,
        weeks: Set<String> = emptySet()
    ): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                calculationExperimentModule(
                    calculationExperimentService,
                    timeOutInMillis,
                )()
            }
            val queryParams = Parameters.build {
                params.dcCodes.forEach { append("dcCode", it) }
                weeks.forEach { append("weeks", it) }
            }.formUrlEncode()
            response = client.post("/calculation/experiment/${params.skuId}/weeklyView?$queryParams") {
                setBody(prepareRequestBody(week, experiment))
                header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                addAuthHeader(authorEmail, authorName, jwtSecret)
            }
        }
        return response
    }

    private fun prepareRequestBody(week: String, experiment: Long) = """
        [
           {
              "week":"$week",
              "experiment":$experiment
           }
        ]
    """.trimIndent()

    companion object {
        val timeOutInMillis: Duration = Duration.parse("PT1S")
        val objectMapper: ObjectMapper = ObjectMapper().findAndRegisterModules()
    }
}

data class WeeklyCalculationExperimentReqParams(val dcCodes: Set<String>, val skuId: String)
