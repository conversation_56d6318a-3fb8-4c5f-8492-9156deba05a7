package com.hellofresh.cif.api.po

import com.hellofresh.cif.api.calculation.generated.model.PoStatus
import com.hellofresh.cif.api.po.PurchaseOrderResponseMapper.mapToPoDetailResponse
import com.hellofresh.cif.api.po.PurchaseOrderResponseMapper.mapToPoStatus
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.Asn
import com.hellofresh.cif.models.purchaseorder.DeliveryInfo
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderSku
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderStatus
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderStatus.APPROVED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderStatus.ASN_RECEIVED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderStatus.DELIVERED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderStatus.DELIVERY_OPEN
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderStatus.OVERDUE
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderStatus.PLANNED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderStatus.SENT
import com.hellofresh.cif.models.purchaseorder.Supplier
import com.hellofresh.cif.models.purchaseorder.TimeRange
import io.mockk.coEvery
import io.mockk.mockk
import java.time.ZoneOffset.UTC
import java.time.ZonedDateTime
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

class PurchaseOrderResponseMapperTest {

    private val skuId = UUID.randomUUID()
    private val skuIdOther = UUID.randomUUID()
    private val orderedQty = 2
    private val receivedQty1 = 1
    private val receivedQty2 = 1
    private val poNr = "2317VE144872"
    private val poRef = "${poNr}_O1"
    private val asnId = "ASN000"
    private val dcConfigService: DcConfigService = mockk()

    @Test
    fun `should map to a PO API response`() {
        // given
        val po = getPurchaseOrder()

        val distributionCenterConfiguration =
            DistributionCenterConfiguration.default(
                dcCode = po.dcCode
            ).copy(zoneId = po.expectedDeliveryTimeslot!!.startTime.zone)
        coEvery {
            dcConfigService.dcConfigurations[po.dcCode]
        } returns distributionCenterConfiguration
        // when
        val result = mapToPoDetailResponse(listOf(po), skuId, dcConfigService)

        // then
        assertNotNull(result.pos)
        assertEquals(1, result.pos.size)
        val poRet = result.pos.first()
        assertEquals(
            po.purchaseOrderSkus.first().deliveries.first().deliveryTime.toOffsetDateTime(),
            poRet.deliveredTimes?.first(),
        )
        assertEquals(po.expectedDeliveryTimeslot?.startTime?.toOffsetDateTime(), poRet.inboundStartTime)
        assertEquals(po.expectedDeliveryTimeslot?.endTime?.toOffsetDateTime(), poRet.inboundEndTime)
        assertEquals(
            DcWeek(
                po.expectedDeliveryTimeslot!!.expectedDeliveryDate,
                distributionCenterConfiguration.productionStart
            ).value,
            poRet.week
        )
        assertEquals(po.status(skuId).name, poRet.status?.name)
        assertEquals(po.supplier?.id, poRet.supplierId)
        assertEquals(po.supplier?.name, poRet.supplierName)
        assertEquals(orderedQty, poRet.ordered)
        assertEquals(receivedQty1 + receivedQty2, poRet.received)
        assertEquals(poRef, poRet.poRef)
        assertEquals(po.dcCode, poRet.dcCode)
        assertEquals(asnId, poRet.asns?.first()?.asnId)
        assertEquals(orderedQty, poRet.asns?.first()?.quantity?.toInt())
        assertEquals(po.purchaseOrderSkus.flatMap { d -> d.deliveries.map { it.id } }, poRet.deliveries?.map { it.id })
    }

    @Test
    fun `should map PO statuses`() {
        PurchaseOrderStatus.entries.forEach {
            val mappedPoStatus = mapToPoStatus(it)
            when (it) {
                PLANNED -> assertEquals(PoStatus.PLANNED, mappedPoStatus)
                SENT -> assertEquals(PoStatus.SENT, mappedPoStatus)
                APPROVED -> assertEquals(PoStatus.APPROVED, mappedPoStatus)
                ASN_RECEIVED -> assertEquals(PoStatus.ASN_RECEIVED, mappedPoStatus)
                DELIVERY_OPEN -> assertEquals(PoStatus.DELIVERY_OPEN, mappedPoStatus)
                DELIVERED -> assertEquals(PoStatus.DELIVERED, mappedPoStatus)
                OVERDUE -> assertEquals(PoStatus.OVERDUE, mappedPoStatus)
            }
        }
    }

    private fun getPurchaseOrder(deliveryDateTime: ZonedDateTime = ZonedDateTime.now(UTC).plusDays(1)) = PurchaseOrder(
        poNr,
        poRef,
        skuId,
        "VE",
        TimeRange(deliveryDateTime, deliveryDateTime.plusHours(1)),
        Supplier(UUID.randomUUID(), "S"),
        listOf(
            PurchaseOrderSku(
                skuId,
                SkuQuantity.fromLong(orderedQty.toLong()),
                listOf(
                    DeliveryInfo(
                        UUID.randomUUID().toString(),
                        deliveryDateTime,
                        DeliveryInfoStatus.CLOSED,
                        SkuQuantity.fromLong(receivedQty1.toLong()),
                    ),
                    DeliveryInfo(
                        UUID.randomUUID().toString(),
                        deliveryDateTime,
                        DeliveryInfoStatus.CLOSED,
                        SkuQuantity.fromLong(receivedQty2.toLong()),
                    ),
                ),
            ),
            PurchaseOrderSku(
                skuIdOther,
                SkuQuantity.fromLong(100),
                emptyList(),
            ),
        ),
        poStatus = com.hellofresh.cif.models.purchaseorder.PoStatus.APPROVED,
        asns = listOf(
            Asn(asnId, skuId, ZonedDateTime.now(UTC), SkuQuantity.fromLong(orderedQty.toLong())),
            Asn(asnId, skuIdOther, ZonedDateTime.now(UTC), SkuQuantity.fromLong(orderedQty.toLong())),
        ),
    )
}
