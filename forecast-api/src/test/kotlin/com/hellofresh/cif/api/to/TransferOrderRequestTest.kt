package com.hellofresh.cif.api.to

import com.hellofresh.cif.distributionCenter.models.DcWeek
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import org.junit.jupiter.api.assertThrows

class TransferOrderRequestTest {
    @Test
    fun `should fail if only one of fromDate and toDate is present`() {
        assertThrows<IllegalArgumentException> {
            TransferOrdersReq(UUID(0, 0), setOf("VE"), weeks = null, fromDate = LocalDate.now(), toDate = null)
        }
        assertThrows<IllegalArgumentException> {
            TransferOrdersReq(UUID(0, 0), setOf("VE"), weeks = null, fromDate = null, toDate = LocalDate.now())
        }
    }

    @Test
    fun `should fail if fromDate is after toDate`() {
        assertThrows<IllegalArgumentException> {
            TransferOrdersReq(
                UUID(0, 0),
                setOf("VE"),
                weeks = null,
                fromDate = LocalDate.now().plusDays(1),
                toDate = LocalDate.now(),
            )
        }
    }

    @Test
    fun `should fail if dc codes are empty`() {
        assertThrows<IllegalArgumentException> {
            TransferOrdersReq(
                skuId = UUID(0, 0),
                dcCodes = emptySet(),
                weeks = setOf(DcWeek.invoke(LocalDate.now(), MONDAY)),
                fromDate = null,
                toDate = null,
            )
        }
    }
}
