package com.hellofresh.cif.api.calculation.csvexport

import com.hellofresh.cif.api.calculation.Calculation
import com.hellofresh.cif.api.calculation.DailyView
import com.hellofresh.cif.api.calculation.ProductionDay
import com.hellofresh.cif.api.calculation.Sku
import com.hellofresh.cif.api.calculation.WeeklyView
import com.hellofresh.cif.api.calculation.fixtures.DEFAULT_TEST_STRATEGY
import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.Context.DC
import com.hellofresh.cif.featureflags.Context.MARKET
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.NetNeeds
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.models.purchaseorder.PoStatus.PLANNED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderSku
import com.hellofresh.cif.models.purchaseorder.TimeRange
import io.mockk.every
import io.mockk.mockk
import java.io.StringReader
import java.math.BigDecimal
import java.time.DayOfWeek.FRIDAY
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneOffset.UTC
import java.time.format.DateTimeFormatter
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.apache.commons.csv.CSVFormat

class MultiDcCalculationsCsvConverterTest {
    private val dcConfigService = mockk<DcConfigService>(relaxed = true)
    private val sut = CalculationsCsvConverter(
        dcConfigService,
        StatsigTestFeatureFlagClient(emptySet()),
        ConfigurationLoader.getSet("csv.brands.consumption.markets"),
    )

    @Test
    fun `should aggregate daily calculations for a sku in multiple DCs when converting to csv`() {
        // given
        val skuId = UUID.randomUUID()
        val dcCodes = listOf("BX", "VE", "VB", "SY")
        val calculationsMultiDc = dcCodes.map { dcCode ->
            (0..6).map { i ->
                newDailyView(skuId, LocalDate.now().plusDays(i.toLong()), dcCode, "PO-$dcCode-$i")
            }
        }.flatten()
        val givenCalcAggregations = calculationsMultiDc.groupBy { it.productionDay.day }

        every { dcConfigService.dcConfigurations } returns mapOf(
            "SY" to DistributionCenterConfiguration.default("BX").copy(market = "AU"),
        )
        // when
        val ret = runBlocking {
            sut.convertDailyToCsv(calculationsMultiDc, emptyList())
        }
        val rt = CSVFormat.DEFAULT.parse(StringReader(ret))
        val records = rt.records

        // then
        assertEquals(8, records.size)
        records.drop(1).forEachIndexed { index, csvRow ->
            val date = LocalDate.now().plusDays(index.toLong())
            val dailyCalcResponse = givenCalcAggregations[date]!!
            assertEquals(dailyCalcResponse.map { it.calculation.dcCode }.toSet(), csvRow[0].split(' ').toSet())
            assertEquals(dailyCalcResponse.first().sku.skuId.toString(), csvRow[1])
            assertEquals(dailyCalcResponse.first().sku.skuCode, csvRow[2])
            assertEquals(dailyCalcResponse.first().sku.skuName, csvRow[3])
            assertEquals(dailyCalcResponse.first().sku.skuCategories, csvRow[4])
            assertEquals(date.format(DateTimeFormatter.ISO_DATE), csvRow[6])
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.inbound }, csvRow[7].toBigDecimal())
            assertEquals(
                dailyCalcResponse.sumOf { v -> v.calculation.incomingPos },
                csvRow[8].toBigDecimal(),
            )
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.consumption }, csvRow[9].toBigDecimal())
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.closingStock }, csvRow[10].toBigDecimal())
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.usableStock }, csvRow[11].toBigDecimal())
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.unusableStock }, csvRow[12].toBigDecimal())
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.actualConsumption }, csvRow[13].toBigDecimal())
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.substituted!! }, csvRow[14].toLong())
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.fumigatedDemand!! }, csvRow[17].toLong())
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.regularDemand!! }, csvRow[18].toLong())
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.netNeeds }, csvRow[19].toBigDecimal())
        }
    }

    @Test
    fun `should aggregate weekly calculations for a sku in multiple DCs when converting to csv`() {
        // given
        val skuId = UUID.randomUUID()
        val dcCodes = listOf("BX", "VE", "VB")
        val calculationsMultiDc = dcCodes.map { dcCode ->
            (0..6).map { i ->
                newWeeklyView(
                    skuId,
                    DcWeek.invoke(LocalDate.now().plusWeeks(i.toLong()), FRIDAY),
                    dcCode,
                    setOf("PO-$dcCode-0$i"),
                )
            }
        }.flatten()
        val givenCalcAggregations = calculationsMultiDc.groupBy { it.week }

        // when
        val ret = runBlocking {
            sut.convertWeeklyToCsv(calculationsMultiDc, emptyList())
        }
        val rt = CSVFormat.DEFAULT.parse(StringReader(ret))
        val records = rt.records

        // then
        records.drop(1).forEachIndexed { index, csvRow ->
            val week = DcWeek.invoke(LocalDate.now().plusWeeks(index.toLong()), FRIDAY).value
            val dailyCalcResponse = givenCalcAggregations[week]!!
            assertEquals(dailyCalcResponse.map { it.calculation.dcCode }.toSet(), csvRow[0].split(' ').toSet())
            assertEquals(dailyCalcResponse.first().sku.skuId.toString(), csvRow[1])
            assertEquals(dailyCalcResponse.first().sku.skuCode, csvRow[2])
            assertEquals(dailyCalcResponse.first().sku.skuName, csvRow[3])
            assertEquals(dailyCalcResponse.first().sku.skuCategories, csvRow[4])
            assertEquals(week, csvRow[5])
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.inbound }, csvRow[7].toBigDecimal())
            assertEquals(
                dailyCalcResponse.sumOf { v -> v.calculation.incomingPos },
                csvRow[8].toBigDecimal(),
            )
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.consumption }, csvRow[9].toBigDecimal())
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.closingStock }, csvRow[10].toBigDecimal())
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.usableStock }, csvRow[11].toBigDecimal())
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.unusableStock }, csvRow[12].toBigDecimal())
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.actualConsumption }, csvRow[13].toBigDecimal())
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.substituted!! }, csvRow[14].toLong())
        }
    }

    @Test
    fun `should not aggregate daily calculations for different skus`() {
        // given
        val dcCodes = listOf("BX", "VE", "VB")
        val dailyCalculationsMultiDc = dcCodes.map { dcCode ->
            (0..6).map { i ->
                newDailyView(UUID.randomUUID(), LocalDate.now().plusDays(i.toLong()), dcCode, "PO1")
            }
        }.flatten()

        // when
        val ret = runBlocking {
            sut.convertDailyToCsv(dailyCalculationsMultiDc, emptyList())
        }
        val records = CSVFormat.DEFAULT.parse(StringReader(ret)).records

        // then
        assertEquals(dailyCalculationsMultiDc.size + 1, records.size)
    }

    @Test
    fun `should not aggregate weekly calculations for different skus`() {
        // given
        val dcCodes = listOf("BX", "VE", "VB")
        val weeklyCalculationsMultiDc = dcCodes.map { dcCode ->
            (0..6).map { i ->
                newWeeklyView(
                    UUID.randomUUID(),
                    DcWeek(LocalDate.now().plusDays(i.toLong()), FRIDAY),
                    dcCode,
                    emptySet(),
                )
            }
        }.flatten()

        // when
        val ret = runBlocking {
            sut.convertWeeklyToCsv(weeklyCalculationsMultiDc, emptyList())
        }
        val records = CSVFormat.DEFAULT.parse(StringReader(ret)).records

        // then
        assertEquals(weeklyCalculationsMultiDc.size + 1, records.size)
    }

    @Test
    fun `should return safety stock net needs csv format when at least one dc request is enable in flag`() {
        // given
        val skuId = UUID.randomUUID()
        val dcCodes = listOf("D1", "D2")
        val calculationsMultiDc = dcCodes.map { dcCode ->
            (0..6).map { i ->
                newDailyView(skuId, LocalDate.now().plusDays(i.toLong()), dcCode, "PO-$dcCode-$i")
            }
        }.flatten()
        val givenCalcAggregations = calculationsMultiDc.groupBy { it.productionDay.day }
        val enabledFlagMarket = "enabledDcMarket"

        // when
        every { dcConfigService.dcConfigurations } returns mapOf(
            "D1" to DistributionCenterConfiguration.default("D1").copy(market = "any"),
            "D2" to DistributionCenterConfiguration.default("D2").copy(market = enabledFlagMarket),
        )

        val calculationsCsvConverter = CalculationsCsvConverter(
            dcConfigService,
            StatsigTestFeatureFlagClient(
                setOf(NetNeeds(setOf(ContextData(DC, "D2"), ContextData(MARKET, enabledFlagMarket)))),
            ),
            ConfigurationLoader.getSet("csv.brands.consumption.markets"),
        )

        val ret = runBlocking {
            calculationsCsvConverter.convertDailyToCsv(calculationsMultiDc, emptyList())
        }
        val rt = CSVFormat.DEFAULT.parse(StringReader(ret))
        val records = rt.records

        // then
        assertEquals(8, records.size)
        records.drop(1).forEachIndexed { index, csvRow ->
            val date = LocalDate.now().plusDays(index.toLong())
            val dailyCalcResponse = givenCalcAggregations[date]!!
            assertEquals(dailyCalcResponse.map { it.calculation.dcCode }.toSet(), csvRow[0].split(' ').toSet())
            assertEquals(dailyCalcResponse.first().sku.skuId.toString(), csvRow[1])
            assertEquals(dailyCalcResponse.first().sku.skuCode, csvRow[2])
            assertEquals(dailyCalcResponse.first().sku.skuName, csvRow[3])
            assertEquals(dailyCalcResponse.first().sku.skuCategories, csvRow[4])
            assertEquals(date.format(DateTimeFormatter.ISO_DATE), csvRow[6])
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.inbound }, csvRow[7].toBigDecimal())
            assertEquals(
                dailyCalcResponse.sumOf { v -> v.calculation.incomingPos },
                csvRow[8].toBigDecimal(),
            )
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.consumption }, csvRow[9].toBigDecimal())
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.closingStock }, csvRow[10].toBigDecimal())
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.usableStock }, csvRow[11].toBigDecimal())
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.unusableStock }, csvRow[12].toBigDecimal())
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.actualConsumption }, csvRow[13].toBigDecimal())
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.safetyStock!! }, csvRow[15].toBigDecimal())
            assertEquals(dailyCalcResponse.sumOf { v -> v.calculation.netNeeds }, csvRow[16].toBigDecimal())
        }
    }

    @Test
    fun `should aggregate planned purchase orders for a sku when converting to csv`() {
        // given
        val skuId = UUID.randomUUID()
        val dcCode = "VB"
        every { dcConfigService.dcConfigurations } returns mapOf(dcCode to DistributionCenterConfiguration.default(dcCode))

        val dcWeek = DcWeek(LocalDate.now(), FRIDAY)
        val weeklyView = newWeeklyView(
            skuId,
            dcWeek,
            dcCode,
            emptySet(),
        )
        val pos = DateRange(
            dcWeek.getStartDateInDcWeek(FRIDAY, UTC),
            dcWeek.getStartDateInDcWeek(FRIDAY, UTC).plusDays(1)
        ).mapIndexed { i, date ->
            PurchaseOrder(
                "",
                "",
                null,
                dcCode,
                TimeRange(
                    date.atTime(LocalTime.NOON).atZone(UTC),
                    date.atTime(LocalTime.NOON).plusHours(1).atZone(UTC),
                ),
                null,
                listOf(
                    PurchaseOrderSku(
                        skuId,
                        SkuQuantity.fromLong(100L * i),
                        emptyList(),
                    ),
                ),
                poStatus = PLANNED,
            )
        }

        // when
        val ret = runBlocking {
            sut.convertWeeklyToCsv(listOf(weeklyView), pos)
        }

        val records = CSVFormat.DEFAULT.parse(StringReader(ret)).records

        // then
        assertEquals(
            pos.flatMap { it.purchaseOrderSkus }.sumOf { it.expectedQuantity?.getValue() ?: BigDecimal.ZERO },
            records[1][16].toBigDecimal(),
        )
    }

    private fun newDailyView(skuId: UUID, date: LocalDate, dcCode: String, poNumber: String) =
        DailyView(
            sku = Sku(
                skuId = skuId,
                skuCode = "skuCode",
                skuName = "",
                skuCategories = "",
            ),
            productionDay = ProductionDay(
                week = "2022-W01",
                day = date,
            ),
            calculation = Calculation(
                unusableStock = BigDecimal(1),
                usableStock = BigDecimal(2),
                incomingPos = BigDecimal(3),
                inbound = BigDecimal(4),
                consumption = BigDecimal(5),
                actualConsumption = BigDecimal(6),
                dailyNeed = BigDecimal(7),
                closingStock = BigDecimal(0),
                dcCode = dcCode,
                skuAtRisk = false,
                pos = setOf(poNumber),
                netNeeds = BigDecimal(0),
                safetyStock = BigDecimal(9),
                strategy = DEFAULT_TEST_STRATEGY,
                safetyStockNeeds = BigDecimal(10),
                substituted = 22L,
                uom = SkuUOM.UOM_UNIT,
                storageStock = null,
                stagingStock = null,
                poDueIn = null,
                regularDemand = 10,
                fumigatedDemand = 10,
            ),
        )

    private fun newWeeklyView(skuId: UUID, week: DcWeek, dcCode: String, poNumbers: Set<String>) =
        WeeklyView(
            sku = Sku(
                skuId = skuId,
                skuCode = "skuCode",
                skuName = "",
                skuCategories = "",
            ),
            week = week.value,
            calculation = Calculation(
                unusableStock = BigDecimal(1),
                usableStock = BigDecimal(2),
                incomingPos = BigDecimal(3),
                inbound = BigDecimal(4),
                consumption = BigDecimal(5),
                actualConsumption = BigDecimal(6),
                dailyNeed = BigDecimal(7),
                closingStock = BigDecimal(0),
                dcCode = dcCode,
                skuAtRisk = false,
                pos = poNumbers,
                substituted = 0,
                storageStock = null,
                stagingStock = null,
                poDueIn = null,
                safetyStock = null,
                strategy = DEFAULT_TEST_STRATEGY,
                safetyStockNeeds = null,
                netNeeds = BigDecimal.ZERO,
                uom = SkuUOM.UOM_UNIT,
                regularDemand = 10,
                fumigatedDemand = 10,
            ),
        )
}
