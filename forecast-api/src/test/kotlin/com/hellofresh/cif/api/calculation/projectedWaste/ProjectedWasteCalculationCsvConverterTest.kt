package com.hellofresh.cif.api.calculation.projectedWaste

import com.hellofresh.cif.api.calculation.generated.model.ProjectedWasteResponse
import com.hellofresh.cif.api.calculation.generated.model.ProjectedWastesResponse
import com.hellofresh.cif.api.calculation.generated.model.SkuResponse
import com.hellofresh.cif.api.calculation.projectedWaste.ProjectedWasteCalculationCsvConverter.Companion.getCsvHeaders
import io.ktor.http.ContentType.Text
import io.ktor.http.content.TextContent
import io.mockk.mockk
import java.io.StringReader
import java.nio.charset.Charset
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVRecord
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class ProjectedWasteCalculationCsvConverterTest {
    private val projectedWasteCalculationCsvConverter = ProjectedWasteCalculationCsvConverter()

    @ParameterizedTest
    @CsvSource("true, YES", "false, NO")
    fun `should convert ProjectedWastesCalculation to csv`(isUsable: Boolean, expectedIsUsable: String) {
        // given
        val projectedWastesResponse = ProjectedWastesResponse(
            projectedWasteResponses = listOf(
                ProjectedWasteResponse(
                    dcCode = "VE",
                    sku = SkuResponse("SKU1", UUID.randomUUID(), "Sample SKU"),
                    quantity = 100,
                    date = LocalDate.now(),
                    isUsable = isUsable,
                    unusableStockReason = "short shelf life",
                ),
            ),
        )
        // when
        val result = runBlocking {
            projectedWasteCalculationCsvConverter.serialize(
                Text.CSV,
                Charset.defaultCharset(),
                mockk(),
                projectedWastesResponse,
            )
        }
        assertEquals(Text.CSV, result.contentType)
        val rt = CSVFormat.DEFAULT.parse(StringReader((result as TextContent).text))
        val records = rt.records
        val defaultCsvHeader = getCsvHeaders()
        assertEquals(2, records.size)
        assertEquals(defaultCsvHeader.toList(), records[0].toList())
        val projectedWasteCalcValues = records[1]

        // then
        assertDefaultCsvValues(
            projectedWastesResponse.projectedWasteResponses!![0],
            projectedWasteCalcValues,
            expectedIsUsable
        )
        assertEquals(defaultCsvHeader.size, projectedWasteCalcValues.size())
    }

    private fun assertDefaultCsvValues(
        projectedWasteResponse: ProjectedWasteResponse,
        projectedWasteCalcValues: CSVRecord,
        expectedIsUsable: String
    ) {
        assertEquals(projectedWasteResponse.dcCode, projectedWasteCalcValues[0])
        assertEquals(projectedWasteResponse.sku.skuCode, projectedWasteCalcValues[1])
        assertEquals(projectedWasteResponse.sku.skuName, projectedWasteCalcValues[2])
        assertEquals(projectedWasteResponse.quantity.toString(), projectedWasteCalcValues[3])
        assertEquals(projectedWasteResponse.date.toString(), projectedWasteCalcValues[4])
        assertEquals(expectedIsUsable, projectedWasteCalcValues[5])
        assertEquals(projectedWasteResponse.unusableStockReason, projectedWasteCalcValues[6])
    }
}
