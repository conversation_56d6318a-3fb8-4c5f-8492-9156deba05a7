package com.hellofresh.cif.api.calculation.csvexport

import com.hellofresh.cif.api.calculation.BrandConsumption
import com.hellofresh.cif.api.calculation.Calculation
import com.hellofresh.cif.api.calculation.DailyView
import com.hellofresh.cif.api.calculation.ProductionDay
import com.hellofresh.cif.api.calculation.Sku
import com.hellofresh.cif.api.calculation.csvexport.CalculationsCsvConverter.Companion.getCsvHeaders
import com.hellofresh.cif.api.calculation.fixtures.DEFAULT_TEST_STRATEGY
import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.Context.DC
import com.hellofresh.cif.featureflags.Context.MARKET
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.NetNeeds
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.models.purchaseorder.PoStatus
import com.hellofresh.cif.models.purchaseorder.PoStatus.PLANNED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderSku
import com.hellofresh.cif.models.purchaseorder.TimeRange
import io.mockk.every
import io.mockk.mockk
import java.io.StringReader
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneOffset
import java.util.UUID
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVRecord
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class CalculationsCsvConverterTest {

    private val dcConfigService = mockk<DcConfigService>(relaxed = true)
    private val calculationsCsvConverter = CalculationsCsvConverter(
        dcConfigService,
        StatsigTestFeatureFlagClient(emptySet()),
        ConfigurationLoader.getSet("csv.brands.consumption.markets"),
    )

    private val dailyView = DailyView(
        sku = Sku(
            skuId = UUID.randomUUID(),
            skuCode = "skuCode",
            skuName = "skuName",
            skuCategories = "cat1,cat2",
        ),
        productionDay = ProductionDay(
            week = "2022-W01",
            day = LocalDate.parse("2022-01-02"),
        ),
        calculation = Calculation(
            dcCode = "VE",
            unusableStock = BigDecimal(1), usableStock = BigDecimal(2),
            storageStock = null, stagingStock = null,
            incomingPos = BigDecimal(3),
            inbound = BigDecimal(4),
            consumption = BigDecimal(5), actualConsumption = BigDecimal(0),
            dailyNeed = BigDecimal(6),
            closingStock = BigDecimal(0),
            skuAtRisk = false,
            pos = emptySet(),
            safetyStock = BigDecimal(1500),
            strategy = DEFAULT_TEST_STRATEGY,
            netNeeds = BigDecimal(1000),
            safetyStockNeeds = BigDecimal(1000),
            brandConsumptions =
            listOf(BrandConsumption("HF", 10L), BrandConsumption("EP", 20L)),
            poDueIn = null,
            prekitting = 10L,
            substituted = 22L,
            uom = SkuUOM.UOM_UNIT,
        ),
    )

    @Test
    fun `should convert DailyCalculationResponse to csv`() {
        // when
        val ret = runBlocking {
            calculationsCsvConverter.convertDailyToCsv(listOf(dailyView), emptyList())
        }

        val rt = CSVFormat.DEFAULT.parse(StringReader(ret))
        val records = rt.records
        val defaultCsvHeader = getCsvHeaders(false, false)
        kotlin.test.assertEquals(2, records.size)
        kotlin.test.assertEquals(defaultCsvHeader.toList(), records[0].toList())
        val calcValues = records[1]

        // then
        assertDefaultCsvValues(dailyView, calcValues)
        kotlin.test.assertEquals(defaultCsvHeader.size, calcValues.size())
    }

    @Test
    fun `getCsvHeaders returns correct headers and values when isBrandConsumption is true and isSafetyStock is false`() {
        // Given
        every {
            dcConfigService.dcConfigurations
        } returns mapOf("VE" to DistributionCenterConfiguration.Companion.default("VE").copy(market = "Market1"))

        val headers = getCsvHeaders(false, true)

        val csvConverter = CalculationsCsvConverter(
            dcConfigService,
            StatsigTestFeatureFlagClient(
                emptySet(),
            ),
            setOf("Market1"),
        )

        // when
        val ret = runBlocking {
            csvConverter.convertDailyToCsv(listOf(dailyView), emptyList())
        }

        val rt = CSVFormat.DEFAULT.parse(StringReader(ret))
        val records = rt.records
        val calcValues = records[1]

        // then
        kotlin.test.assertEquals(headers.toList(), records[0].toList())
        kotlin.test.assertTrue(records[0].contains("HelloFresh"), "The record does not contain 'HelloFresh'")
        kotlin.test.assertTrue(records[0].contains("EveryPlate"), "The record does not contain 'EveryPlate'")

        assertDefaultCsvValues(dailyView, calcValues)
        kotlin.test.assertEquals(headers.size, calcValues.size())

        kotlin.test.assertEquals(calcValues[15].toLong(), 10L) // HelloFresh consumption
        kotlin.test.assertEquals(calcValues[16].toLong(), 20L) // EveryPlate consumption
    }

    @Test
    fun `getCsvHeaders returns correct headers and values when isAnzMarket and isSafetyStockMarket are true`() {
        // Given
        every {
            dcConfigService.dcConfigurations
        } returns mapOf("VE" to DistributionCenterConfiguration.Companion.default("VE").copy(market = "Market1"))

        val headers = getCsvHeaders(true, true)

        val csvConverter = CalculationsCsvConverter(
            dcConfigService,
            StatsigTestFeatureFlagClient(
                setOf(
                    NetNeeds(setOf(ContextData(DC, "VE"), ContextData(MARKET, "Market1"))),
                ),
            ),
            setOf("Market1"),
        )

        // when
        val ret = runBlocking {
            csvConverter.convertDailyToCsv(listOf(dailyView), emptyList())
        }

        val rt = CSVFormat.DEFAULT.parse(StringReader(ret))
        val records = rt.records
        val calcValues = records[1]

        // then
        kotlin.test.assertEquals(headers.toList(), records[0].toList())
        kotlin.test.assertTrue(records[0].contains("HelloFresh"), "The record does not contain 'HelloFresh'")
        kotlin.test.assertTrue(records[0].contains("EveryPlate"), "The record does not contain 'EveryPlate'")
        kotlin.test.assertTrue(records[0].contains("Safety Stock"), "The record does not contain 'Safety Stock'")
        kotlin.test.assertTrue(
            records[0].contains("Supply Quantity Recommendation"),
            "The record does not contain 'Supply Quantity Recommendation'",
        )

        assertDefaultCsvValues(dailyView, calcValues)
        kotlin.test.assertEquals(headers.size, calcValues.size())

        kotlin.test.assertEquals(calcValues[15].toLong(), 1500L) // Safety Stock
        kotlin.test.assertEquals(calcValues[16].toLong(), 10L) // HelloFresh consumption
        kotlin.test.assertEquals(calcValues[17].toLong(), 20L) // EveryPlate consumption
        kotlin.test.assertEquals(calcValues[18].toLong(), 1000L) // Net Needs
    }

    @ParameterizedTest
    @CsvSource(
        "Market1, true",
        "Market2, false",
    )
    fun `should convert DailyCalculationResponse to csv with safety stock and net need markets when flag is enabled`(
        market: String,
        flagEnabled: Boolean
    ) {
        // given
        every {
            dcConfigService.dcConfigurations
        } returns mapOf("VE" to DistributionCenterConfiguration.Companion.default("VE").copy(market = market))

        val csvConverter = CalculationsCsvConverter(
            dcConfigService,
            StatsigTestFeatureFlagClient(
                if (flagEnabled) {
                    setOf(
                        NetNeeds(setOf(ContextData(DC, "VE"), ContextData(MARKET, market))),
                    )
                } else {
                    emptySet()
                },
            ),
            ConfigurationLoader.getSet("csv.brands.consumption.markets"),
        )

        // when
        val ret = runBlocking {
            csvConverter.convertDailyToCsv(listOf(dailyView), emptyList())
        }
        // then
        val rt = CSVFormat.DEFAULT.parse(StringReader(ret))
        val records = rt.records
        kotlin.test.assertEquals(2, records.size)
        val calcValues = records[1]
        if (flagEnabled) {
            val safetyStockCsvHeader = getCsvHeaders(true, false)
            kotlin.test.assertEquals(safetyStockCsvHeader.toList(), records[0].toList())
            kotlin.test.assertEquals(safetyStockCsvHeader.size, calcValues.size())

            assertDefaultCsvValues(dailyView, calcValues)
            kotlin.test.assertEquals(dailyView.calculation.substituted, calcValues[14].toLong())
            kotlin.test.assertEquals(dailyView.calculation.safetyStock, calcValues[15].toBigDecimal())
            kotlin.test.assertEquals(dailyView.calculation.netNeeds, calcValues[16].toBigDecimal())
        } else {
            val defaultCsvHeader = getCsvHeaders(false, false)
            kotlin.test.assertEquals(defaultCsvHeader.toList(), records[0].toList())
            kotlin.test.assertEquals(defaultCsvHeader.size, calcValues.size())
            assertDefaultCsvValues(dailyView, calcValues)
        }
    }

    @Test
    fun `should include Planned Purchase Orders in csv`() {
        // given
        every {
            dcConfigService.dcConfigurations
        } returns mapOf("VE" to DistributionCenterConfiguration.Companion.default("VE"))

        val dailyView2 = dailyView.copy(sku = dailyView.sku.copy(skuId = UUID.randomUUID()))

        val po1 = PurchaseOrder(
            "",
            "",
            null,
            "VE",
            TimeRange(
                dailyView.productionDay.day.atTime(LocalTime.NOON).atZone(ZoneOffset.UTC),
                dailyView.productionDay.day.atTime(LocalTime.NOON).plusHours(1).atZone(ZoneOffset.UTC),
            ),
            null,
            listOf(
                PurchaseOrderSku(
                    dailyView.sku.skuId,
                    SkuQuantity.fromLong(100),
                    emptyList(),
                ),
                PurchaseOrderSku(
                    dailyView2.sku.skuId,
                    SkuQuantity.fromLong(200),
                    emptyList(),
                ),
            ),
            poStatus = PLANNED,
        )
        val po2 =
            po1.copy(
                purchaseOrderSkus = po1.purchaseOrderSkus
                    .mapIndexed { i, poSku ->
                        poSku.copy(
                            expectedQuantity = poSku.expectedQuantity?.multiply(i.toBigDecimal())
                        )
                    },
            )
        val po3 = po1.copy(poStatus = PoStatus.SENT)

        // when
        val ret = runBlocking {
            calculationsCsvConverter.convertDailyToCsv(listOf(dailyView, dailyView2), listOf(po1, po2, po3))
        }
        // then
        val records = CSVFormat.DEFAULT.parse(StringReader(ret)).records

        assertEquals(3, records.size)

        assertDefaultCsvValues(dailyView, records.first { it[1] == dailyView.sku.skuId.toString() })
        assertEquals(
            listOf(po1, po2).flatMap { it.purchaseOrderSkus }.filter { it.skuId == dailyView.sku.skuId }
                .sumOf { it.expectedQuantity?.getValue() ?: BigDecimal.ZERO },
            records.first { it[1] == dailyView.sku.skuId.toString() }[16].toBigDecimal(),
        )

        assertDefaultCsvValues(dailyView2, records.first { it[1] == dailyView2.sku.skuId.toString() })
        assertEquals(
            listOf(po1, po2).flatMap { it.purchaseOrderSkus }.filter { it.skuId == dailyView2.sku.skuId }
                .sumOf { it.expectedQuantity?.getValue() ?: BigDecimal.ZERO },
            records.first { it[1] == dailyView2.sku.skuId.toString() }[16].toBigDecimal(),
        )
    }

    private fun assertDefaultCsvValues(dailyCalcResponse: DailyView, calcValues: CSVRecord) {
        kotlin.test.assertEquals(dailyCalcResponse.sku.skuId.toString(), calcValues[1])
        kotlin.test.assertEquals(dailyCalcResponse.sku.skuCode, calcValues[2])
        kotlin.test.assertEquals(dailyCalcResponse.sku.skuName, calcValues[3])
        kotlin.test.assertEquals(dailyCalcResponse.sku.skuCategories, calcValues[4])
        kotlin.test.assertEquals(dailyCalcResponse.productionDay.week, calcValues[5])
        kotlin.test.assertEquals(dailyCalcResponse.productionDay.day.toString(), calcValues[6])
        kotlin.test.assertEquals(dailyCalcResponse.calculation.dcCode, calcValues[0])
        kotlin.test.assertEquals(dailyCalcResponse.calculation.inbound, calcValues[7].toBigDecimal())
        kotlin.test.assertEquals(dailyCalcResponse.calculation.incomingPos, calcValues[8].toBigDecimal())
        kotlin.test.assertEquals(dailyCalcResponse.calculation.consumption, calcValues[9].toBigDecimal())
        kotlin.test.assertEquals(dailyCalcResponse.calculation.closingStock, calcValues[10].toBigDecimal())
        kotlin.test.assertEquals(dailyCalcResponse.calculation.usableStock, calcValues[11].toBigDecimal())
        kotlin.test.assertEquals(dailyCalcResponse.calculation.unusableStock, calcValues[12].toBigDecimal())
        kotlin.test.assertEquals(dailyCalcResponse.calculation.actualConsumption, calcValues[13].toBigDecimal())
        kotlin.test.assertEquals(dailyCalcResponse.calculation.substituted, calcValues[14].toLong())
    }
}
