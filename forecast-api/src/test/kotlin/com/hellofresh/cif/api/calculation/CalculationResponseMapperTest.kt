package com.hellofresh.cif.api.calculation

import com.hellofresh.cif.api.calculation.fixtures.DEFAULT_TEST_STRATEGY
import com.hellofresh.cif.api.calculation.fixtures.Default.dcCode
import com.hellofresh.cif.featureflags.Context.DC
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.AutomatedDcLiveRules
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import java.math.BigDecimal
import java.math.BigDecimal.ONE
import java.math.BigDecimal.TEN
import java.math.BigDecimal.ZERO
import java.time.LocalDate
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class CalculationResponseMapperTest {
    private val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(emptySet())
    private val calculationResponseMapper = CalculationResponseMapper(statsigFeatureFlagClient)

    @Test
    fun `should match multiple POs`() {
        // given
        val poRef = "2022PO"
        val expectedAmount = BigDecimal(11)
        val actualInboundAmount = BigDecimal(22)
        val dcCode = "VE"
        val skuPo1 = "$poRef-001"
        val skuPo2 = "$poRef-002"
        val calculation = Calculation(
            uom = UOM_UNIT,
            ZERO, ZERO, expectedAmount, actualInboundAmount, setOf(skuPo1, skuPo2),
            ZERO, ZERO, ZERO, ZERO, dcCode, false, null, DEFAULT_TEST_STRATEGY,
            ONE.negate(), ONE.negate(), ONE.negate(), 0, TEN, 0,
        )
        val weeklyView = WeeklyView("2020-W01", sku, calculation)

        // when
        val calculationResponse = calculationResponseMapper.toWeeklyCalculationResponse(weeklyView)

        // then
        assertEquals(2, calculationResponse.pos?.size)
        assertTrue(calculationResponse.pos?.containsAll(setOf(skuPo1, skuPo2)) ?: false)
        assertEquals(expectedAmount, calculationResponse.incomingPos.amount)
        assertEquals(actualInboundAmount, calculationResponse.inbound.amount)
    }

    @Test
    fun `should map the net needs in the weekly view`() {
        // given
        val poRef = "2022PO"
        val expectedAmount = BigDecimal(11)
        val actualInboundAmount = BigDecimal(22)
        val dcCode = "VE"
        val skuPo1 = "$poRef-001"
        val skuPo2 = "$poRef-002"

        val calculation = Calculation(
            uom = UOM_UNIT,
            ZERO, ZERO, expectedAmount, actualInboundAmount, setOf(skuPo1, skuPo2),
            ZERO, ZERO, ZERO, ZERO, dcCode, false, null, DEFAULT_TEST_STRATEGY,
            ONE.negate(), ONE.negate(), ONE.negate(), 0, TEN, null,
        )
        val weeklyView = WeeklyView("2020-W01", sku, calculation)

        // when
        val calculationResponse = calculationResponseMapper.toWeeklyCalculationResponse(weeklyView)

        // then
        assertEquals(BigDecimal(10), calculationResponse.netNeeds)
    }

    @Test
    fun `should expose staging and storage stock quantities when feature flag include-staging-storage-qtys is false`() {
        val calculation = newEmptyCalculation().copy(stagingStock = BigDecimal(11), storageStock = BigDecimal(12))
        val dayCalculation = DailyView(ProductionDay("2022-W01", LocalDate.now()), sku, calculation)

        // when
        val calculationResponse = calculationResponseMapper.toDailyCalculationResponse(dayCalculation)
        assertEquals(calculation.stagingStock, calculationResponse.stagingStock)
        assertEquals(calculation.storageStock, calculationResponse.storageStock)
    }

    @ParameterizedTest
    @MethodSource("purchaseOrderDueInInputs")
    fun `should return purchase order due in if its within the range (-7 to 10)`(
        poDueIn: Long,
        expectedPoDueIn: Long?,
    ) {
        statsigFeatureFlagClient.fixtures = emptySet()
        val calculation = newEmptyCalculation().copy(
            stagingStock = BigDecimal(11),
            storageStock = BigDecimal(12),
            poDueIn = poDueIn
        )
        val dayCalculation = DailyView(ProductionDay("2022-W01", LocalDate.now()), sku, calculation)

        // when
        val calculationResponse = calculationResponseMapper.toDailyCalculationResponse(dayCalculation)
        assertEquals(expectedPoDueIn, calculationResponse.poDueIn)
    }

    @Test
    fun `should not expose staging and storage quantities when feature flag include-staging-storage-qtys is true`() {
        statsigFeatureFlagClient.fixtures = setOf(AutomatedDcLiveRules(setOf(ContextData(DC, "VE"))))
        val calculation = newEmptyCalculation()
        val dayCalculation = DailyView(ProductionDay("2022-W01", LocalDate.now()), sku, calculation)

        // when
        val calculationResponse = calculationResponseMapper.toDailyCalculationResponse(dayCalculation)
        assertNull(calculationResponse.stagingStock)
        assertNull(calculationResponse.storageStock)
    }

    private fun newEmptyCalculation() =
        Calculation(
            uom = UOM_UNIT,
            ZERO, ZERO, ZERO, ZERO, emptySet(),
            ZERO, ZERO, ZERO, ZERO, dcCode, false, null, DEFAULT_TEST_STRATEGY,
            ONE.negate(), ZERO, ZERO, 0, TEN, 0,
        )

    companion object {
        private val sku = Sku(UUID.randomUUID(), "PHF-000", "Sku Name1 ", "")

        @Suppress("unused")
        @JvmStatic
        fun purchaseOrderDueInInputs() =
            Stream.of(
                Arguments.of(-40L, null, "purchase Order Due In greater than -7"),
                Arguments.of(40L, null, "purchase Order Due In greater than 10"),
                Arguments.of(-7L, -7L, "purchase Order Due In equals to -7"),
                Arguments.of(-6L, -6L, "purchase Order Due In less than -7"),
                Arguments.of(10L, 10L, "purchase Order Due In equals to 10"),
                Arguments.of(9L, 9L, "purchase Order Due In less than 10"),
                Arguments.of(2L, 2L, "purchase Order Due In with in -7 to 10"),
            )
    }
}
