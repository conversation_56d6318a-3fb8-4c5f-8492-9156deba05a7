package com.hellofresh.cif.api.calculation.experiment

import com.hellofresh.cif.api.calculation.generated.model.DailyCalculationExperimentResponse
import com.hellofresh.cif.api.stockupdate.StockUpdateResults
import com.hellofresh.cif.calculator.calculations.rules.TARGET_SAFETY_STOCK_DEFAULT_STRATEGY
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.DayCalculationResult
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull

class StockUpdateSimulationResponseMapperTest {
    private val today = LocalDate.now()

    @Test
    fun `should map to the experiment calculation response dto`() {
        // given
        val dayCalculationResult = createDayCalculationResult(today)

        // when
        val result = CalculationExperimentResponseMapper.mapToCalculationExperimentResponse(
            StockUpdateResults(
                listOf(dayCalculationResult),
                mapOf(
                    CalculationKey(dayCalculationResult.cskuId, dayCalculationResult.dcCode, today) to SkuQuantity.fromLong(100L, UOM_UNIT),
                ),
                emptySet(),
                emptyMap(),
            ),
        )[0]

        // then
        assertDayCalculationResult(dayCalculationResult, result)
        assertEquals(100, result.experiment)
        assertEquals(10, result.actualConsumption)
    }

    @Test
    fun `should NOT map the experiment value in the calculation response dto`() {
        // given
        val pastDate = LocalDate.now().minusDays(10)
        val dayCalculationResult = createDayCalculationResult(today)

        // when
        val result = CalculationExperimentResponseMapper.mapToCalculationExperimentResponse(
            StockUpdateResults(
                listOf(dayCalculationResult),
                mapOf(
                    CalculationKey(dayCalculationResult.cskuId, dayCalculationResult.dcCode, pastDate) to SkuQuantity.fromLong(100L, UOM_UNIT),
                ),
                emptySet(),
                emptyMap(),
            ),
        )[0]

        // then
        assertDayCalculationResult(dayCalculationResult, result)
        assertNull(result.experiment)
        assertEquals(10, result.actualConsumption)
    }

    @Test
    fun `should NOT map the experiment value in the calculation response dto with the given empty experiment input`() {
        // given
        val dayCalculationResult = createDayCalculationResult(today)

        // when
        val result = CalculationExperimentResponseMapper.mapToCalculationExperimentResponse(
            StockUpdateResults(
                listOf(dayCalculationResult),
                emptyMap(),
                emptySet(),
                emptyMap(),
            ),
        )[0]

        // then
        assertDayCalculationResult(dayCalculationResult, result)
        assertNull(result.experiment)
    }

    private fun createDayCalculationResult(today: LocalDate) = DayCalculationResult(
        UOM_UNIT,
        UUID.randomUUID(), "VE", today, SkuQuantity.fromLong(1),
        SkuQuantity.fromLong(
            2,
        ),
        SkuQuantity.fromLong(3), SkuQuantity.fromLong(4), emptySet(), SkuQuantity.fromLong(5), emptySet(),
        SkuQuantity.fromLong(
            6,
        ),
        SkuQuantity.fromLong(
            7,
        ),
        SkuQuantity.fromLong(
            8,
        ),
        SkuQuantity.fromLong(9), "2022-W53", SkuQuantity.fromLong(10), netNeeds = SkuQuantity.fromLong(10),
        strategy = TARGET_SAFETY_STOCK_DEFAULT_STRATEGY,
    )

    private fun assertDayCalculationResult(
        dayCalculationResult: DayCalculationResult,
        result: DailyCalculationExperimentResponse
    ) {
        dayCalculationResult.apply {
            assertEquals(actualInbound.getValue().toInt(), result.inbound)
            assertEquals(expectedInbound.getValue().toInt(), result.incoming)
            assertEquals(unusable.getValue().toInt(), result.unusable)
            assertEquals(closingStock.getValue().toLong(), result.closing.toLong())
            assertEquals(openingStock.getValue().toLong(), result.opening.toLong())
            assertEquals(demanded.getValue().toLong(), result.consumption.toLong())
            assertEquals(actualConsumption.getValue().toLong(), result.actualConsumption.toLong())
        }
    }
}
