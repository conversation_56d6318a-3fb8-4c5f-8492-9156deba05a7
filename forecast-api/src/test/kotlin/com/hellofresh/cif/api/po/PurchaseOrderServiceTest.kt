package com.hellofresh.cif.api.po

import com.hellofresh.cif.api.configuration.ConfigService
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.PoStatus.APPROVED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderSku
import com.hellofresh.cif.purchaseorder.PurchaseOrderRepository
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.slot
import java.time.DayOfWeek
import java.time.DayOfWeek.MONDAY
import java.time.DayOfWeek.TUESDAY
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZoneOffset
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking

class PurchaseOrderServiceTest {
    private val poRepoMock: PurchaseOrderRepository = mockk()
    private val dcConfigRepoMock: ConfigService = mockk()
    private val poService = PurchaseOrderService(poRepoMock, dcConfigRepoMock)

    @Test
    fun `should return POs by fromDate and toDate if supplied`() {
        // given
        val today = LocalDate.now()
        val dcWeek = DcWeek("2000-W01")
        val dcCodes = setOf("VE", "BY")
        val poReq = PurchaseOrdersReq(
            UUID.randomUUID(),
            dcCodes,
            weeks = setOf(dcWeek),
            fromDate = today,
            toDate = today
        )

        dcCodes.forEach {
            coEvery { dcConfigRepoMock.getByCode(it) } returns
                DistributionCenterConfiguration(it, MONDAY, TUESDAY, "dach", ZoneId.of("UTC"), true, true, wmsType = WmsSystem.WMS_SYSTEM_FCMS,)
        }

        val slot = slot<Set<DateRange>>()
        val expectedPo =
            PurchaseOrder(
                "PO1",
                "PO1",
                null,
                dcCodes.first(),
                null,
                null,
                listOf(PurchaseOrderSku(poReq.skuId, SkuQuantity.fromLong(1L), emptyList())),
                poStatus = APPROVED
            )
        coEvery { poRepoMock.findPurchaseOrdersWithAsns(any(), dcCodes, capture(slot)) } returns listOf(expectedPo)

        // when
        val result = runBlocking { poService.findPurchaseOrders(poReq) }

        // then
        assertEquals(setOf(DateRange(today, today)), slot.captured)
        assertEquals(listOf(expectedPo), result)
    }

    @Test
    fun `should return POs by week selection if fromDate and toDate are missing`() {
        val dcWeeks = setOf(DcWeek("2023-W01"), DcWeek("2023-W02"))
        val dcProductionStartDay = DayOfWeek.MONDAY
        val dcZoneId = ZoneOffset.UTC
        val poReq = PurchaseOrdersReq(UUID.randomUUID(), setOf("VE"), weeks = dcWeeks, fromDate = null, toDate = null)

        coEvery { dcConfigRepoMock.getByCode(poReq.dcCodes.first()) } returns
            DistributionCenterConfiguration(
                poReq.dcCodes.first(),
                DayOfWeek.valueOf(dcProductionStartDay.name),
                TUESDAY,
                "dach",
                ZoneId.of(dcZoneId.id),
                true,
                true,
                wmsType = WmsSystem.WMS_SYSTEM_FCMS,
            )

        val expectedDateRanges = dcWeeks.map {
            DateRange(
                it.getStartDateInDcWeek(dcProductionStartDay, dcZoneId),
                it.getLastDateInDcWeek(dcProductionStartDay, ZoneOffset.UTC),
            )
        }.toSet()
        val slot = slot<Set<DateRange>>()
        val expectedPo =
            PurchaseOrder(
                "PO1",
                "PO1",
                null,
                poReq.dcCodes.first(),
                null,
                null,
                listOf(PurchaseOrderSku(poReq.skuId, SkuQuantity.fromLong(1L), emptyList())),
                poStatus = APPROVED
            )
        coEvery { poRepoMock.findPurchaseOrdersWithAsns(any(), any(), capture(slot)) } returns listOf(expectedPo)

        // when
        val result = runBlocking { poService.findPurchaseOrders(poReq) }

        // then
        assertEquals(expectedDateRanges, slot.captured)
        assertEquals(listOf(expectedPo), result)
    }
}
