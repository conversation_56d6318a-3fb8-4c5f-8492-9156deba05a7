package com.hellofresh.cif.api.calculation

import com.hellofresh.cif.api.calculation.CalculationServiceMapper.toDailyCalculation
import com.hellofresh.cif.api.calculation.SortBy.SKU_CODE
import com.hellofresh.cif.api.calculation.db.CalculationRecord
import com.hellofresh.cif.api.calculation.db.CalculationRepository
import com.hellofresh.cif.api.calculation.fixtures.default
import com.hellofresh.cif.api.calculation.stockupdate.CalculationsPendingStockUpdateService
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.inventory.models.UsableInventoryEvaluator
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import java.time.LocalDate
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class CalculationServiceSelectorTest {

    private val dcConfigService = mockk<DcConfigService>()
    private val usableInventoryEvaluator = mockk<UsableInventoryEvaluator>(relaxed = true)
    private val calculationsPendingStockUpdateService = mockk<CalculationsPendingStockUpdateService>(relaxed = true)
    private val prodCalculationRepoMock = mockk<CalculationRepository>(relaxed = true)
    private val preProdCalculationRepoMock = mockk<CalculationRepository>(relaxed = true)
    private val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(emptySet())
    private val calculationsService = CalculationsService(
        SimpleMeterRegistry(),
        dcConfigService,
        usableInventoryEvaluator,
        calculationsPendingStockUpdateService,
        prodCalculationRepoMock,
        preProdCalculationRepoMock,
        statsigFeatureFlagClient,
    )
    private val distributionCenterConfiguration = DistributionCenterConfiguration.default("VE")
    private val dcVE = listOf(distributionCenterConfiguration.dcCode)
    private val dcWeek = DcWeek(LocalDate.now().plusWeeks(2), distributionCenterConfiguration.productionStart)
    private val weeks = listOf(dcWeek.value)
    private val page = Page(0, 1)
    private val dailyRecord = CalculationRecord.default(dcCode = distributionCenterConfiguration.dcCode).copy(
        productionWeek = dcWeek.value,
        date = dcWeek.getStartDateInDcWeek(
            distributionCenterConfiguration.productionStart,
            distributionCenterConfiguration.zoneId,
        ),
    )
    private val dailyRecords = listOf(dailyRecord)
    private val pageCount = 0
    private val calculationsPage = CalculationsPage(
        dailyRecords,
        pageCount,
        0,
        dailyRecords.map { it.code }.distinct().count(),
    )

    @BeforeEach
    fun init() {
        coEvery {
            dcConfigService.dcConfigurations
        } returns mapOf(distributionCenterConfiguration.dcCode to distributionCenterConfiguration)
        coEvery { preProdCalculationRepoMock.fetchPageableCalculations(any()) } returns calculationsPage
        coEvery { prodCalculationRepoMock.fetchPageableCalculations(any()) } returns calculationsPage
    }

    @Test
    fun `Should use production calculator when the calculation request has consumptionDaysAhead as 0`() {
        val calculationRequestProd = CalculationRequest(dcVE, weeks, pageRequest = page, consumptionDaysAhead = 0)
        val output = runBlocking { calculationsService.getDailyCalculations(calculationRequestProd) }

        coVerify(exactly = 1) { prodCalculationRepoMock.fetchPageableCalculations(calculationRequestProd) }
        coVerify(exactly = 0) { preProdCalculationRepoMock.fetchPageableCalculations(calculationRequestProd) }
        assertEquals(dailyRecords.map { toDailyCalculation(it, usableInventoryEvaluator) }, output.calculationPage)
        assertEquals(pageCount, output.totalPages)
    }

    @Test
    fun `Should use preproduction calculator when the calculation request has consumptionDaysAhead as 1`() {
        val calculationRequestPreProd = CalculationRequest(dcVE, weeks, pageRequest = page, consumptionDaysAhead = 1)
        val output = runBlocking { calculationsService.getDailyCalculations(calculationRequestPreProd) }

        coVerify(exactly = 1) { preProdCalculationRepoMock.fetchPageableCalculations(calculationRequestPreProd) }
        coVerify(exactly = 0) { prodCalculationRepoMock.fetchPageableCalculations(calculationRequestPreProd) }
        assertEquals(dailyRecords.map { toDailyCalculation(it, usableInventoryEvaluator) }, output.calculationPage)
        assertEquals(pageCount, output.totalPages)
    }

    @Test
    fun `Should throw illegalArgumentException when the calculation request has consumptionDaysAhead other than 0 or 1`() {
        assertThrows<IllegalArgumentException> {
            runBlocking {
                calculationsService.getDailyCalculations(
                    CalculationRequest(
                        dcVE,
                        weeks,
                        page,
                        consumptionDaysAhead = 2,
                    ),
                )
            }
        }
    }

    @Test
    fun `should sort by skuName by default`() {
        val calculationRequestPreProd = CalculationRequest(dcVE, weeks, pageRequest = page, consumptionDaysAhead = 0)
        runBlocking {
            calculationsService.getDailyCalculations(calculationRequestPreProd)
            coVerify(exactly = 1) { prodCalculationRepoMock.fetchPageableCalculations(calculationRequestPreProd) }
        }
    }

    @Test
    fun `should sort by skuCode when requested`() {
        val calculationRequestPreProd = CalculationRequest(
            dcVE,
            weeks,
            pageRequest = page,
            consumptionDaysAhead = 0,
            sortBy = SKU_CODE,
        )
        runBlocking {
            calculationsService.getDailyCalculations(calculationRequestPreProd)
            coVerify(exactly = 1) { prodCalculationRepoMock.fetchPageableCalculations(calculationRequestPreProd) }
        }
    }
}
