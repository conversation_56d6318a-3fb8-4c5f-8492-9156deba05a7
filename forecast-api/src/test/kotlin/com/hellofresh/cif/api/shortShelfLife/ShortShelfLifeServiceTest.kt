package com.hellofresh.cif.api.shortShelfLife

import com.hellofresh.cif.api.calculation.fixtures.default
import com.hellofresh.cif.api.calculation.generated.model.UomEnum.UNIT
import com.hellofresh.cif.api.shortShelfLife.model.ShortShelfLife
import com.hellofresh.cif.api.shortShelfLife.model.ShortShelfLifeConfigKey
import com.hellofresh.cif.api.shortShelfLife.model.ShortShelfLifeConfigWithStockUpdate
import com.hellofresh.cif.api.shortShelfLife.repository.ShortShelfLifeRepositoryImpl
import com.hellofresh.cif.api.stockupdate.StockUpdateApiService
import com.hellofresh.cif.api.stockupdate.StockUpdateCalculationService
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.inventory.StockUpdate
import com.hellofresh.cif.inventory.StockUpdateService
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.sqr.shortshelflife.SQRShortShelfLifeConf
import com.hellofresh.cif.sqr.shortshelflife.SQRShortShelfLifeConfigurations
import com.hellofresh.cif.sqr.shortshelflife.repository.SQRShortShelfLifeConfRepository
import io.mockk.coEvery
import io.mockk.mockk
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZoneId
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.Test
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class ShortShelfLifeServiceTest {
    private val shortShelfLifeRepositoryImpl: ShortShelfLifeRepositoryImpl = mockk<ShortShelfLifeRepositoryImpl>(
        relaxed = true,
    )
    private val dcConfigService: DcConfigService = mockk<DcConfigService>(relaxed = true)
    private val stockUpdateApiService: StockUpdateApiService = mockk<StockUpdateApiService>(relaxed = true)
    private val stockUpdateCalculationService: StockUpdateCalculationService = mockk<StockUpdateCalculationService>(
        relaxed = true,
    )
    private val sqrShortShelfLifeConfRepository: SQRShortShelfLifeConfRepository =
        mockk<SQRShortShelfLifeConfRepository>(relaxed = true)
    private val stockUpdateService: StockUpdateService = mockk<StockUpdateService>(relaxed = true)
    private val shortShelfLifeService = ShortShelfLifeService(
        shortShelfLifeRepositoryImpl,
        dcConfigService,
        stockUpdateApiService,
        stockUpdateCalculationService,
        sqrShortShelfLifeConfRepository,
        stockUpdateService,
    )

    @Test
    fun `should include the past weeks while getting the short shelf life data`() {
        val dcCode = "GR"
        val dcWeek1 = "2025-W07"
        val dcWeek2 = "2025-W08"
        val dc = DistributionCenterConfiguration.default(dcCode).copy(zoneId = ZoneId.of("UTC"))
        val skuId = UUID.randomUUID()

        val week07SSL = (10..13).map {
            val date = LocalDate.parse("2025-02-$it")
            ShortShelfLife.Companion.default(dcCode, dcWeek1, skuId, date)
        }
        val week08SSL = (14..20).map {
            val date = LocalDate.parse("2025-02-$it")
            ShortShelfLife.Companion.default(dcCode, dcWeek2, skuId, date)
        }
        val week07SSLConf = (10..13).map {
            val date = LocalDate.parse("2025-02-$it")
            SQRShortShelfLifeConf.default(dcCode, skuId, date)
        }
        val week08SSLConf = (14..20).map {
            val date = LocalDate.parse("2025-02-$it")
            SQRShortShelfLifeConf.default(dcCode, skuId, date)
        }
        coEvery {
            dcConfigService.dcConfigurations[dcCode]
        } returns DistributionCenterConfiguration.default(dcCode).copy(zoneId = ZoneId.of("UTC"))

        coEvery {
            shortShelfLifeRepositoryImpl.getShortShelfLife(dcCode, any())
        } returns
            week07SSL + week08SSL

        coEvery {
            sqrShortShelfLifeConfRepository.fetchSQRShortShelfLifeConfigurationFromWeek(dcWeek1, dc)
        } returns SQRShortShelfLifeConfigurations(
            week07SSLConf + week08SSLConf,
            setOf(dc),
        )

        val week07StockUpdates = (10..13).map {
            val date = LocalDate.parse("2025-02-$it")
            StockUpdate.Companion.default(
                skuId = skuId,
                dcCode = dcCode,
                date = date,
                dcWeek = dcWeek1,
            )
        }
        val week08StockUpdates = (14..20).map {
            val date = LocalDate.parse("2025-02-$it")
            StockUpdate.Companion.default(
                skuId = skuId,
                dcCode = dcCode,
                date = date,
                dcWeek = dcWeek1,
            )
        }

        coEvery {
            stockUpdateService.getStockUpdates(any(), any(), any())
        } returns week07StockUpdates + week08StockUpdates

        runBlocking {
            val shortShelfLifeResponse = shortShelfLifeService.getShortShelfLife(
                dcCode,
                setOf(
                    DcWeek("2025-W07"),
                    DcWeek("2025-W08"),
                ),
            )

            assertEquals(BigDecimal.ONE, shortShelfLifeResponse.totalNoOfSkus)
            assertEquals(4, shortShelfLifeResponse.skus.first().data.first().sqrs.size)
            val pastWeekSSL = shortShelfLifeResponse.skus.first().data.first().sqrs.find {
                it.date == LocalDate.parse("2025-02-10")
            }
            assertEquals(
                10,
                pastWeekSSL?.bufferAdditional?.toInt(),
            )
            assertEquals(
                10,
                pastWeekSSL?.bufferPercentage?.toInt(),
            )
        }
    }

    @ParameterizedTest
    @MethodSource("provideShortShelfLifeTestCases")
    fun `verify getShortShelfLifeConfigsMatching`(
        sourceShortShelfLife: Map<ShortShelfLifeConfigKey, ShortShelfLifeConfigWithStockUpdate>,
        calcShortShelfLife: List<ShortShelfLife>,
        expectedMatching: Int,
        expectedNonMatching: Int
    ) {
        val result = shortShelfLifeService.getShortShelfLifeConfigsMatching(sourceShortShelfLife, calcShortShelfLife)
        assertEquals(expectedMatching, result.matchingList.size, "Matching list size mismatch")
        assertEquals(expectedNonMatching, result.nonMatchingList.size, "Non-matching list size mismatch")
    }

    companion object {
        @JvmStatic
        fun provideShortShelfLifeTestCases(): Stream<Arguments> {
            val skuId1 = UUID.randomUUID()
            val skuId2 = UUID.randomUUID()
            val date = LocalDate.now()

            val key1 = ShortShelfLifeConfigKey("DC1", skuId1, date)
            val key2 = ShortShelfLifeConfigKey("DC1", skuId2, date)

            val sourceConfigMap = mapOf(
                key1 to ShortShelfLifeConfigWithStockUpdate(
                    "2025-W09",
                    BigDecimal("5"),
                    BigDecimal("10"),
                    SkuQuantity.fromBigDecimal(BigDecimal("100")),
                    1,
                    false,
                ),
                key2 to ShortShelfLifeConfigWithStockUpdate(
                    "2025-W09",
                    BigDecimal("3"),
                    BigDecimal("7"),
                    SkuQuantity.fromBigDecimal(BigDecimal("50")),
                    1,
                    false,
                ),
            )

            val calcShortShelfLifeMatching = listOf(
                ShortShelfLife(
                    skuId1, "Item1", "Code1", "Category1", "DC1", "Week1", date,
                    BigDecimal("10"), BigDecimal("1"), BigDecimal("5"), BigDecimal("100"),
                    BigDecimal("20"), BigDecimal("5"), BigDecimal("10"), UNIT, false,
                ),

                ShortShelfLife(
                    skuId2, "Item2", "Code2", "Category2", "DC1", "Week1", date,
                    BigDecimal("8"), BigDecimal("2"), BigDecimal("3"), BigDecimal("50"),
                    BigDecimal("15"), BigDecimal("3"), BigDecimal("7"), UNIT, false,
                ),
            )

            val calcShortShelfLifeNonMatching = listOf(
                ShortShelfLife(
                    skuId1, "Item1", "Code1", "Category1", "DC1", "Week1", date,
                    BigDecimal("10"), BigDecimal("1"), BigDecimal("5"), BigDecimal("200"), // StockUpdate mismatch
                    BigDecimal("20"), BigDecimal("5"), BigDecimal("10"), UNIT, false,
                ),

                ShortShelfLife(
                    skuId2, "Item2", "Code2", "Category2", "DC1", "Week1", date,
                    BigDecimal("8"), BigDecimal("2"), BigDecimal("3"), BigDecimal("50"),
                    BigDecimal("15"), BigDecimal("4"), BigDecimal("8"), UNIT, false,
                ), // Buffer mismatch
            )

            return Stream.of(
                Arguments.of(sourceConfigMap, calcShortShelfLifeMatching, 2, 0), // All matching
                Arguments.of(sourceConfigMap, calcShortShelfLifeNonMatching, 0, 2), // All non-matching
            )
        }
    }
}
