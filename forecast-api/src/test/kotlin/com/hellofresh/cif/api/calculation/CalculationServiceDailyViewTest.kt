package com.hellofresh.cif.api.calculation

import com.hellofresh.calculator.models.ForecastInventory
import com.hellofresh.cif.api.calculation.InventoryRefreshType.CLEARDOWN
import com.hellofresh.cif.api.calculation.db.CalculationRecord
import com.hellofresh.cif.api.calculation.fixtures.DEFAULT_TEST_STRATEGY
import com.hellofresh.cif.api.calculation.fixtures.default
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.inventory.CurrentStockUpdates
import com.hellofresh.inventory.models.LocationType
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import java.math.BigDecimal
import java.math.BigDecimal.ONE
import java.math.BigDecimal.TEN
import java.math.BigDecimal.ZERO
import java.time.DayOfWeek.MONDAY
import java.time.DayOfWeek.WEDNESDAY
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking

internal class CalculationServiceDailyViewTest : AbstractCalculationServiceViewsTest() {

    private val service: CalculationsService =
        CalculationsService(
            SimpleMeterRegistry(),
            dcConfigService,
            usableInventoryEvaluatorMock,
            calculationsPendingStockUpdateServiceMock,
            prodCalculationRepositoryMock,
            mockk(),
            statsigFeatureFlagClient,
        )

    @Test
    fun `should return all the daily views`() {
        val week = "2022-W1"
        val calculationFilters = CalculationRequest(
            dcCodes = listOf(dcCodeVE),
            weeks = listOf(week),
            pageRequest = AllPages,
            consumptionDaysAhead = 0,
            inventoryRefreshType = CLEARDOWN,
        )
        mockRepository(calculationFilters, emptyList())

        // when
        runBlocking {
            service.getDailyCalculations(calculationFilters).calculationPage
            coVerify(exactly = 1) { prodCalculationRepositoryMock.fetchPageableCalculations(calculationFilters) }
        }
    }

    @Test
    fun `should return accummulated unusable for daily views`() {
        val dc = DistributionCenterConfiguration.default("DC").copy(
            productionStart = MONDAY,
            cleardown = WEDNESDAY,
        )
        coEvery { dcConfigService.dcConfigurations } returns mapOf(dc.dcCode to dc)

        val week = ProductionWeek(LocalDate.now(), dc.productionStart)
        val previousWeek = week.minusWeeks(1)

        val sku = Sku(UUID.randomUUID(), "PHF-0001", "Sku Name1 ", "")
        val dailyViewSku = dailyRecordsWithUnusable(sku, week, dc)
        val dailyViewSkuPrevious = dailyRecordsWithUnusable(sku, previousWeek, dc)

        val calculationRequest = CalculationRequest(
            listOf(dc.dcCode),
            listOf(week.dcWeek.value),
            AllPages,
            consumptionDaysAhead = 0,
            inventoryRefreshType = CLEARDOWN,
        )

        mockRepository(
            calculationRequest.copy(
                weeks = calculationRequest.weeks,
            ),
            dailyViewSku,
        )
        mockRepository(
            CalculationRequest(
                dcCodes = calculationRequest.dcCodes,
                weeks = listOf(previousWeek.dcWeek.value),
                pageRequest = AllPages,
                inventoryRefreshType = calculationRequest.inventoryRefreshType,
                consumptionDaysAhead = calculationRequest.consumptionDaysAhead,
            ),
            dailyViewSkuPrevious,
        )

        // when
        val calculationsPage = runBlocking {
            service.getDailyCalculations(calculationRequest).calculationPage
        }

        assertEquals(dailyViewSku.size, calculationsPage.size)
        calculationsPage
            .sortedBy { it.productionDay.day }
            .forEach { dailyView ->
                val actual: BigDecimal = (dailyViewSku + dailyViewSkuPrevious)
                    .filter { dc.getCleardown(dailyView.productionDay.day) == dc.getCleardown(it.date) }
                    .filter { c -> c.date <= dailyView.productionDay.day }
                    .sumOf { it.expired }
                assertEquals(
                    dailyView.calculation.unusableStock,
                    actual,
                )
            }
    }

    @Test
    fun `pending calculation replace fetched calculations`() {
        // given
        val week = "2022-W01"
        val skuId = UUID.randomUUID()
        val date = LocalDate.now()
        val calculationRecord =
            CalculationRecord.Companion.default(dcCodeVE).copy(
                cskuId = skuId,
                productionWeek = week,
                date = date,
                stockUpdate = TEN,
            )

        val calculationRequest = CalculationRequest(listOf(dcCodeVE), emptyList(), AllPages, consumptionDaysAhead = 0)
            .copy(weeks = listOf(week), sortBy = SortBy.SKU_CODE)

        val pendingRecord = calculationRecord.copy(closingStock = 1000.toBigDecimal())
        val currentStockUpdates = mockk<CurrentStockUpdates>()
        coEvery {
            calculationsPendingStockUpdateServiceMock.getCurrentStockUpdates(calculationRequest)
        } returns currentStockUpdates

        coEvery {
            calculationsPendingStockUpdateServiceMock.processPendingCalculations(
                calculationRequest,
                listOf(calculationRecord).associateBy { it.toKey() }, currentStockUpdates, any(),

            )
        } returns listOf(pendingRecord).associateBy { it.toKey() }

        // when
        coEvery {
            prodCalculationRepositoryMock.fetchPageableCalculations(calculationRequest)
        } returns CalculationsPage(listOf(calculationRecord), 1, 0, 1)

        val pageableCalculations = runBlocking { service.getDailyCalculations(calculationRequest) }

        // then
        assertEquals(1, pageableCalculations.calculationPage.size)
        assertEquals(CalculationStatus.PENDING, pageableCalculations.calculationPage.first().calculation.status)
        assertEquals(
            pendingRecord.closingStock,
            pageableCalculations.calculationPage.first().calculation.closingStock,
        )
    }

    @Test
    fun `pending calculations are not cached`() {
        // given
        val week = "2022-W01"
        val skuId = UUID.randomUUID()
        val date = LocalDate.now()
        val calculationRecord =
            CalculationRecord.Companion.default(dcCodeVE).copy(
                cskuId = skuId,
                productionWeek = week,
                date = date,
                stockUpdate = TEN,

            )

        val calculationRequest = CalculationRequest(
            listOf(dcCodeVE),
            emptyList(),
            AllPages,
            consumptionDaysAhead = 0,
        ).copy(
            weeks = listOf(week),
            sortBy = SortBy.SKU_CODE,
        )

        val currentStockUpdates = mockk<CurrentStockUpdates>()
        coEvery {
            calculationsPendingStockUpdateServiceMock.getCurrentStockUpdates(calculationRequest)
        } returns currentStockUpdates

        val pendingCalculation = calculationRecord.copy(closingStock = 1000.toBigDecimal())
        coEvery {
            calculationsPendingStockUpdateServiceMock.processPendingCalculations(
                calculationRequest, mapOf(calculationRecord.toKey() to calculationRecord), currentStockUpdates, any(),
            )
        } returns mapOf(pendingCalculation.toKey() to pendingCalculation)

        // when
        coEvery {
            prodCalculationRepositoryMock.fetchPageableCalculations(
                calculationRequest,
            )
        } returns CalculationsPage(listOf(calculationRecord), 1, 0, 1)

        val pageableCalculations = runBlocking { service.getDailyCalculations(calculationRequest) }

        // then
        assertEquals(1, pageableCalculations.calculationPage.size)
        assertEquals(
            pendingCalculation.closingStock,
            pageableCalculations.calculationPage.first().calculation.closingStock,
        )
        assertEquals(CalculationStatus.PENDING, pageableCalculations.calculationPage.first().calculation.status)

        coEvery {
            calculationsPendingStockUpdateServiceMock.processPendingCalculations(
                calculationRequest, mapOf(calculationRecord.toKey() to calculationRecord), currentStockUpdates, any(),
            )
        } returns emptyMap()

        val pageableCalculationsProcessed = runBlocking { service.getDailyCalculations(calculationRequest) }

        assertEquals(1, pageableCalculationsProcessed.calculationPage.size)
        assertEquals(
            CalculationStatus.PROCESSED,
            pageableCalculationsProcessed.calculationPage.first().calculation.status,
        )
        assertEquals(
            calculationRecord.stockUpdate,
            pageableCalculationsProcessed.calculationPage.first().calculation.stockUpdate,
        )
    }

    fun dailyRecordsWithUnusable(
        sku: Sku,
        productionWeek: ProductionWeek,
        distributionCenterConfiguration: DistributionCenterConfiguration
    ) = (0L until DAYS).map {
        val date =
            productionWeek.dcWeek.getStartDateInDcWeek(
                distributionCenterConfiguration.productionStart,
                distributionCenterConfiguration.zoneId,
            ).plusDays(it)
        CalculationRecord(
            date = date,
            productionWeek = productionWeek.dcWeek.value,
            cskuId = sku.skuId,
            code = sku.skuCode,
            name = sku.skuName,
            category = sku.skuCategories,
            coolingType = null,
            packaging = null,
            acceptableCodeLife = 0,
            uom = Uom.UOM_UNIT,
            expectedInbound = ONE,
            expectedInboundPo = null,
            actualInbound = ONE,
            actualInboundPo = null,
            openingStock = ZERO,
            expired = it.toBigDecimal(),
            present = ZERO,
            demanded = ZERO,
            dailyNeeds = ZERO,
            closingStock = TEN,
            actualConsumption = ZERO,
            dcCode = distributionCenterConfiguration.dcCode,
            skuAtRisk = false,
            safetyStock = null,
            strategy = DEFAULT_TEST_STRATEGY,
            safetyStockNeeds = null,
            storageStock = ZERO,
            stagingStock = ZERO,
            poDueIn = null,
            netNeeds = TEN,
            subbed = null,
            stockUpdate = null,
            unusableStockDetails = listOf(ForecastInventory(BigDecimal(it), null, LocationType.LOCATION_TYPE_WASTE)),
        )
    }
}
