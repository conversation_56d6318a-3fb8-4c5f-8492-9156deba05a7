package com.hellofresh.cif.api.calculation

import com.hellofresh.cif.api.calculation.db.CalculationsQueryStep.Companion.deserializeCalculation
import com.hellofresh.cif.api.schema.enums.Uom
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test

class CalculationsDbJoinJsonParseTest {

    @Test
    fun `should marshal the resulting json after joining calculations data`() {
        val stagingStock = 10
        val storageStock = 11
        val expectedPos = setOf("2233BV458630_E1", "2233BV425609_O1")
        val actualPos = setOf("2233BV458630_E1", "2233BV425609_O1")
        val calculationsArrayJsonStr = """
[
  {
    "dc_code": "BV",
    "production_week": "2022-W33",
    "csku_id": "81f79f2f-b3a1-4afa-9665-a8cad70f6093",
    "name": "Sliced Mushrooms 120g",
    "code": "PHF-10-000432-4",
    "category": "PHF",
    "date": "2022-08-12",
    "uom": "UOM_UNIT",
    "expired": 0,
    "opening_stock": 12312,
    "storage_stock": $storageStock,
    "staging_stock": $stagingStock,
    "demanded": 9655,
    "actual_consumption": 4324,
    "present": 12312,
    "closing_stock": 22529,
    "actual_inbound": 19872,
    "expected_inbound": 19872,
    "actual_inbound_po": "${actualPos.joinToString(",")}",
    "expected_inbound_po": "${expectedPos.joinToString(",")}",
    "issued_at": "2022-08-15T00:02:07",
    "daily_needs": 0,
    "net_needs": 10,
    "strategy": "UNSPECIFIED_STRATEGY",
    "cooling_type": "Production",
    "packaging": "Assembly-Cool Pouch"
  }
]
        """.trimIndent()

        val calculationsArray = deserializeCalculation(calculationsArrayJsonStr)

        assertEquals(1, calculationsArray.size)
        assertEquals(expectedPos, calculationsArray.first().expectedInboundPo)
        assertEquals(actualPos, calculationsArray.first().actualInboundPo)
        assertEquals(stagingStock, calculationsArray.first().stagingStock?.toInt())
        assertEquals(storageStock, calculationsArray.first().storageStock?.toInt())
        assertEquals(Uom.UOM_UNIT, calculationsArray.first().uom)
    }
}
