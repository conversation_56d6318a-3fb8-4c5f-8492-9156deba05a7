package com.hellofresh.cif.api.configuration

import java.time.DayOfWeek.FRIDAY
import java.time.DayOfWeek.MONDAY
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneOffset
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

internal class DcConfigurationTest {
    private val dcCode = "VE"
    private val market = "dach"

    @Test
    fun `should not be able to create DcConfiguration if the dc has cleardown enabled with null cleardown time`() {
        val exception = assertThrows<IllegalArgumentException> {
            DcConfiguration(dcCode, market, MONDAY, FRIDAY, ZoneOffset.UTC, null, true, LocalTime.now())
        }
        assertEquals("cleardown time should exist if the dc cleardown is enabled : VE", exception.message)
    }

    @ParameterizedTest
    @MethodSource("hasCleardown")
    fun `should be able to create DcConfiguration if the dc has cleardown enabled or disabled`(
        hasCleardown: Boolean,
        lastCleardownTime: OffsetDateTime?
    ) {
        val dcConfig = DcConfiguration(
            dcCode,
            market,
            MONDAY,
            FRIDAY,
            ZoneOffset.UTC,
            lastCleardownTime,
            hasCleardown,
            LocalTime.now(),
        )
        assertDcConfig(dcConfig, hasCleardown)
    }

    private fun assertDcConfig(dcConfig: DcConfiguration, hasCleardown: Boolean) {
        assertEquals(dcCode, dcConfig.dcCode)
        assertEquals(market, dcConfig.market)
        assertEquals(hasCleardown, dcConfig.hasCleardown)
        assertEquals(MONDAY, dcConfig.productionStartDay)
        assertEquals(FRIDAY, dcConfig.clearDownDay)
        assertEquals(ZoneOffset.UTC, dcConfig.zoneId)
    }

    companion object {
        @Suppress("unused")
        @JvmStatic
        fun hasCleardown() =
            listOf(Arguments.of(false, null), Arguments.of(true, OffsetDateTime.now()))
    }
}
