package com.hellofresh.cif.api.to

import com.hellofresh.cif.api.configuration.ConfigService
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.transferorder.db.TransferOrderRepository
import com.hellofresh.cif.transferorder.model.DeliveryInfo
import com.hellofresh.cif.transferorder.model.DeliveryInfoStatus
import com.hellofresh.cif.transferorder.model.TransferOrder
import com.hellofresh.cif.transferorder.model.TransferOrderSku
import com.hellofresh.cif.transferorder.model.TransferOrderStatus
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.slot
import java.time.DayOfWeek
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking

class TransferOrderServiceTest {
    private val repositoryMock = mockk<TransferOrderRepository>()
    private val configServiceMock = mockk<ConfigService>()
    private val service = TransferOrderService(repositoryMock, configServiceMock)

    @Test
    fun `should return transfer orders by fromDate and toDate if supplied`() {
        val skuId = UUID.randomUUID()
        val fromDate = LocalDate.of(2025, 7, 1)
        val toDate = LocalDate.of(2025, 7, 5)
        val dcCodes = setOf("VE")
        val weeks = setOf(DcWeek("2025-W27"))

        val request = TransferOrdersReq(
            skuId = skuId,
            dcCodes = dcCodes,
            weeks = weeks,
            fromDate = fromDate,
            toDate = toDate
        )

        val slot = slot<List<DateRange>>()
        val expectedResult = listOf(getExpectedTransferOrder())

        coEvery {
            repositoryMock.findTransferOrders(skuId, dcCodes, capture(slot))
        } returns expectedResult

        val result = runBlocking { service.findTransferOrders(request) }

        assertEquals(expectedResult, result)
        coVerify(exactly = 1) {
            repositoryMock.findTransferOrders(skuId, dcCodes, slot.captured)
        }
    }

    @Test
    fun `should return transfer orders by week selection if fromDate and toDate are missing`() {
        val skuId = UUID.randomUUID()
        val dcCode = "VE"
        val dcWeeks = setOf(DcWeek("2025-W30"), DcWeek("2025-W31"))
        val dcZoneId = ZoneId.of("UTC")
        val productionStartDay = DayOfWeek.MONDAY

        val request = TransferOrdersReq(
            skuId = skuId,
            dcCodes = setOf(dcCode),
            weeks = dcWeeks,
            fromDate = null,
            toDate = null
        )

        coEvery { configServiceMock.getByCode(dcCode) } returns
            DistributionCenterConfiguration(
                dcCode,
                productionStartDay,
                DayOfWeek.TUESDAY,
                market = "DACH",
                zoneId = dcZoneId,
                wmsType = WmsSystem.WMS_SYSTEM_FCMS
            )

        val expectedDateRanges = dcWeeks.map {
            DateRange(
                it.getStartDateInDcWeek(productionStartDay, dcZoneId.rules.getOffset(Instant.now())),
                it.getLastDateInDcWeek(productionStartDay, dcZoneId.rules.getOffset(Instant.now()))
            )
        }.toList()

        val slot = slot<List<DateRange>>()

        val expected = listOf(getExpectedTransferOrder())

        coEvery {
            repositoryMock.findTransferOrders(skuId, setOf(dcCode), capture(slot))
        } returns expected

        val result = runBlocking { service.findTransferOrders(request) }

        assertEquals(expectedDateRanges, slot.captured)
        assertEquals(expected, result)
    }

    private fun getExpectedTransferOrder() = TransferOrder(
        transferOrderNumber = "TO-001",
        sourceDc = "BV",
        destinationDc = "VE",
        week = "2025-W27",
        marketCode = "DACH",
        status = TransferOrderStatus.STATE_RESERVED,
        expectedDeliveryTimeslot = null,
        transferOrderSkus = listOf(
            TransferOrderSku(
                toNumber = "TO-001",
                skuId = UUID.randomUUID(),
                supplierId = UUID.randomUUID(),
                supplierName = "Test Supplier",
                expectedQuantity = SkuQuantity.fromLong(10),
                deliveries = listOf(
                    DeliveryInfo(
                        id = "DEL-001",
                        deliveryTime = LocalDateTime.of(2025, 7, 5, 10, 30),
                        state = DeliveryInfoStatus.OPEN,
                        quantity = SkuQuantity.fromLong(5),
                        expiryDate = LocalDate.of(2025, 8, 1)
                    )
                )
            )
        )
    )
}
