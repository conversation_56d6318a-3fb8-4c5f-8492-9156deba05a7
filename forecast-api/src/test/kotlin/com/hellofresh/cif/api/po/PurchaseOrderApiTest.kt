package com.hellofresh.cif.api.po

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.api.calculation.generated.model.PurchaseOrdersDetailResponse
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.user.AuthUtils.addAuthHeader
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.Asn
import com.hellofresh.cif.models.purchaseorder.DeliveryInfo
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus.OPEN
import com.hellofresh.cif.models.purchaseorder.PoStatus.APPROVED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderSku
import com.hellofresh.cif.models.purchaseorder.Supplier
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.http.formUrlEncode
import io.ktor.server.testing.testApplication
import io.mockk.coEvery
import io.mockk.mockk
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZoneOffset.UTC
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.Random
import java.util.UUID
import kotlin.test.Test
import kotlin.time.Duration
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals

class PurchaseOrderApiTest {
    private val poServiceMock: PurchaseOrderService = mockk()
    private val dcConfigService: DcConfigService = mockk()
    private val jwtSecret = "testSecret"
    private val jwksURI = "https://test.com"
    private val authorName = "testAuthor"
    private val authorEmail = "<EMAIL>"

    @Test
    fun `should return OK if request has all required params`() {
        runBlocking {
            testApplication {
                application {
                    configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                    purchaseOrderModule(poServiceMock, dcConfigService, Duration.parse("PT1S")) ()
                }
                client.get(PURCHASE_ORDER_PATH) {
                    this.addAuthHeader(authorEmail, authorName, jwtSecret)
                }.apply {
                    assertEquals(HttpStatusCode.Companion.BadRequest, this.status)
                }
                val skuId = UUID.randomUUID()
                val dcCode1 = "VE"
                val dcCode2 = "DC"
                val dcWeek = "2023-W14"
                val paramsOk = Parameters.build {
                    this.append("skuId", skuId.toString())
                    this.append("dcCode", dcCode1)
                    this.append("dcCode", dcCode2)
                    this.append("weeks", dcWeek)
                }.formUrlEncode()
                val expectedPoList = listOf(
                    PurchaseOrder(
                        "PO1",
                        "PO1",
                        null,
                        dcCode1,
                        null,
                        Supplier(UUID.randomUUID(), "AA"),
                        listOf(
                            PurchaseOrderSku(
                                skuId,
                                deliveries = listOf(randomDelivery()),
                                expectedQuantity = SkuQuantity.fromLong(1L),
                            ),
                            PurchaseOrderSku(
                                UUID.randomUUID(),
                                deliveries = listOf(randomDelivery()),
                                expectedQuantity = SkuQuantity.fromLong(1L),
                            ),
                        ),
                        poStatus = APPROVED,
                        asns = listOf(Asn("ASN000", skuId, ZonedDateTime.now(UTC), SkuQuantity.fromLong(1L))),
                    ),
                )
                coEvery {
                    dcConfigService.dcConfigurations[any()]
                } returns DistributionCenterConfiguration.default().copy(zoneId = ZoneId.of("UTC"))
                coEvery {
                    poServiceMock.findPurchaseOrders(
                        PurchaseOrdersReq(
                            skuId,
                            setOf(dcCode1, dcCode2),
                            setOf(DcWeek(dcWeek)),
                            null,
                            null,
                        ),
                    )
                } returns expectedPoList
                client.get("$PURCHASE_ORDER_PATH?$paramsOk") {
                    this.addAuthHeader(authorEmail, authorName, jwtSecret)
                }.apply {
                    val bodyStr = this.body<String>()
                    val body = jacksonObjectMapper()
                        .findAndRegisterModules()
                        .readValue(bodyStr, PurchaseOrdersDetailResponse::class.java)
                    assertEquals(HttpStatusCode.Companion.OK, this.status)
                    assertEquals(
                        PurchaseOrderResponseMapper.mapToPoDetailResponse(
                            expectedPoList,
                            skuId,
                            dcConfigService
                        ),
                        body
                    )
                }
            }
        }
    }

    @Test
    fun `should return Bad Request if required query params are missing`() {
        runBlocking {
            testApplication {
                application {
                    configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                    purchaseOrderModule(poServiceMock, dcConfigService, Duration.parse("PT1S")) ()
                }
                client.get("$PURCHASE_ORDER_PATH") {
                    this.addAuthHeader(authorEmail, authorName, jwtSecret)
                }.apply {
                    assertEquals(HttpStatusCode.Companion.BadRequest, this.status)
                }
                val paramsMissingSkuId = Parameters.build {
                    this.append("dcCode", "VE")
                    this.append("weeks", "2023-W11")
                }.formUrlEncode()
                client.get("$PURCHASE_ORDER_PATH?$paramsMissingSkuId") {
                    this.addAuthHeader(authorEmail, authorName, jwtSecret)
                }.apply {
                    assertEquals(HttpStatusCode.Companion.BadRequest, this.status)
                }
                val paramsMissingDcCode = Parameters.build {
                    this.append("skuId", UUID.randomUUID().toString())
                    this.append("weeks", "2023-W11")
                }.formUrlEncode()
                client.get("$PURCHASE_ORDER_PATH?$paramsMissingDcCode") {
                    this.addAuthHeader(authorEmail, authorName, jwtSecret)
                }.apply {
                    assertEquals(HttpStatusCode.Companion.BadRequest, this.status)
                }
            }
        }
    }

    @Test
    fun `should return Bad Request if weeks param is missing and not both fromDate and toDate are present`() {
        runBlocking {
            testApplication {
                application {
                    configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                    purchaseOrderModule(poServiceMock, dcConfigService, Duration.parse("PT1S"))()
                }
                client.get(PURCHASE_ORDER_PATH) {
                    this.addAuthHeader(authorEmail, authorName, jwtSecret)
                }.apply {
                    assertEquals(HttpStatusCode.Companion.BadRequest, this.status)
                }
                val paramsMissingToDate = Parameters.build {
                    this.append("skuId", UUID.randomUUID().toString())
                    this.append("dcCode", "VE")
                    this.append("fromDate", LocalDate.now().format(DateTimeFormatter.ISO_DATE))
                }.formUrlEncode()
                client.get("$PURCHASE_ORDER_PATH?$paramsMissingToDate") {
                    this.addAuthHeader(authorEmail, authorName, jwtSecret)
                }.apply {
                    assertEquals(HttpStatusCode.Companion.BadRequest, this.status)
                }
            }
        }
    }

    private fun randomDelivery() = DeliveryInfo(
        id = UUID.randomUUID().toString(),
        deliveryTime = ZonedDateTime.now(UTC),
        state = OPEN,
        quantity = SkuQuantity.fromLong(Random(System.nanoTime()).nextLong()),
        expiryDate = null,

    )
}
