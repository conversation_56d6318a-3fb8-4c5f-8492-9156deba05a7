package com.hellofresh.cif.api.calculation

import com.hellofresh.cif.api.calculation.db.CalculationRecord
import com.hellofresh.cif.api.calculation.db.CalculationRepository
import com.hellofresh.cif.api.calculation.fixtures.DEFAULT_TEST_STRATEGY
import com.hellofresh.cif.api.calculation.stockupdate.CalculationsPendingStockUpdateService
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.Context.MARKET
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.EnableWeeklySQROnStockOverview
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import com.hellofresh.demand.models.ConsumptionDetails
import com.hellofresh.demand.models.DemandType.FUMIGATED
import com.hellofresh.demand.models.DemandType.REGULAR
import com.hellofresh.demand.models.Prekitting
import com.hellofresh.demand.models.Prekittings
import com.hellofresh.demand.models.RecipeBreakdown
import com.hellofresh.demand.models.Substitution
import com.hellofresh.demand.models.Substitutions
import com.hellofresh.inventory.models.UsableInventoryEvaluator
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.unmockkAll
import java.math.BigDecimal.ONE
import java.math.BigDecimal.TEN
import java.math.BigDecimal.ZERO
import java.time.LocalDate
import java.util.UUID
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach

@Suppress("UnnecessaryAbstractClass")
abstract class AbstractCalculationServiceViewsTest {
    val dcConfigService = mockk<DcConfigService>()
    val calculationsPendingStockUpdateServiceMock = mockk<CalculationsPendingStockUpdateService>(relaxed = true)
    val usableInventoryEvaluatorMock = mockk<UsableInventoryEvaluator>(relaxed = true)
    val prodCalculationRepositoryMock: CalculationRepository = mockk()
    val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(emptySet())

    val dcVE = DistributionCenterConfiguration.default("VE")
    val dcBV = dcVE.copy(
        dcCode = "BV",
        market = "GB",
    )

    val dcCodeVE = dcVE.dcCode
    val dcCodeBV = dcBV.dcCode

    @BeforeEach
    fun beforeEach() {
        coEvery { dcConfigService.dcConfigurations } returns mapOf(dcVE.dcCode to dcVE, dcBV.dcCode to dcBV)

        statsigFeatureFlagClient.fixtures = setOf(
            EnableWeeklySQROnStockOverview(
                setOf(ContextData(MARKET, "GB")),
            ),
        )
    }

    @AfterEach
    fun afterEach() {
        unmockkAll()
    }

    fun mockRepository(
        request: CalculationRequest,
        dailyCalculations: List<CalculationRecord>,
        totalPages: Int = 1,
        totalSkuAtRiskCount: Int = 0,
    ) {
        coEvery {
            prodCalculationRepositoryMock.fetchPageableCalculations(request)
        } returns CalculationsPage(
            dailyCalculations, totalPages, totalSkuAtRiskCount,
            dailyCalculations.map { it.cskuId }.distinct().count(),
        )
    }

    fun givenRequest(dcWeek: String, vararg dcCodes: String) = CalculationRequest(
        dcCodes = dcCodes.toList(),
        weeks = listOf(dcWeek),
        pageRequest = Page(1, 1),
        skuCodes = emptyList(),
        skuCategories = emptyList(),
        additionalFilters = emptySet(),
        consumptionDaysAhead = 0,
    )

    fun givenCalculation(
        dcCode: String,
        sku: Sku,
        productionWeek: String,
        productionDay: LocalDate,
        pos: Set<String> = emptySet()
    ) =
        CalculationRecord(
            date = productionDay,
            productionWeek = productionWeek,
            cskuId = sku.skuId,
            code = sku.skuCode,
            name = sku.skuName,
            category = sku.skuCategories,
            coolingType = null,
            packaging = null,
            acceptableCodeLife = 0,
            uom = Uom.UOM_UNIT,
            expectedInbound = ONE,
            expectedInboundPo = pos,
            actualInbound = ONE,
            actualInboundPo = null,
            openingStock = ZERO,
            expired = ZERO,
            present = ZERO,
            demanded = ZERO,
            dailyNeeds = ZERO,
            closingStock = TEN,
            actualConsumption = ZERO,
            dcCode = dcCode,
            skuAtRisk = false,
            safetyStock = null,
            strategy = DEFAULT_TEST_STRATEGY,
            safetyStockNeeds = null,
            storageStock = ZERO,
            stagingStock = ZERO,
            poDueIn = null,
            netNeeds = TEN,
            subbed = null,
            consumptionDetails = ConsumptionDetails(
                recipeBreakdowns = listOf(
                    RecipeBreakdown(
                        UUID.randomUUID().toString(),
                        UUID.randomUUID().toString(),
                        100,
                        REGULAR,
                    ),
                ),
                prekitting = Prekittings(
                    `in` = listOf(Prekitting(10, REGULAR)),
                    out = listOf(Prekitting(5, REGULAR)),
                ),
                substitutions = Substitutions(
                    `in` = listOf(
                        Substitution(
                            UUID.randomUUID().toString(),
                            UUID.randomUUID().toString(),
                            20,
                            FUMIGATED,
                        ),
                    ),
                    out = listOf(
                        Substitution(
                            UUID.randomUUID().toString(),
                            UUID.randomUUID().toString(),
                            10,
                            FUMIGATED,
                        ),
                    ),
                ),
            ),
            stockUpdate = null,
        )

    fun weeklyView(
        dcWeek: String,
        sku: Sku,
        dailyViews: List<DailyView>,
        pos: Set<String> = emptySet()
    ) = WeeklyView(
        dcWeek,
        sku,
        Calculation(
            uom = UOM_UNIT,
            usableStock = ZERO,
            dailyViews.sumOf { v -> v.calculation.unusableStock },
            incomingPos = dailyViews.sumOf { v -> v.calculation.incomingPos },
            inbound = dailyViews.sumOf { v -> v.calculation.inbound },
            pos = pos,
            brandConsumptions = dailyViews.flatMap { v -> v.calculation.brandConsumptions },
            prekitting = dailyViews.map { v -> v.calculation.prekitting }.sumOf { it!! },
            consumption = dailyViews
                .map { v -> v.calculation.consumption }
                .reduce { acc, value ->
                    acc + value
                },
            dailyNeed = dailyViews
                .map { v -> v.calculation.dailyNeed }
                .reduce { acc, value ->
                    acc + value
                },
            closingStock = ZERO,
            actualConsumption = dailyViews
                .map { v -> v.calculation.actualConsumption }
                .reduce { acc, value ->
                    acc + value
                },
            dcCode = dailyViews.first().calculation.dcCode,
            skuAtRisk = false,
            safetyStock = dailyViews.first().calculation.safetyStock,
            strategy = dailyViews.first().calculation.strategy,
            safetyStockNeeds = dailyViews
                .map { v -> v.calculation.safetyStockNeeds }
                .reduce { acc, value ->
                    acc?.plus(value ?: ZERO)
                },
            storageStock = ZERO,
            stagingStock = ZERO,
            poDueIn = null,
            netNeeds = dailyViews
                .map { v -> v.calculation.netNeeds }
                .reduce { acc, value ->
                    acc + value
                },
            substituted = null,
            regularDemand = dailyViews
                .map { v -> v.calculation.regularDemand }
                .sumOf { it!! },
            fumigatedDemand = dailyViews
                .map { v -> v.calculation.fumigatedDemand }
                .sumOf { it!! },
        ),
    )
}
