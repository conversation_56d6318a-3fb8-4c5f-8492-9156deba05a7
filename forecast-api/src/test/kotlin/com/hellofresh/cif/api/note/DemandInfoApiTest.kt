package com.hellofresh.cif.api.note

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.api.calculation.generated.model.Demand
import com.hellofresh.cif.api.calculation.generated.model.DemandPerWeeksResponse
import com.hellofresh.cif.api.calculation.generated.model.DemandsRefreshResponse
import com.hellofresh.cif.api.demand.DcDemandTimestampWeek
import com.hellofresh.cif.api.demand.DemandService
import com.hellofresh.cif.api.demand.MarketDemand
import com.hellofresh.cif.api.demand.MarketDemandByWeek
import com.hellofresh.cif.api.demand.demandByMarket
import com.hellofresh.cif.api.demand.demandInfoModule
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.user.AuthUtils.addAuthHeader
import com.hellofresh.cif.distributionCenter.models.DcWeek
import io.ktor.client.request.get
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.http.formUrlEncode
import io.ktor.server.testing.testApplication
import io.mockk.coEvery
import io.mockk.mockk
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.time.Duration
import kotlinx.coroutines.runBlocking

class DemandInfoApiTest {
    private val demandServiceMock: DemandService = mockk()
    private val jwtSecret = "testSecret"
    private val jwksURI = "https://test.com"
    private val authorName = "testAuthor"
    private val authorEmail = "<EMAIL>"

    @Test
    fun `should return the latest demand timestamps for the weeks requested`() {
        // given
        val dcWeek = DcWeek("2023-W11")
        val dcCodes = listOf("VE", "BX")
        val latestDemandTime = OffsetDateTime.now(ZoneOffset.UTC)
        val givenDemandInfo =
            dcCodes.associateWith {
                listOf(
                    DcDemandTimestampWeek(
                        it,
                        dcWeek,
                        latestDemandTime
                    )
                )
            }
        coEvery { demandServiceMock.getLatestDemandInfo(any(), any()) } returns givenDemandInfo

        runBlocking {
            testApplication {
                application {
                    configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                    demandInfoModule(demandServiceMock, Duration.parse("PT1S"))()
                }
                val params = Parameters.build {
                    dcCodes.forEach { this.append("dcCode", it) }
                    this.append("weeks", "2023-W11")
                }.formUrlEncode()
                client.get("/forecast?$params") {
                    this.addAuthHeader(authorEmail, authorName, jwtSecret)
                }.apply {
                    assertEquals(HttpStatusCode.Companion.OK, this.status)
                    val demandsRefreshResponse =
                        objectMapper.readValue(bodyAsText(), DemandsRefreshResponse::class.java)
                    assertEquals(dcCodes.size, demandsRefreshResponse.demandRefresh.size)
                    assertEquals(dcCodes.toSet(), demandsRefreshResponse.demandRefresh.map { it.dcCode }.toSet())
                    demandsRefreshResponse.demandRefresh.forEach {
                        val demandRefreshForDc = it.lastUpdates.first()
                        assertEquals(latestDemandTime, demandRefreshForDc.lastUpdated)
                        assertEquals(dcWeek.value, demandRefreshForDc.week)
                    }
                }
            }
        }
    }

    @Test
    fun `should return an empty list if DC not found`() {
        coEvery { demandServiceMock.getLatestDemandInfo(any(), any()) } returns emptyMap()

        runBlocking {
            testApplication {
                application {
                    configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                    demandInfoModule(demandServiceMock, Duration.parse("PT1S"))()
                }
                client.get("/forecast?dcCode=XX&weeks=2023-W11") {
                    this.addAuthHeader(authorEmail, authorName, jwtSecret)
                }.apply {
                    assertEquals(HttpStatusCode.Companion.OK, this.status)
                    val demandsRefreshResponse =
                        objectMapper.readValue(bodyAsText(), DemandsRefreshResponse::class.java)
                    assertEquals(0, demandsRefreshResponse.demandRefresh.size)
                }
            }
        }
    }

    @Test
    fun `should return the latest demand timestamps for the requested market and weeks`() {
        // Given
        val market = "DACH"
        val weeks = listOf("2024-W16", "2024-W17")
        val timestamp = LocalDateTime.now()
        val uuid = UUID.randomUUID()

        val expectedServiceResponse = MarketDemandByWeek(
            timestamp,
            listOf(
                MarketDemand(
                    uuid,
                    "VE",
                    LocalDate.now(),
                    100L,
                ),
            ),
        )

        val expectedResponse = DemandPerWeeksResponse(
            listOf(
                Demand(
                    uuid,
                    LocalDate.now(),
                    "VE",
                    100L,
                ),
            ),
            timestamp.atOffset(ZoneOffset.UTC),
        )
        coEvery { demandServiceMock.getDemandByMarket(any(), any(), any()) } returns expectedServiceResponse

        runBlocking {
            testApplication {
                application {
                    configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                    demandByMarket(demandServiceMock, Duration.parse("PT1S")) ()
                }
                val params = Parameters.build {
                    append("market", market)
                    weeks.forEach { append("weeks", it) }
                }.formUrlEncode()

                client.get("/demand/$market?$params") {
                    addAuthHeader(authorEmail, authorName, jwtSecret)
                }.apply {
                    assertEquals(HttpStatusCode.OK, status)
                    val response = objectMapper.readValue(bodyAsText(), DemandPerWeeksResponse::class.java)
                    assertEquals(expectedResponse, response)
                }
            }
        }
    }

    @Test
    fun `should return bad request if weeks param isn't present`() {
        // Given
        val market = "DACH"
        val weeks = emptyList<String>()
        val timestamp = LocalDateTime.now()

        val expectedResponse = MarketDemandByWeek(
            timestamp,
            listOf(
                MarketDemand(
                    UUID.randomUUID(),
                    "VE",
                    LocalDate.now(),
                    100L,
                ),
            ),
        )
        coEvery { demandServiceMock.getDemandByMarket(any(), any(), any()) } returns expectedResponse

        runBlocking {
            testApplication {
                application {
                    configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                    demandByMarket(demandServiceMock, Duration.parse("PT1S")) ()
                }
                val params = Parameters.build {
                    append("market", market)
                    weeks.forEach { append("weeks", it) }
                }.formUrlEncode()

                client.get("/demand/$market?$params") {
                    addAuthHeader(authorEmail, authorName, jwtSecret)
                }
                    .apply {
                        assertEquals(HttpStatusCode.BadRequest, status)
                    }
            }
        }
    }

    companion object {
        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()
    }
}
