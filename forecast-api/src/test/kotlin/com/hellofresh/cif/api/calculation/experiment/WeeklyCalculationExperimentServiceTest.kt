package com.hellofresh.cif.api.calculation.experiment

import com.hellofresh.cif.api.calculation.generated.model.WeeklyCalculationExperimentResponse
import com.hellofresh.cif.api.stockupdate.StockUpdateCalculationService
import com.hellofresh.cif.api.stockupdate.StockUpdateResults
import com.hellofresh.cif.calculator.calculations.rules.TARGET_SAFETY_STOCK_DEFAULT_STRATEGY
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.models.DayCalculationResult
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepository
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.mockk
import java.math.BigDecimal
import java.time.DayOfWeek.MONDAY
import java.time.DayOfWeek.TUESDAY
import java.time.LocalDate
import java.time.ZoneId
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.assertThrows

class WeeklyCalculationExperimentServiceTest {
    private val stockUpdateCalculationService = mockk<StockUpdateCalculationService>(relaxed = true)
    private val dcRepository = mockk<DcRepository>(relaxed = true)
    private val dcConfigService = DcConfigService(SimpleMeterRegistry(), dcRepository)

    private val weeklyCalculationExperimentService =
        CalculationExperimentService(stockUpdateCalculationService, dcConfigService)

    private val productionWeek = "2022-W44"
    private val dcCode = "VE"
    private val today = LocalDate.now()

    @Test
    fun `should be able to get weekly calculation experiments results`() {
        // Given
        val skuId = UUID.randomUUID()
        val dayCalculationResults = createDayCalculationResult(skuId, productionWeek)
        val weeklyCalculationExperimentData = WeeklyCalculationExperimentData(
            dcCode,
            skuId,
            emptySet(),
            CalculatorMode.PRODUCTION,
            mapOf(DcWeek(productionWeek) to BigDecimal(100)),
        )
        val dc = DistributionCenterConfiguration(
            dcCode,
            MONDAY,
            TUESDAY,
            "dach",
            ZoneId.of("UTC"),
            true,
            true,
            wmsType = WmsSystem.WMS_SYSTEM_WMS_LITE,
        )
        coEvery { dcRepository.fetchDcConfigurations() } returns listOf(dc)

        val dayCalculationResultList = listOf(dayCalculationResults)

        coEvery {
            stockUpdateCalculationService.runStockUpdatesWithoutUom(
                dcCode, skuId, weeklyCalculationExperimentData.weeks, CalculatorMode.PRODUCTION,
                weeklyCalculationExperimentData.calculationExperiments(dc),
            )
        } returns StockUpdateResults(
            dayCalculationResultList,
            weeklyCalculationExperimentData.calculationExperiments(dc).mapValues { (_, value) -> SkuQuantity.fromBigDecimal(value, UOM_UNIT) },
            emptySet(),
            emptyMap(),
        )

        // When
        val weeklyCalculationExperimentResponses = runBlocking {
            weeklyCalculationExperimentService.getWeeklyCalculationExperiment(
                weeklyCalculationExperimentData,
            )
        }

        assertEquals(1, weeklyCalculationExperimentResponses.size)
        assertEquals(100, weeklyCalculationExperimentResponses[0].experiment)
        assertWeeklyCalculationExperimentResponses(weeklyCalculationExperimentResponses)
    }

    @Test
    fun `should be able to get weekly calculation experiments results without EXPERIMENT input`() {
        // Given
        val skuId = UUID.randomUUID()
        val productionWeek = "2022-W44"
        val dayCalculationResults = createDayCalculationResult(skuId, productionWeek)
        val weeklyCalculationExperimentData = WeeklyCalculationExperimentData(
            dcCode,
            skuId,
            emptySet(),
            CalculatorMode.PRODUCTION,
            emptyMap(),
        )
        val dc = DistributionCenterConfiguration(
            dcCode,
            MONDAY,
            TUESDAY,
            "dach",
            ZoneId.of("UTC"),
            true,
            true,
            wmsType = WmsSystem.WMS_SYSTEM_HIGH_JUMP,
        )
        coEvery { dcRepository.fetchDcConfigurations() } returns listOf(dc)

        val dayCalculationResultList = listOf(dayCalculationResults)

        coEvery {
            stockUpdateCalculationService.runStockUpdatesWithoutUom(
                dcCode, skuId, weeklyCalculationExperimentData.weeks, CalculatorMode.PRODUCTION,
                weeklyCalculationExperimentData.calculationExperiments(dc),
            )
        } returns StockUpdateResults(
            dayCalculationResultList,
            weeklyCalculationExperimentData.calculationExperiments(dc).mapValues { (_, value) -> SkuQuantity.fromBigDecimal(value, UOM_UNIT) },
            emptySet(),
            emptyMap(),
        )

        // When
        val weeklyCalculationExperimentResults = runBlocking {
            weeklyCalculationExperimentService.getWeeklyCalculationExperiment(
                weeklyCalculationExperimentData,
            )
        }

        assertEquals(1, weeklyCalculationExperimentResults.size)
        assertNull(weeklyCalculationExperimentResults[0].experiment)
        assertWeeklyCalculationExperimentResponses(weeklyCalculationExperimentResults)
    }

    @Test
    fun `should be able to get weekly calculation experiments results considering MULTIPLE DAYS`() {
        // Given
        val skuId = UUID.randomUUID()
        val productionWeek = "2022-W44"
        val dayCalculationResultDayOne = createDayCalculationResult(skuId, productionWeek)
        val dayCalculationResultDayTwo = createDayCalculationResult(skuId, productionWeek, today.plusDays(1))
        val dayCalculationResultDayThree = createDayCalculationResult(skuId, productionWeek, today.plusDays(2))
        val weeklyCalculationExperimentData = WeeklyCalculationExperimentData(
            dcCode,
            skuId,
            emptySet(),
            CalculatorMode.PRODUCTION,
            mapOf(DcWeek(productionWeek) to BigDecimal(100)),
        )

        val dc = DistributionCenterConfiguration(
            dcCode,
            MONDAY,
            TUESDAY,
            "dach",
            ZoneId.of("UTC"),
            true,
            true,
            wmsType = WmsSystem.WMS_SYSTEM_FCMS,
        )
        coEvery { dcRepository.fetchDcConfigurations() } returns listOf(dc)

        val dayCalculationResultList = listOf(
            dayCalculationResultDayOne,
            dayCalculationResultDayTwo,
            dayCalculationResultDayThree,
        )
        coEvery {
            stockUpdateCalculationService.runStockUpdatesWithoutUom(
                dcCode, skuId, weeklyCalculationExperimentData.weeks, CalculatorMode.PRODUCTION,
                weeklyCalculationExperimentData.calculationExperiments(dc),
            )
        } returns StockUpdateResults(
            dayCalculationResultList,
            weeklyCalculationExperimentData.calculationExperiments(dc).mapValues { (_, value) -> SkuQuantity.fromBigDecimal(value, UOM_UNIT) },
            emptySet(),
            emptyMap(),
        )

        // When
        val weeklyCalculationExperimentResults = runBlocking {
            weeklyCalculationExperimentService.getWeeklyCalculationExperiment(
                weeklyCalculationExperimentData,
            )
        }

        assertEquals(1, weeklyCalculationExperimentResults.size)
        assertEquals(100, weeklyCalculationExperimentResults[0].experiment)
        weeklyCalculationExperimentResults[0].apply {
            assertEquals(productionWeek, week)
            assertEquals(15, incoming)
            assertEquals(12, inbound)
            assertEquals(3, unusable)
            assertEquals(7, closing)
            assertEquals(2, opening)
            assertEquals(18, consumption)
            assertEquals(30, actualConsumption)
        }
    }

    @Test
    fun `should be able to get weekly calculation experiments results considering MULTIPLE WEEKS`() {
        // Given
        val skuId = UUID.randomUUID()
        val productionWeek44 = "2022-W44"
        val productionWeek45 = "2022-W45"
        val productionWeek46 = "2022-W46"
        val dayCalculationResultDayOne = createDayCalculationResult(skuId, productionWeek44)
        val dayCalculationResultDayTwo = createDayCalculationResult(skuId, productionWeek45, today.plusDays(7))
        val dayCalculationResultDayThree = createDayCalculationResult(skuId, productionWeek46, today.plusDays(14))
        val dayCalculationResultDayFour = createDayCalculationResult(skuId, productionWeek46, today.plusDays(15))
        val weeklyCalculationExperimentData = WeeklyCalculationExperimentData(
            dcCode,
            skuId,
            emptySet(),
            CalculatorMode.PRODUCTION,
            mapOf(
                DcWeek(productionWeek44) to BigDecimal(1000),
                DcWeek(productionWeek45) to BigDecimal(1500),
                DcWeek(productionWeek46) to BigDecimal(2000),
            ),
        )

        val dayCalculationResultList = listOf(
            dayCalculationResultDayOne,
            dayCalculationResultDayTwo,
            dayCalculationResultDayThree,
            dayCalculationResultDayFour,
        )
        val dc = DistributionCenterConfiguration(
            dcCode,
            MONDAY,
            TUESDAY,
            "dach",
            ZoneId.of("UTC"),
            true,
            true,
            wmsType = WmsSystem.WMS_SYSTEM_FCMS,
        )
        coEvery { dcRepository.fetchDcConfigurations() } returns listOf(dc)

        coEvery {
            stockUpdateCalculationService.runStockUpdatesWithoutUom(
                dcCode, skuId, weeklyCalculationExperimentData.weeks, CalculatorMode.PRODUCTION,
                weeklyCalculationExperimentData.calculationExperiments(dc),
            )
        } returns StockUpdateResults(
            dayCalculationResultList,
            weeklyCalculationExperimentData.calculationExperiments(dc).mapValues { (_, value) -> SkuQuantity.fromBigDecimal(value, UOM_UNIT) },
            emptySet(),
            emptyMap(),
        )

        // When
        val weeklyCalculationExperimentResults = runBlocking {
            weeklyCalculationExperimentService.getWeeklyCalculationExperiment(
                weeklyCalculationExperimentData,
            )
        }

        assertEquals(3, weeklyCalculationExperimentResults.size)
        assertEquals(1000, weeklyCalculationExperimentResults[0].experiment)
        assertWeeklyCalculationExperimentResponses(weeklyCalculationExperimentResults)

        weeklyCalculationExperimentResults[2].apply {
            assertEquals(productionWeek46, week)
            assertEquals(2000, experiment)
            assertEquals(10, incoming)
            assertEquals(8, inbound)
            assertEquals(2, unusable)
            assertEquals(7, closing)
            assertEquals(2, opening)
            assertEquals(12, consumption)
            assertEquals(20, actualConsumption)
        }
    }

    @Test
    fun `should throw an exception when there is an empty daily calculation results`() {
        // Given
        val skuId = UUID.randomUUID()
        val weeklyCalculationExperimentData = WeeklyCalculationExperimentData(
            dcCode,
            skuId,
            emptySet(),
            CalculatorMode.PRODUCTION,
            mapOf(DcWeek("2022-W44") to BigDecimal(100)),
        )

        val dc = DistributionCenterConfiguration(
            dcCode,
            MONDAY,
            TUESDAY,
            "dach",
            ZoneId.of("UTC"),
            true,
            true,
            wmsType = WmsSystem.WMS_SYSTEM_HIGH_JUMP,
        )
        coEvery { dcRepository.fetchDcConfigurations() } returns listOf(dc)

        coEvery {
            stockUpdateCalculationService.runStockUpdatesWithoutUom(
                dcCode, skuId, weeklyCalculationExperimentData.weeks, CalculatorMode.PRODUCTION,
                weeklyCalculationExperimentData.calculationExperiments(dc),
            )
        } returns StockUpdateResults(
            emptyList(),
            weeklyCalculationExperimentData.calculationExperiments(dc).mapValues { (_, value) -> SkuQuantity.fromBigDecimal(value, UOM_UNIT) },
            emptySet(),
            emptyMap(),
        )

        // When
        runBlocking {
            assertThrows<IllegalArgumentException> {
                weeklyCalculationExperimentService.getWeeklyCalculationExperiment(
                    weeklyCalculationExperimentData,
                )
            }
        }
    }

    private fun createDayCalculationResult(skuId: UUID, productionWeek: String, date: LocalDate = today) =
        DayCalculationResult(
            UOM_UNIT,
            skuId, "VE", date, SkuQuantity.fromLong(1),
            SkuQuantity.fromLong(
                2
            ),
            SkuQuantity.fromLong(3), SkuQuantity.fromLong(4), emptySet(), SkuQuantity.fromLong(5), emptySet(),
            SkuQuantity.fromLong(
                6
            ),
            SkuQuantity.fromLong(
                7
            ),
            SkuQuantity.fromLong(
                8
            ),
            SkuQuantity.fromLong(9), productionWeek, SkuQuantity.fromLong(10), netNeeds = SkuQuantity.fromLong(10),
            strategy = TARGET_SAFETY_STOCK_DEFAULT_STRATEGY,
        )

    private fun assertWeeklyCalculationExperimentResponses(
        weeklyCalculationExperimentResponses: List<WeeklyCalculationExperimentResponse>
    ) {
        weeklyCalculationExperimentResponses[0].apply {
            assertEquals(productionWeek, week)
            assertEquals(5, incoming)
            assertEquals(4, inbound)
            assertEquals(1, unusable)
            assertEquals(7, closing)
            assertEquals(2, opening)
            assertEquals(6, consumption)
            assertEquals(10, actualConsumption)
        }
    }
}
