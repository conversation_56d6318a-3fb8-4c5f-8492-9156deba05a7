package com.hellofresh.cif.api.calculation.experiment

import com.hellofresh.cif.calculator.calculations.rules.TARGET_SAFETY_STOCK_DEFAULT_STRATEGY
import com.hellofresh.cif.calculator.models.DayCalculationResult
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test

internal class CalculationExperimentResultWeekFilterTest {

    @Test
    fun `should include only the requested weeks from calculation results`() {
        // given
        val dcWeekRequested = DcWeek(LocalDate.now(), MONDAY).value
        val dcWeekNonRequested = DcWeek(LocalDate.now().plusWeeks(1), MONDAY).value
        val dayCalculationResultWithRequestedWeek = dayCalculationResultWithWeek(dcWeekRequested)
        val dayCalculationResultWithNonRequestedWeek = dayCalculationResultWithWeek(dcWeekNonRequested)

        // when
        val dayCalculationResults = CalculationExperimentResultWeekFilter.filter(
            listOf(dayCalculationResultWithRequestedWeek, dayCalculationResultWithNonRequestedWeek),
            setOf(dcWeekRequested),
        )

        // Then
        assertEquals(dayCalculationResults, listOf(dayCalculationResultWithRequestedWeek))
    }

    @Test
    fun `should include all calculation results when the weeks param is not present in the request`() {
        // given
        val dayCalculationResultWithRequestedWeek = dayCalculationResultWithWeek(DcWeek(LocalDate.now(), MONDAY).value)
        val dayCalculationResultWithNonRequestedWeek =
            dayCalculationResultWithWeek(DcWeek(LocalDate.now().plusWeeks(2), MONDAY).value)
        val calculationExperimentResults =
            listOf(dayCalculationResultWithRequestedWeek, dayCalculationResultWithNonRequestedWeek)

        // when
        val dayCalculationResults = CalculationExperimentResultWeekFilter.filter(
            calculationExperimentResults,
            emptySet(),
        )

        // Then
        assertEquals(dayCalculationResults, calculationExperimentResults)
    }

    private fun dayCalculationResultWithWeek(week: String) = DayCalculationResult(
        UOM_UNIT,
        UUID.randomUUID(), "VE", LocalDate.now(), SkuQuantity.fromLong(1),
        SkuQuantity.fromLong(
            2
        ),
        SkuQuantity.fromLong(3), SkuQuantity.fromLong(4), emptySet(), SkuQuantity.fromLong(5), emptySet(),
        SkuQuantity.fromLong(
            6
        ),
        SkuQuantity.fromLong(
            7
        ),
        SkuQuantity.fromLong(
            8
        ),
        SkuQuantity.fromLong(9), week, SkuQuantity.fromLong(10), netNeeds = SkuQuantity.fromLong(10),
        strategy = TARGET_SAFETY_STOCK_DEFAULT_STRATEGY,
    )
}
