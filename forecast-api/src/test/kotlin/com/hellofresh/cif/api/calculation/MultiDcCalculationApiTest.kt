package com.hellofresh.cif.api.calculation

import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.cif.api.calculation.fixtures.DEFAULT_TEST_STRATEGY
import com.hellofresh.cif.api.calculation.generated.model.DailyCalculationsResponse
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.user.AuthUtils.addAuthHeader
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import io.ktor.client.request.accept
import io.ktor.client.request.get
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.http.formUrlEncode
import io.ktor.server.testing.testApplication
import io.mockk.coEvery
import io.mockk.mockk
import java.math.BigDecimal
import java.math.BigDecimal.ONE
import java.math.BigDecimal.TEN
import java.math.BigDecimal.TWO
import java.math.BigDecimal.ZERO
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.time.Duration

class MultiDcCalculationApiTest {
    private val calculationsServiceMock = mockk<CalculationsService>(relaxed = true)
    private val jwtSecret = "testSecret"

    private val calculationResponseMapper = CalculationResponseMapper(
        StatsigTestFeatureFlagClient(emptySet()),
    )

    @Test
    fun `should return daily views for all requested DCs`() {
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", "https://test.com"), true)
                calculationRoutingModule(
                    calculationsServiceMock,
                    calculationResponseMapper,
                    Duration.parse("PT5S"),
                )()
            }
            val dcCodes = listOf("VE", "BX")
            val calcParams = Parameters.build {
                dcCodes.forEach { this.append("dcCode", it) }
                this.append("weeks", "2022-W01")
            }.formUrlEncode()
            val dailyViews = dcCodes.map {
                DailyView(
                    ProductionDay("2022-W01", LocalDate.now()),
                    Sku(UUID.randomUUID(), "code1", "skuName", "categories"),
                    Calculation(
                        uom = UOM_UNIT,
                        ONE, TWO, ZERO, ZERO, emptySet(),
                        BigDecimal(
                            5
                        ),
                        BigDecimal(6), ZERO, BigDecimal(100), it, false, BigDecimal(7),
                        DEFAULT_TEST_STRATEGY, BigDecimal(8),
                        BigDecimal(9), TEN, null, TEN, 0,
                    ),
                )
            }
            coEvery { calculationsServiceMock.getDailyCalculations(any()) } returns CalculationsPage(
                dailyViews,
                1,
                0,
                dailyViews.map { it.sku.skuCode }.distinct().count(),
            )

            // when
            val response = client.get("/calculation/dailyView?$calcParams") {
                accept(ContentType.Application.Json)
                this.addAuthHeader(UUID.randomUUID().toString(), UUID.randomUUID().toString(), jwtSecret)
            }

            // then
            assertEquals(HttpStatusCode.OK, response.status)
            val result = objectMapper.readValue(response.bodyAsText(), DailyCalculationsResponse::class.java)
            assertEquals(2, result.calculations.size)
            assertEquals(
                dailyViews.map { calculationResponseMapper.toDailyCalculationResponse(it) },
                result.calculations,
            )
        }
    }

    companion object {
        private val objectMapper: ObjectMapper = ObjectMapper().findAndRegisterModules()
    }
}
