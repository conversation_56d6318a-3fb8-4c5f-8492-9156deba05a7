package com.hellofresh.cif.api.calculation.experiment

import com.hellofresh.cif.api.stockupdate.StockUpdateCalculationService
import com.hellofresh.cif.api.stockupdate.StockUpdateResults
import com.hellofresh.cif.calculator.calculations.rules.TARGET_SAFETY_STOCK_DEFAULT_STRATEGY
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.models.DayCalculationResult
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepository
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.mockk
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking

class DailyCalculationExperimentServiceTest {

    private val stockUpdateCalculationService = mockk<StockUpdateCalculationService>(relaxed = true)
    private val dcRepository = mockk<DcRepository>(relaxed = true)
    private val dcConfigService = DcConfigService(SimpleMeterRegistry(), dcRepository)

    private val dailyCalculationExperimentService = CalculationExperimentService(
        stockUpdateCalculationService,
        dcConfigService,
    )

    private val dcCode = "VE"
    private val today = LocalDate.now()

    @Test
    fun `should be able to get calculation experiments results`() {
        // Given
        val skuId = UUID.randomUUID()
        val productionWeek = "2022-W53"
        val dayCalculationResults = DayCalculationResult(
            UOM_UNIT,
            UUID.randomUUID(), "VE", today, SkuQuantity.fromLong(1),
            SkuQuantity.fromLong(
                2
            ),
            SkuQuantity.fromLong(3), SkuQuantity.fromLong(4), emptySet(), SkuQuantity.fromLong(5), emptySet(),
            SkuQuantity.fromLong(
                6
            ),
            SkuQuantity.fromLong(
                7
            ),
            SkuQuantity.fromLong(
                8
            ),
            SkuQuantity.fromLong(9), productionWeek, SkuQuantity.fromLong(10), netNeeds = SkuQuantity.fromLong(10),
            strategy = TARGET_SAFETY_STOCK_DEFAULT_STRATEGY,
        )

        val dailyCalculationExperimentData = DailyCalculationExperimentData(
            dcCode,
            skuId,
            setOf(productionWeek),
            CalculatorMode.PRODUCTION,
            mapOf(today to BigDecimal(100)),
        )
        val dayCalculationResultList = listOf(dayCalculationResults)

        coEvery {
            stockUpdateCalculationService.runStockUpdatesWithoutUom(
                dcCode, skuId, dailyCalculationExperimentData.weeks, CalculatorMode.PRODUCTION,
                dailyCalculationExperimentData.calculationExperiments(),
            )
        } returns StockUpdateResults(
            dayCalculationResultList,
            dailyCalculationExperimentData.calculationExperiments().mapValues { (_, value) -> SkuQuantity.fromBigDecimal(value, UOM_UNIT) },
            emptySet(),
            emptyMap(),
        )

        // When
        val calculationExperimentResults = runBlocking {
            dailyCalculationExperimentService.getCalculationExperiment(
                dailyCalculationExperimentData,
            )
        }

        // Then
        assertEquals(dayCalculationResultList, calculationExperimentResults.calculations)
    }
}
