# Ingredient Depletion API - End-to-End Tests

This directory contains comprehensive end-to-end tests for the Ingredient Depletion module in the forecast API.

## Test Files Overview

### 1. IngredientDepletionApiFunctionalTest.kt
**Purpose**: Full HTTP API testing with authentication and request/response validation

**Test Coverage**:
- ✅ Valid requests with specific DC codes
- ✅ Valid requests without DC codes (brand filtering)
- ✅ Missing brand parameter validation
- ✅ Empty result handling
- ✅ Authentication validation (401 responses)
- ✅ Multiple SKUs from same DC
- ✅ Different UOM types handling
- ✅ Whitespace handling in parameters
- ✅ Empty brand parameter handling
- ✅ Duplicate DC codes handling
- ✅ Timeout scenarios

### 2. IngredientDepletionIntegrationTest.kt
**Purpose**: Integration testing focusing on service and repository layer interactions

**Test Coverage**:
- ✅ Database join validation (CALCULATION ⋈ SKU_SPECIFICATION_VIEW)
- ✅ Brand-based DC filtering logic
- ✅ DC code override behavior
- ✅ Empty DC codes handling
- ✅ Large dataset efficiency
- ✅ UOM mapping validation
- ✅ Concurrent request handling

### 3. IngredientDepletionPerformanceTest.kt
**Purpose**: Performance and load testing to ensure scalability

**Test Coverage**:
- ✅ Large dataset handling (1000+ SKUs)
- ✅ Concurrent request processing (20+ simultaneous requests)
- ✅ Memory efficiency with large result sets (5000+ SKUs)
- ✅ DC filtering performance with multiple DCs
- ✅ Database connection pooling under load

### 4. IngredientDepletionBusinessLogicTest.kt
**Purpose**: Business rule validation and edge case testing

**Test Coverage**:
- ✅ Join requirement validation (only SKUs in calculation table)
- ✅ Brand filtering logic validation
- ✅ DC code override behavior
- ✅ Case-sensitive brand handling
- ✅ Parameter validation
- ✅ Special characters in DC codes
- ✅ Data consistency across requests
- ✅ Required field validation

## Key Business Rules Tested

### 1. Database Join Requirement
The API must only return SKUs that exist in both the `CALCULATION` table and `SKU_SPECIFICATION_VIEW`. This is validated through:
- Tests that insert SKUs without calculations
- Verification that only joined records are returned

### 2. Brand-Based DC Filtering
When no DC codes are provided, the API filters DCs based on brand support:
- `hellofresh`: Supported by GB01, DE01, US01
- `greenchef`: Supported by GB01, US01, FR01  
- `everyplate`: Supported by US01 only

### 3. DC Code Override
When DC codes are explicitly provided, they override brand filtering:
- API uses provided DC codes regardless of brand support
- Allows querying unsupported brand/DC combinations

### 4. Authentication
All endpoints require valid JWT authentication:
- 401 responses for missing/invalid tokens
- Proper JWT claims validation

## Test Data Setup

Each test creates isolated test data including:
- **SKU Specifications**: Test ingredients with various categories and UOMs
- **Calculations**: Forecast calculations linking SKUs to DCs
- **DC Configurations**: Distribution center setups with brand support

## Running the Tests

### Unit Tests (No Database Required)
```bash
./gradlew :forecast-api:test --tests "*IngredientDepletion*"
```

### Functional Tests (Database Required)
```bash
# Start test database first
docker-compose up -d inventory-postgres

# Run functional tests
./gradlew :forecast-api:testFunctional --tests "*IngredientDepletion*"
```

### Performance Tests
```bash
# Run only performance tests
./gradlew :forecast-api:testFunctional --tests "*IngredientDepletionPerformanceTest*"
```

## Test Assertions

### Response Structure Validation
- Correct JSON structure matching OpenAPI specification
- All required fields present (site, skuCode, skuName, category, uom, brand)
- Proper data types and formats

### Business Logic Validation
- Brand parameter requirement enforcement
- DC filtering logic correctness
- UOM mapping accuracy
- Data consistency across requests

### Performance Validation
- Response times under acceptable thresholds
- Memory usage within limits
- Concurrent request handling
- Database connection efficiency

## Dependencies

The tests use the existing functional test infrastructure:
- **FunctionalTest**: Base class with database setup and utilities
- **AuthUtils**: JWT token generation for authentication
- **TestContainers**: Database setup for integration tests
- **MockK**: Mocking framework for unit tests
- **Ktor Test**: HTTP client testing utilities

## Future Enhancements

Potential areas for additional test coverage:
- Error response format validation
- Rate limiting behavior
- Caching behavior (if implemented)
- Audit logging validation
- Cross-region DC testing
- Multi-tenant scenarios
