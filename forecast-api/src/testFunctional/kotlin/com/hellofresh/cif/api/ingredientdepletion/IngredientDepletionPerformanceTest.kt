package com.hellofresh.cif.api.ingredientdepletion

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.KotlinFeature
import com.fasterxml.jackson.module.kotlin.KotlinModule
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.api.AuthUtils.addAuthHeader
import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.calculation.generated.model.IngredientDepletionResponse
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.api.schema.tables.records.CalculationRecord
import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import io.ktor.client.request.get
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpStatusCode
import io.ktor.server.testing.testApplication
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlin.time.Duration
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking

/**
 * Performance tests for the Ingredient Depletion API to ensure it can handle
 * realistic loads and data volumes efficiently.
 */
class IngredientDepletionPerformanceTest : FunctionalTest() {

    private val jwtSecret = "testSecret"
    private val jwksURI = "https://test.com"
    private val authorName = "testAuthor"
    private val authorEmail = "<EMAIL>"
    private val timeOutInMillis = Duration.parse("PT30S") // Longer timeout for performance tests

    private val objectMapper = ObjectMapper()
        .registerModule(KotlinModule.Builder().configure(KotlinFeature.NullIsSameAsDefault, true).build())
        .findAndRegisterModules()

    @Test
    fun `should handle large dataset efficiently`() {
        // Given
        val brand = "hellofresh"
        val dcCodes = listOf("GB01", "DE01", "US01", "FR01", "AU01")
        val numberOfSkus = 1000 // Large dataset
        
        setupLargeDataset(dcCodes, numberOfSkus)

        runBlocking {
            // When
            val startTime = System.currentTimeMillis()
            val response = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=$brand")
            val endTime = System.currentTimeMillis()

            // Then
            assertEquals(HttpStatusCode.OK, response.status)
            val body = response.bodyAsText()
            val result = objectMapper.readValue<IngredientDepletionResponse>(body)

            val responseTime = endTime - startTime
            println("Response time for $numberOfSkus SKUs across ${dcCodes.size} DCs: ${responseTime}ms")

            // Performance assertions
            assertTrue(responseTime < 10000, "Response should complete within 10 seconds, took ${responseTime}ms")
            assertEquals(numberOfSkus * dcCodes.size, result.ingredientSummary.size)
            
            // Verify data integrity
            val uniqueSkuCodes = result.ingredientSummary.map { it.skuCode }.toSet()
            assertEquals(numberOfSkus, uniqueSkuCodes.size)
        }
    }

    @Test
    fun `should handle concurrent requests efficiently`() {
        // Given
        val brand = "hellofresh"
        val dcCodes = listOf("GB01", "DE01")
        val numberOfSkus = 100
        val concurrentRequests = 20

        setupLargeDataset(dcCodes, numberOfSkus)

        runBlocking {
            // When
            val startTime = System.currentTimeMillis()
            
            val responses = (1..concurrentRequests).map { requestId ->
                async {
                    val requestStart = System.currentTimeMillis()
                    val response = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=$brand&dcCodes=${dcCodes.joinToString(",")}")
                    val requestEnd = System.currentTimeMillis()
                    
                    Triple(requestId, response, requestEnd - requestStart)
                }
            }.awaitAll()
            
            val totalTime = System.currentTimeMillis() - startTime
            
            // Then
            println("Completed $concurrentRequests concurrent requests in ${totalTime}ms")
            
            responses.forEach { (requestId, response, requestTime) ->
                assertEquals(HttpStatusCode.OK, response.status, "Request $requestId should succeed")
                assertTrue(requestTime < 5000, "Request $requestId should complete within 5 seconds, took ${requestTime}ms")
                
                val body = response.bodyAsText()
                val result = objectMapper.readValue<IngredientDepletionResponse>(body)
                assertEquals(numberOfSkus * dcCodes.size, result.ingredientSummary.size, "Request $requestId should return correct number of results")
            }
            
            val averageResponseTime = responses.map { it.third }.average()
            println("Average response time: ${averageResponseTime}ms")
            assertTrue(averageResponseTime < 3000, "Average response time should be under 3 seconds")
        }
    }

    @Test
    fun `should handle memory efficiently with large result sets`() {
        // Given
        val brand = "hellofresh"
        val dcCode = "GB01"
        val numberOfSkus = 5000 // Very large dataset
        
        createDcConfig(dcCode, "GB") { brands = arrayOf("hellofresh") }
        
        // Setup large dataset
        val skuIds = (1..numberOfSkus).map { UUID.randomUUID() }
        val skuRecords = skuIds.mapIndexed { index, skuId ->
            createSkuRecord(
                skuId,
                "SKU${String.format("%05d", index + 1)}",
                "Test SKU ${index + 1}",
                "Category${index % 10}",
                Uom.values()[index % Uom.values().size]
            )
        }
        
        // Insert in batches to avoid memory issues
        val batchSize = 500
        skuRecords.chunked(batchSize).forEach { batch ->
            insertSkuRecords(batch)
        }
        
        val calculationRecords = skuIds.map { skuId ->
            createCalculationRecord(dcCode, skuId)
        }
        
        calculationRecords.chunked(batchSize).forEach { batch ->
            dsl.batchInsert(batch).execute()
        }

        runBlocking {
            // When
            val runtime = Runtime.getRuntime()
            val memoryBefore = runtime.totalMemory() - runtime.freeMemory()
            
            val startTime = System.currentTimeMillis()
            val response = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=$brand&dcCodes=$dcCode")
            val endTime = System.currentTimeMillis()
            
            val memoryAfter = runtime.totalMemory() - runtime.freeMemory()
            val memoryUsed = memoryAfter - memoryBefore

            // Then
            assertEquals(HttpStatusCode.OK, response.status)
            val body = response.bodyAsText()
            val result = objectMapper.readValue<IngredientDepletionResponse>(body)

            val responseTime = endTime - startTime
            println("Response time for $numberOfSkus SKUs: ${responseTime}ms")
            println("Memory used: ${memoryUsed / 1024 / 1024}MB")

            assertEquals(numberOfSkus, result.ingredientSummary.size)
            assertTrue(responseTime < 15000, "Should complete within 15 seconds for large dataset")
            assertTrue(memoryUsed < 500 * 1024 * 1024, "Should use less than 500MB of memory") // 500MB limit
        }
    }

    @Test
    fun `should handle filtering performance with multiple DC codes`() {
        // Given
        val brand = "hellofresh"
        val allDcCodes = listOf("GB01", "DE01", "US01", "FR01", "AU01", "CA01", "NL01", "BE01", "IT01", "ES01")
        val numberOfSkus = 500
        
        setupLargeDataset(allDcCodes, numberOfSkus)

        runBlocking {
            // Test different DC code combinations
            val testCases = listOf(
                listOf("GB01"), // Single DC
                listOf("GB01", "DE01"), // Two DCs
                listOf("GB01", "DE01", "US01", "FR01", "AU01"), // Five DCs
                allDcCodes // All DCs
            )

            testCases.forEach { dcCodes ->
                val startTime = System.currentTimeMillis()
                val response = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=$brand&dcCodes=${dcCodes.joinToString(",")}")
                val endTime = System.currentTimeMillis()

                assertEquals(HttpStatusCode.OK, response.status)
                val body = response.bodyAsText()
                val result = objectMapper.readValue<IngredientDepletionResponse>(body)

                val responseTime = endTime - startTime
                val expectedResults = numberOfSkus * dcCodes.size
                
                println("${dcCodes.size} DC(s): ${responseTime}ms for $expectedResults results")
                
                assertEquals(expectedResults, result.ingredientSummary.size)
                assertTrue(responseTime < 5000, "Should complete within 5 seconds for ${dcCodes.size} DCs")
                
                // Verify only requested DCs are returned
                val returnedDcs = result.ingredientSummary.map { it.site }.toSet()
                assertEquals(dcCodes.toSet(), returnedDcs)
            }
        }
    }

    @Test
    fun `should handle database connection pooling under load`() {
        // Given
        val brand = "hellofresh"
        val dcCode = "GB01"
        val numberOfSkus = 50
        val numberOfConcurrentRequests = 50 // High concurrency to test connection pooling
        
        setupLargeDataset(listOf(dcCode), numberOfSkus)

        runBlocking {
            // When
            val startTime = System.currentTimeMillis()
            
            val responses = (1..numberOfConcurrentRequests).map { requestId ->
                async {
                    try {
                        val response = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=$brand&dcCodes=$dcCode")
                        Pair(requestId, response)
                    } catch (e: Exception) {
                        println("Request $requestId failed: ${e.message}")
                        throw e
                    }
                }
            }.awaitAll()
            
            val totalTime = System.currentTimeMillis() - startTime

            // Then
            println("Completed $numberOfConcurrentRequests requests in ${totalTime}ms")
            
            responses.forEach { (requestId, response) ->
                assertEquals(HttpStatusCode.OK, response.status, "Request $requestId should succeed")
                
                val body = response.bodyAsText()
                val result = objectMapper.readValue<IngredientDepletionResponse>(body)
                assertEquals(numberOfSkus, result.ingredientSummary.size, "Request $requestId should return correct results")
            }
            
            // All requests should complete without connection pool exhaustion
            assertTrue(totalTime < 30000, "All requests should complete within 30 seconds")
        }
    }

    private fun setupLargeDataset(dcCodes: List<String>, numberOfSkus: Int) {
        // Setup DC configurations
        dcCodes.forEach { dcCode ->
            createDcConfig(dcCode, "TEST") { brands = arrayOf("hellofresh", "greenchef") }
        }

        // Create SKUs
        val skuIds = (1..numberOfSkus).map { UUID.randomUUID() }
        val skuRecords = skuIds.mapIndexed { index, skuId ->
            createSkuRecord(
                skuId,
                "SKU${String.format("%05d", index + 1)}",
                "Test SKU ${index + 1}",
                "Category${index % 20}", // 20 different categories
                Uom.values()[index % Uom.values().size]
            )
        }
        insertSkuRecords(skuRecords)

        // Create calculations for each SKU in each DC
        val calculationRecords = mutableListOf<CalculationRecord>()
        dcCodes.forEach { dcCode ->
            skuIds.forEach { skuId ->
                calculationRecords.add(createCalculationRecord(dcCode, skuId))
            }
        }
        dsl.batchInsert(calculationRecords).execute()
    }

    private fun createSkuRecord(
        id: UUID,
        code: String,
        name: String,
        category: String,
        uom: Uom
    ): SkuSpecificationRecord = SkuSpecificationRecord().apply {
        this.id = id
        this.code = code
        this.name = name
        this.category = category
        this.uom = uom
        this.acceptableCodeLife = 0
        this.packaging = ""
        this.coolingType = ""
        this.market = "test-market"
    }

    private fun createCalculationRecord(dcCode: String, skuId: UUID): CalculationRecord =
        CalculationRecord().apply {
            this.dcCode = dcCode
            this.cskuId = skuId
            this.date = LocalDate.now()
            this.week = "2024-W01"
            this.openingStock = BigDecimal.ZERO
            this.demand = BigDecimal.TEN
            this.stockUpdate = BigDecimal.ZERO
            this.closingStock = BigDecimal.TEN
            this.netNeeds = BigDecimal.ZERO
            this.strategy = "TEST_STRATEGY"
        }

    private fun getIngredientDepletionUrl(url: String): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            this.application {
                configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                ingredientDepletionModule(
                    IngredientDepletionService(
                        IngredientDepletionRepositoryImpl(dsl as MetricsDSLContext),
                        dcConfigService
                    ),
                    timeOutInMillis
                )()
            }
            response = client.get(url) {
                this.addAuthHeader(authorEmail, authorName, jwtSecret)
            }
        }
        return response
    }
}
