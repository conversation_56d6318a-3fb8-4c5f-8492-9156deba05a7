package com.hellofresh.cif.api.ingredientdepletion

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.KotlinFeature
import com.fasterxml.jackson.module.kotlin.KotlinModule
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.api.AuthUtils.addAuthHeader
import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.calculation.generated.model.ErrorResponse
import com.hellofresh.cif.api.calculation.generated.model.IngredientDepletionResponse
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.api.schema.tables.records.CalculationRecord
import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import io.ktor.client.request.get
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpStatusCode
import io.ktor.server.testing.testApplication
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import kotlin.time.Duration
import kotlinx.coroutines.runBlocking

/**
 * Business logic tests for the Ingredient Depletion API that validate
 * specific business rules and edge cases according to requirements.
 */
class IngredientDepletionBusinessLogicTest : FunctionalTest() {

    private val jwtSecret = "testSecret"
    private val jwksURI = "https://test.com"
    private val authorName = "testAuthor"
    private val authorEmail = "<EMAIL>"
    private val timeOutInMillis = Duration.parse("PT5S")

    private val objectMapper = ObjectMapper()
        .registerModule(KotlinModule.Builder().configure(KotlinFeature.NullIsSameAsDefault, true).build())
        .findAndRegisterModules()

    @Test
    fun `should only return SKUs that exist in calculation table - validates join requirement`() {
        // Given - This test validates the memory requirement that the query should join 
        // calculation table with sku_specification to only return SKUs that exist in the calculation table
        val brand = "hellofresh"
        val dcCode = "GB01"
        val skuWithCalculation = UUID.randomUUID()
        val skuWithoutCalculation = UUID.randomUUID()

        // Insert both SKUs in specification table
        insertSkuRecords(
            createSkuRecord(skuWithCalculation, "SKU-WITH-CALC", "SKU with calculation", "Category1", Uom.UOM_UNIT),
            createSkuRecord(skuWithoutCalculation, "SKU-WITHOUT-CALC", "SKU without calculation", "Category2", Uom.UOM_KG)
        )

        // Only insert calculation for one SKU
        dsl.batchInsert(createCalculationRecord(dcCode, skuWithCalculation)).execute()

        runBlocking {
            // When
            val response = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=$brand&dcCodes=$dcCode")

            // Then
            assertEquals(HttpStatusCode.OK, response.status)
            val result = objectMapper.readValue<IngredientDepletionResponse>(response.bodyAsText())
            
            // Should only return the SKU that has a calculation
            assertEquals(1, result.ingredientSummary.size)
            assertEquals("SKU-WITH-CALC", result.ingredientSummary[0].skuCode)
            assertEquals("SKU with calculation", result.ingredientSummary[0].skuName)
        }
    }

    @Test
    fun `should respect brand filtering when no DC codes provided`() {
        // Given
        val hellofreshBrand = "hellofresh"
        val greenchefBrand = "greenchef"
        val everyplateBrand = "everyplate"
        val skuId = UUID.randomUUID()

        // Setup DC configurations with different brand support
        createDcConfig("GB01", "GB") { brands = arrayOf("hellofresh", "greenchef") }
        createDcConfig("DE01", "DACH") { brands = arrayOf("hellofresh") }
        createDcConfig("US01", "US") { brands = arrayOf("hellofresh", "greenchef", "everyplate") }
        createDcConfig("FR01", "FR") { brands = arrayOf("greenchef") }

        insertSkuRecords(createSkuRecord(skuId, "SKU001", "Test SKU", "Category", Uom.UOM_UNIT))

        // Insert calculations for all DCs
        dsl.batchInsert(
            createCalculationRecord("GB01", skuId),
            createCalculationRecord("DE01", skuId),
            createCalculationRecord("US01", skuId),
            createCalculationRecord("FR01", skuId)
        ).execute()

        runBlocking {
            // Test hellofresh brand - should get GB01, DE01, US01
            val hellofreshResponse = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=$hellofreshBrand")
            assertEquals(HttpStatusCode.OK, hellofreshResponse.status)
            val hellofreshResult = objectMapper.readValue<IngredientDepletionResponse>(hellofreshResponse.bodyAsText())
            assertEquals(3, hellofreshResult.ingredientSummary.size)
            val hellofreshSites = hellofreshResult.ingredientSummary.map { it.site }.toSet()
            assertEquals(setOf("GB01", "DE01", "US01"), hellofreshSites)

            // Test greenchef brand - should get GB01, US01, FR01
            val greenchefResponse = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=$greenchefBrand")
            assertEquals(HttpStatusCode.OK, greenchefResponse.status)
            val greenchefResult = objectMapper.readValue<IngredientDepletionResponse>(greenchefResponse.bodyAsText())
            assertEquals(3, greenchefResult.ingredientSummary.size)
            val greenchefSites = greenchefResult.ingredientSummary.map { it.site }.toSet()
            assertEquals(setOf("GB01", "US01", "FR01"), greenchefSites)

            // Test everyplate brand - should get only US01
            val everyplateResponse = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=$everyplateBrand")
            assertEquals(HttpStatusCode.OK, everyplateResponse.status)
            val everyplateResult = objectMapper.readValue<IngredientDepletionResponse>(everyplateResponse.bodyAsText())
            assertEquals(1, everyplateResult.ingredientSummary.size)
            assertEquals("US01", everyplateResult.ingredientSummary[0].site)
        }
    }

    @Test
    fun `should override brand filtering when DC codes are explicitly provided`() {
        // Given - When DC codes are provided, they should be used regardless of brand support
        val brand = "everyplate" // Only supported by US01
        val explicitDcCodes = "GB01,DE01" // These don't support everyplate
        val skuId = UUID.randomUUID()

        createDcConfig("GB01", "GB") { brands = arrayOf("hellofresh", "greenchef") }
        createDcConfig("DE01", "DACH") { brands = arrayOf("hellofresh") }
        createDcConfig("US01", "US") { brands = arrayOf("hellofresh", "greenchef", "everyplate") }

        insertSkuRecords(createSkuRecord(skuId, "SKU001", "Test SKU", "Category", Uom.UOM_UNIT))

        dsl.batchInsert(
            createCalculationRecord("GB01", skuId),
            createCalculationRecord("DE01", skuId),
            createCalculationRecord("US01", skuId)
        ).execute()

        runBlocking {
            // When
            val response = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=$brand&dcCodes=$explicitDcCodes")

            // Then - Should use explicit DC codes, not brand filtering
            assertEquals(HttpStatusCode.OK, response.status)
            val result = objectMapper.readValue<IngredientDepletionResponse>(response.bodyAsText())
            assertEquals(2, result.ingredientSummary.size)
            val sites = result.ingredientSummary.map { it.site }.toSet()
            assertEquals(setOf("GB01", "DE01"), sites)
            
            // Brand should still be set correctly in response
            result.ingredientSummary.forEach { ingredient ->
                assertEquals(brand, ingredient.brand)
            }
        }
    }

    @Test
    fun `should handle case-sensitive brand names correctly`() {
        // Given
        val skuId = UUID.randomUUID()
        createDcConfig("GB01", "GB") { brands = arrayOf("HelloFresh", "GreenChef") } // Mixed case

        insertSkuRecords(createSkuRecord(skuId, "SKU001", "Test SKU", "Category", Uom.UOM_UNIT))
        dsl.batchInsert(createCalculationRecord("GB01", skuId)).execute()

        runBlocking {
            // Test exact case match
            val exactCaseResponse = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=HelloFresh")
            assertEquals(HttpStatusCode.OK, exactCaseResponse.status)
            val exactCaseResult = objectMapper.readValue<IngredientDepletionResponse>(exactCaseResponse.bodyAsText())
            assertEquals(1, exactCaseResult.ingredientSummary.size)

            // Test different case - should not match (case-sensitive)
            val differentCaseResponse = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=hellofresh")
            assertEquals(HttpStatusCode.OK, differentCaseResponse.status)
            val differentCaseResult = objectMapper.readValue<IngredientDepletionResponse>(differentCaseResponse.bodyAsText())
            assertEquals(0, differentCaseResult.ingredientSummary.size)
        }
    }

    @Test
    fun `should validate required brand parameter with proper error message`() {
        runBlocking {
            // Test missing brand parameter
            val missingBrandResponse = getIngredientDepletionUrl("/api/v1/ingredient-depletion")
            assertEquals(HttpStatusCode.BadRequest, missingBrandResponse.status)
            val missingBrandBody = missingBrandResponse.bodyAsText()
            assertTrue(missingBrandBody.contains("Brand parameter is required"))

            // Test empty brand parameter
            val emptyBrandResponse = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=")
            assertEquals(HttpStatusCode.BadRequest, emptyBrandResponse.status)
            val emptyBrandBody = emptyBrandResponse.bodyAsText()
            assertTrue(emptyBrandBody.contains("Brand parameter is required"))
        }
    }

    @Test
    fun `should handle special characters in DC codes parameter`() {
        // Given
        val brand = "hellofresh"
        val skuId = UUID.randomUUID()

        insertSkuRecords(createSkuRecord(skuId, "SKU001", "Test SKU", "Category", Uom.UOM_UNIT))
        dsl.batchInsert(
            createCalculationRecord("GB01", skuId),
            createCalculationRecord("DE-01", skuId), // DC code with hyphen
            createCalculationRecord("US_01", skuId)  // DC code with underscore
        ).execute()

        runBlocking {
            // When
            val response = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=$brand&dcCodes=GB01,DE-01,US_01")

            // Then
            assertEquals(HttpStatusCode.OK, response.status)
            val result = objectMapper.readValue<IngredientDepletionResponse>(response.bodyAsText())
            assertEquals(3, result.ingredientSummary.size)
            val sites = result.ingredientSummary.map { it.site }.toSet()
            assertEquals(setOf("GB01", "DE-01", "US_01"), sites)
        }
    }

    @Test
    fun `should maintain data consistency across multiple requests`() {
        // Given
        val brand = "hellofresh"
        val dcCode = "GB01"
        val skuId = UUID.randomUUID()

        insertSkuRecords(createSkuRecord(skuId, "SKU001", "Test SKU", "Category", Uom.UOM_UNIT))
        dsl.batchInsert(createCalculationRecord(dcCode, skuId)).execute()

        runBlocking {
            // When - Make multiple requests
            val responses = (1..5).map {
                getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=$brand&dcCodes=$dcCode")
            }

            // Then - All responses should be identical
            responses.forEach { response ->
                assertEquals(HttpStatusCode.OK, response.status)
                val result = objectMapper.readValue<IngredientDepletionResponse>(response.bodyAsText())
                assertEquals(1, result.ingredientSummary.size)
                assertEquals("SKU001", result.ingredientSummary[0].skuCode)
                assertEquals(brand, result.ingredientSummary[0].brand)
                assertEquals(dcCode, result.ingredientSummary[0].site)
            }
        }
    }

    @Test
    fun `should handle duplicate DC codes in parameter`() {
        // Given
        val brand = "hellofresh"
        val dcCodes = "GB01,GB01,DE01,GB01" // Duplicate GB01
        val skuId = UUID.randomUUID()

        insertSkuRecords(createSkuRecord(skuId, "SKU001", "Test SKU", "Category", Uom.UOM_UNIT))
        dsl.batchInsert(
            createCalculationRecord("GB01", skuId),
            createCalculationRecord("DE01", skuId)
        ).execute()

        runBlocking {
            // When
            val response = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=$brand&dcCodes=$dcCodes")

            // Then - Should not return duplicates
            assertEquals(HttpStatusCode.OK, response.status)
            val result = objectMapper.readValue<IngredientDepletionResponse>(response.bodyAsText())
            assertEquals(2, result.ingredientSummary.size) // Should not have duplicates
            val sites = result.ingredientSummary.map { it.site }.toSet()
            assertEquals(setOf("GB01", "DE01"), sites)
        }
    }

    @Test
    fun `should preserve all required fields in response`() {
        // Given
        val brand = "hellofresh"
        val dcCode = "GB01"
        val skuId = UUID.randomUUID()

        insertSkuRecords(
            createSkuRecord(skuId, "CHICKEN-001", "Organic Chicken Breast 200g", "Protein", Uom.UOM_KG)
        )
        dsl.batchInsert(createCalculationRecord(dcCode, skuId)).execute()

        runBlocking {
            // When
            val response = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=$brand&dcCodes=$dcCode")

            // Then
            assertEquals(HttpStatusCode.OK, response.status)
            val result = objectMapper.readValue<IngredientDepletionResponse>(response.bodyAsText())
            assertEquals(1, result.ingredientSummary.size)

            val ingredient = result.ingredientSummary[0]
            
            // Verify all required fields are present and correct
            assertNotNull(ingredient.site)
            assertEquals(dcCode, ingredient.site)
            
            assertNotNull(ingredient.skuCode)
            assertEquals("CHICKEN-001", ingredient.skuCode)
            
            assertNotNull(ingredient.skuName)
            assertEquals("Organic Chicken Breast 200g", ingredient.skuName)
            
            assertNotNull(ingredient.category)
            assertEquals("Protein", ingredient.category)
            
            assertNotNull(ingredient.uom)
            assertEquals("UOM_KG", ingredient.uom)
            
            assertNotNull(ingredient.brand)
            assertEquals(brand, ingredient.brand)
        }
    }

    private fun createSkuRecord(
        id: UUID,
        code: String,
        name: String,
        category: String,
        uom: Uom
    ): SkuSpecificationRecord = SkuSpecificationRecord().apply {
        this.id = id
        this.code = code
        this.name = name
        this.category = category
        this.uom = uom
        this.acceptableCodeLife = 0
        this.packaging = ""
        this.coolingType = ""
        this.market = "test-market"
    }

    private fun createCalculationRecord(dcCode: String, skuId: UUID): CalculationRecord =
        CalculationRecord().apply {
            this.dcCode = dcCode
            this.cskuId = skuId
            this.date = LocalDate.now()
            this.week = "2024-W01"
            this.openingStock = BigDecimal.ZERO
            this.demand = BigDecimal.TEN
            this.stockUpdate = BigDecimal.ZERO
            this.closingStock = BigDecimal.TEN
            this.netNeeds = BigDecimal.ZERO
            this.strategy = "TEST_STRATEGY"
        }

    private fun getIngredientDepletionUrl(url: String): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            this.application {
                configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                ingredientDepletionModule(
                    IngredientDepletionService(
                        IngredientDepletionRepositoryImpl(dsl as MetricsDSLContext),
                        dcConfigService
                    ),
                    timeOutInMillis
                )()
            }
            response = client.get(url) {
                this.addAuthHeader(authorEmail, authorName, jwtSecret)
            }
        }
        return response
    }
}
