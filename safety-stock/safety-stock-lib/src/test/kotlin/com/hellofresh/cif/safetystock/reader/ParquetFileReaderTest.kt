package com.hellofresh.cif.safetystock.reader

import java.io.File
import java.nio.file.Files
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test

class ParquetFileReaderTest {

    @Test
    fun `returns accepted while processing file successfully`() {
        val reader = ParquetFileReader()
        val data = readFileContent("calculated_buffers/test_file.parquet")
        val records = reader.readParquetFromByteArray(data)

        assertEquals(8, records.size.toLong())

        val record = records.first()
        with(record) {
            assertEquals("PRO-10-135756-4", get("full_sku").toString())
            assertEquals("NJ", get("dc").toString())
            assertEquals("2025-W01", get("start_week").toString())
            assertEquals(0.1, get("total_buffer").toString().toDouble())
        }
    }

    private fun readFileContent(fileName: String): ByteArray =
        with(this::class.java.classLoader) {
            File(getResource(fileName)!!.toURI())
        }.let {
            Files.readAllBytes(it.toPath())
        }
}
