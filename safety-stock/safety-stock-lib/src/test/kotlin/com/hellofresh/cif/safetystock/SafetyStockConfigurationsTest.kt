package com.hellofresh.cif.safetystock

import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.safetystock.SafetyStockConfiguration.Companion.DEFAULT_RISK_MULTIPLIER
import com.hellofresh.cif.safetystock.model.SkuRiskRating.MEDIUM
import java.math.BigDecimal
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.UUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

private const val DC = "DC"

class SafetyStockConfigurationsTest {

    @Test
    fun `returns matching safety stock configuration for sku and week`() {
        val skuId = UUID.randomUUID()
        val productionWeek = ProductionWeek(LocalDate.now(ZoneOffset.UTC).plusWeeks(1), DayOfWeek.MONDAY)
        val riskMultiplier = BigDecimal.TEN
        val safetyStockConfigurations = SafetyStockConfigurations(
            listOf(
                SafetyStockConfiguration(
                    DC,
                    skuId,
                    productionWeek,
                    riskMultiplier,
                    MEDIUM
                ),
                SafetyStockConfiguration(
                    DC,
                    skuId,
                    productionWeek.plusWeeks(1),
                    DEFAULT_RISK_MULTIPLIER,
                    MEDIUM
                ),
            ),
        )
        val safetyStockConfiguration = safetyStockConfigurations.getConfiguration(DC, skuId, productionWeek)

        assertEquals(riskMultiplier, safetyStockConfiguration.riskMultiplier)
    }

    @Test
    fun `returns default safetyStock configuration for non existing sku`() {
        val safetyStockConfiguration =
            SafetyStockConfigurations(
                emptyList(),
            ).getConfiguration(
                DC,
                UUID.randomUUID(),
                ProductionWeek(LocalDate.now(ZoneOffset.UTC).plusWeeks(1), DayOfWeek.MONDAY),
            )
        Assertions.assertEquals(DEFAULT_RISK_MULTIPLIER, safetyStockConfiguration.riskMultiplier)
    }

    @Test
    fun `returns matching safetyStock configuration for sku and previous week`() {
        val skuId = UUID.randomUUID()
        val productionWeek = ProductionWeek(LocalDate.now(ZoneOffset.UTC).plusWeeks(1), DayOfWeek.MONDAY)
        val expectedRiskMultiplier = BigDecimal.TEN
        val safetyStockConfiguration = SafetyStockConfigurations(
            listOf(
                SafetyStockConfiguration(
                    DC,
                    skuId,
                    productionWeek.minusWeeks(1),
                    expectedRiskMultiplier,
                    MEDIUM
                ),
                SafetyStockConfiguration(
                    DC,
                    skuId,
                    productionWeek.plusWeeks(3),
                    BigDecimal.TEN,
                    MEDIUM
                ),
            ),
        ).getConfiguration(DC, skuId, productionWeek)

        assertEquals(expectedRiskMultiplier, safetyStockConfiguration.riskMultiplier)
    }

    @Test
    fun `returns default safetyStock configuration if there is no week match (current and past weeks)`() {
        val skuId = UUID.randomUUID()
        val productionWeek = ProductionWeek(LocalDate.now(ZoneOffset.UTC), DayOfWeek.MONDAY)

        val safetyStockConfiguration = SafetyStockConfigurations(
            listOf(
                SafetyStockConfiguration(
                    DC,
                    skuId,
                    productionWeek.plusWeeks(3),
                    DEFAULT_RISK_MULTIPLIER.plus(BigDecimal.TWO),
                    MEDIUM
                ),
            ),
        ).getConfiguration(DC, skuId, productionWeek)

        assertEquals(DEFAULT_RISK_MULTIPLIER, safetyStockConfiguration.riskMultiplier)
    }
}
