output_resources:
  - label: distribution_center_output
    sql_raw:
      max_in_flight: 1
      driver: postgres
      dsn: postgres://${HF_INVENTORY_DB_USERNAME}:${HF_INVENTORY_DB_PASSWORD}@${DB_HOST}:5432/${DB_SCHEMA}
      query: >-
        INSERT INTO dc_config
        (dc_code,
        market,
        production_start,
        cleardown,
        has_cleardown,
        zone_id,
        enabled,
        record_timestamp,
        scheduled_cleardown_time,
        wms_type,
        po_cutoff_time,
        brands
        )
        VALUES(TRIM(BOTH '"' FROM $1), $2, $3, $4, $5, $6, $7, to_timestamp($8), $9::time, $10, $11, ARRAY(SELECT jsonb_array_elements_text($12::jsonb)))
        ON CONFLICT (dc_code)
        DO UPDATE SET
        (dc_code,
         market,
         production_start,
         cleardown,
         has_cleardown,
         zone_id,
         enabled,
         record_timestamp,
         scheduled_cleardown_time,
         wms_type,
         po_cutoff_time,
         brands
        )
        = (TRIM(BOTH '"' FROM $1), $2, $3, $4, $5, $6, $7, to_timestamp($8), $9::time, $10, $11, ARRAY(SELECT jsonb_array_elements_text($12::jsonb)))
        WHERE dc_config.record_timestamp <= to_timestamp($8)
      args_mapping: |
        root = [
          meta("kafka_key"),
          json("value.market"),
          json("value.production_start") ,
          json("value.cleardown"),
          json("value.has_cleardown"),
          json("value.zone_id"),
          json("value.enabled"),
          json("record_timestamp"),
          json("value.scheduled_clear_down_time"),
          json("value.wms_type"),
          if json("value.po_cutoff_time") == "null" { null } else { json("value.po_cutoff_time") },
          json("value.brands").string()
        ]
