---
environment: '@tier@'
tribe: '@tribe@'
squad: '@squad@'
# Deployment experiment, see build.gradle.kts
tag: '@dockerTag@'
fullnameOverride: '@applicationId@'
slack: '@slackAlertChannel@-@tier@'

vaultNamespace: services/@projectName@

deployments:
  distribution-center:
    replicaCount: 1
    containerPorts:
      http: 8080
    repository: '@dockerRepository@'
    pullPolicy: IfNotPresent
    command: [ "sh" ]
    args:
      - -c
      - echo "$HF_KAFKA_SSL_CA_PEM" > /tmp/ca.crt && exec ./benthos -r "/etc/benthos/benthos-resources/*resources.yaml" -c "/etc/benthos/benthos.yaml"
    livenessProbe:
      httpGet:
        path: /ping
        port: http
    readinessProbe:
      httpGet:
        path: /ready
        port: http
    resources:
      requests:
        memory: '400Mi'
        cpu: '200m'
      limits:
        memory: '1Gi'
        cpu: '500m'
    nodeSelector: { }
    tolerations: [ ]
    affinity: { }
    podAnnotations: { }
    env:
      APP_NAME: '@applicationName@-distribution-center'
      APP_VERSION: '6'
      RESOURCE_NAME: 'distribution_center'
      PROCESSOR_NAME: 'common'
      SOURCE_TOPIC: 'csku-inventory-forecast.intermediate.distribution-center'
      SOURCE_TOPIC_VERSION: '@csku-inventory-forecast.intermediate.distribution-center@'
      METRICS_PREFIX: '@projectKey@<EMAIL>@_distribution_center'
      DB_HOST: 'vault:@tier@/key-value/data/inventory#DB_MASTER_HOST'
      HF_INVENTORY_DB_PASSWORD: 'vault:@tier@/key-value/data/inventory#DB_PASSWORD'
      HF_INVENTORY_DB_USERNAME: 'vault:@tier@/key-value/data/inventory#DB_USERNAME'
      HF_KAFKA_SASL_PASSWORD: 'vault:@tier@/key-value/data/kafka/csku-inventory-forecast#password'
      HF_KAFKA_BOOTSTRAP_SERVERS: 'vault:@tier@/key-value/data/kafka#KAFKA_BOOTSTRAP_SERVERS'
      HF_KAFKA_SSL_CA_PEM: 'vault:@tier@/key-value/data/kafka/csku-inventory-forecast#ca'

    hpa:
      enabled: false
    spotInstance:
      preferred: true

services:
  distribution-center:
    enablePrometheus: true
    metricPortName: 'http'
    metricPath: '/metrics'
    enabled: true
    type: ClusterIP
    ports:
      http: 8080

  expected-inbound:
    enablePrometheus: true
    metricPortName: 'http'
    metricPath: '/metrics'
    enabled: true
    type: ClusterIP
    ports:
      http: 8080

  inventory:
    enablePrometheus: true
    metricPortName: 'http'
    metricPath: '/metrics'
    enabled: true
    type: ClusterIP
    ports:
      http: 8080

configMap:
  HF_TIER: '@tier@'
  APP_VERSION: '1'
  BENTHOS_PORT: '8080'
  KAFKA_USERNAME: '@projectName@'
  HF_KAFKA_SASL_MECHANISM: 'PLAIN'
  DB_HOST: 'inventory-db000.@<EMAIL>'
  DB_PORT: '5432'
  DB_SCHEMA: 'inventory'

